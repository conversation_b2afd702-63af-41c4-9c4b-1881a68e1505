import React from "react";
import ScopingAISidebar from "./ScopingAISidebar";

interface ScopingAILayoutProps {
  children: React.ReactNode;
}

export default function ScopingAILayout({ children }: ScopingAILayoutProps) {
  return (
    <div className="flex h-screen bg-gray-50">
      <ScopingAISidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <main className="flex-1 overflow-y-auto">{children}</main>
      </div>
    </div>
  );
}
