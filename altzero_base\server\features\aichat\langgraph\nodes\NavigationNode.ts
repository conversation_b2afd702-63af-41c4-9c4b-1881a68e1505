// =====================================================
// NAVIGATION NODE - AI CHAT WORKFLOW
// =====================================================

import { BaseNode, NavigationNodeResult } from '../types/NodeTypes';
import { WorkflowContext, NavigationAction } from '../types/WorkflowState';

export class NavigationNode implements BaseNode {
  name = 'navigation';
  description = 'Handles navigation requests and triggers page changes';

  async execute(context: WorkflowContext): Promise<any> {
    const { state, tools, logger } = context;
    const startTime = Date.now();

    try {
      logger.info('Starting navigation processing', {
        workflow_id: state.workflow_id,
        current_intent: state.current_intent,
        detected_entities: state.detected_entities
      });

      // Check if this is a navigation intent
      if (state.current_intent !== 'navigation') {
        logger.debug('Skipping navigation - intent is not navigation');
        return {
          navigation_actions: state.navigation_actions,
          processing_time: state.processing_time,
          last_updated: new Date().toISOString()
        };
      }

      // Extract target page from entities or analyze message
      const targetPage = this.extractTargetPage(state);
      
      if (!targetPage) {
        logger.warn('No target page found for navigation');
        return this.createNavigationError('No target page specified', state, startTime);
      }

      // Validate the target page
      const isValidPage = tools.navigation.validatePage(targetPage);
      if (!isValidPage) {
        logger.warn('Invalid target page:', { targetPage });
        return this.createNavigationError(`Invalid page: ${targetPage}`, state, startTime);
      }

      // Get the target URL
      const targetUrl = tools.navigation.getPageUrl(targetPage);
      logger.info('Navigation target determined:', { targetPage, targetUrl });

      // Trigger the navigation
      const navigationSuccess = await tools.navigation.triggerNavigation(targetPage, state.user_id);
      
      const executionTime = Date.now() - startTime;

      // Create navigation action record
      const navigationAction: NavigationAction = {
        page: targetPage,
        url: targetUrl,
        timestamp: new Date().toISOString(),
        success: navigationSuccess,
        error: navigationSuccess ? undefined : 'Navigation trigger failed'
      };

      const result: NavigationNodeResult = {
        navigation_triggered: true,
        target_page: targetPage,
        target_url: targetUrl,
        success: navigationSuccess,
        execution_time: executionTime
      };

      if (navigationSuccess) {
        logger.info('Navigation completed successfully', {
          target_page: targetPage,
          target_url: targetUrl,
          execution_time: executionTime
        });

        return {
          navigation_actions: [...state.navigation_actions, navigationAction],
          ai_response: {
            ...state.ai_response,
            content: `I've navigated you to the ${targetPage} page. You should see the page change now.`,
            confidence: 0.95,
            reasoning: `Successfully triggered navigation to ${targetPage}`,
            suggested_actions: [`Explore the ${targetPage} features`, 'Ask me if you need help with anything else']
          },
          node_data: {
            ...state.node_data,
            navigation: result
          },
          processing_time: (state.processing_time || 0) + executionTime,
          last_updated: new Date().toISOString()
        };
      } else {
        logger.error('Navigation failed', { targetPage, targetUrl });
        return this.createNavigationError('Navigation trigger failed', state, startTime, navigationAction);
      }

    } catch (error) {
      const executionTime = Date.now() - startTime;
      logger.error('Navigation processing failed', error);

      return {
        navigation_actions: state.navigation_actions,
        errors: [
          ...state.errors,
          {
            node_name: this.name,
            error_message: error instanceof Error ? error.message : 'Unknown navigation error',
            error_code: 'NAVIGATION_PROCESSING_FAILED',
            timestamp: new Date().toISOString(),
            recoverable: true
          }
        ],
        processing_time: (state.processing_time || 0) + executionTime,
        last_updated: new Date().toISOString()
      };
    }
  }

  // Extract target page from state entities or messages
  private extractTargetPage(state: any): string | null {
    // First, check detected entities
    if (state.detected_entities?.page_name) {
      return state.detected_entities.page_name;
    }

    // If no entity, analyze the latest user message
    const userMessages = state.messages.filter((msg: any) => msg.role === 'user');
    if (userMessages.length === 0) {
      return null;
    }

    const latestMessage = userMessages[userMessages.length - 1].content.toLowerCase();

    // Define page mapping with various keywords
    const pageKeywords = {
      dashboard: ['dashboard', 'home', 'main', 'overview'],
      knowledge: ['knowledge', 'knowledge base', 'documents', 'docs', 'kb'],
      teams: ['teams', 'team', 'members', 'people'],
      settings: ['settings', 'preferences', 'config', 'configuration'],
      profile: ['profile', 'account', 'user profile'],
      pseo: ['pseo', 'seo', 'search optimization'],
      scopingai: ['scopingai', 'scoping', 'ai scoping', 'proposal'],
      crm: ['crm', 'customer', 'contacts', 'leads'],
      'ai-chat': ['chat', 'ai chat', 'assistant', 'ai assistant']
    };

    // Find matching page
    for (const [page, keywords] of Object.entries(pageKeywords)) {
      for (const keyword of keywords) {
        if (latestMessage.includes(keyword)) {
          return page;
        }
      }
    }

    return null;
  }

  // Create navigation error response
  private createNavigationError(
    errorMessage: string, 
    state: any, 
    startTime: number, 
    navigationAction?: NavigationAction
  ): any {
    const executionTime = Date.now() - startTime;
    
    const actions = navigationAction 
      ? [...state.navigation_actions, navigationAction]
      : state.navigation_actions;

    return {
      navigation_actions: actions,
      ai_response: {
        ...state.ai_response,
        content: this.generateErrorResponse(errorMessage),
        confidence: 0.8,
        reasoning: `Navigation failed: ${errorMessage}`,
        suggested_actions: this.generateErrorSuggestions()
      },
      errors: [
        ...state.errors,
        {
          node_name: this.name,
          error_message: errorMessage,
          error_code: 'NAVIGATION_ERROR',
          timestamp: new Date().toISOString(),
          recoverable: true
        }
      ],
      processing_time: (state.processing_time || 0) + executionTime,
      last_updated: new Date().toISOString()
    };
  }

  // Generate user-friendly error response
  private generateErrorResponse(errorMessage: string): string {
    if (errorMessage.includes('No target page')) {
      return "I'd be happy to help you navigate! Could you please specify which page you'd like to go to? Available pages include: Dashboard, Knowledge Base, Teams, Settings, PSEO, ScopingAI, and CRM.";
    }
    
    if (errorMessage.includes('Invalid page')) {
      return "I'm sorry, but I don't recognize that page name. Available pages are: Dashboard, Knowledge Base, Teams, Settings, PSEO, ScopingAI, and CRM. Which one would you like to visit?";
    }
    
    return "I encountered an issue with navigation. Please try again or let me know which specific page you'd like to visit.";
  }

  // Generate error recovery suggestions
  private generateErrorSuggestions(): string[] {
    return [
      'Try saying "Go to dashboard" or "Navigate to settings"',
      'Ask me "What pages are available?"',
      'Use specific page names like "CRM", "Knowledge Base", or "Teams"'
    ];
  }

  // Validate navigation request
  private validateNavigationRequest(state: any): { valid: boolean; error?: string } {
    if (!state.user_id) {
      return { valid: false, error: 'User ID is required for navigation' };
    }

    if (state.current_intent !== 'navigation') {
      return { valid: false, error: 'Intent must be navigation' };
    }

    return { valid: true };
  }

  // Get available pages list
  private getAvailablePages(): string[] {
    return [
      'dashboard',
      'knowledge',
      'teams', 
      'settings',
      'profile',
      'pseo',
      'scopingai',
      'crm',
      'ai-chat'
    ];
  }

  // Check if page requires special permissions
  private checkPagePermissions(page: string, userId: string): boolean {
    // In a real implementation, this would check user permissions
    // For now, all pages are accessible
    return true;
  }

  // Log navigation analytics
  private logNavigationAnalytics(
    userId: string, 
    fromPage: string, 
    toPage: string, 
    success: boolean
  ): void {
    // In a real implementation, this would log to analytics service
    console.log('📊 Navigation Analytics:', {
      user_id: userId,
      from_page: fromPage,
      to_page: toPage,
      success,
      timestamp: new Date().toISOString()
    });
  }
}
