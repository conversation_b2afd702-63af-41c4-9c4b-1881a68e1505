{"name": "altzero", "version": "0.1.0", "private": true, "scripts": {"dev:frontend": "vite --port 5173 --no-open", "dev:server": "cd server && nodemon", "dev": "concurrently \"npm run dev:frontend\" \"npm run dev:server\"", "build:frontend": "tsc && vite build", "build:server": "tsc -p server/tsconfig.json", "build": "npm run build:frontend && npm run build:server", "preview": "vite preview", "start:frontend": "vite preview --port 5173", "start:server": "node --max-old-space-size=8192 dist/server/app.js", "start:langgraph-studio": "npx @langchain/langgraph-cli dev --port 8123 --no-pull", "start": "concurrently \"npm run start:frontend\" \"npm run start:server\"", "lint:frontend": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:server": "eslint server --ext .ts"}, "dependencies": {"@copilotkit/backend": "^0.37.0", "@copilotkit/react-core": "^1.8.13", "@copilotkit/react-textarea": "^1.8.13", "@copilotkit/react-ui": "^1.8.13", "@copilotkit/runtime": "^1.9.1", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.0.1", "@langchain/community": "^0.3.44", "@langchain/core": "^0.3.57", "@langchain/langgraph": "^0.3.3", "@langchain/openai": "^0.5.11", "@langchain/pinecone": "^0.2.0", "@llamaindex/cloud": "^4.0.11", "@pinecone-database/pinecone": "^5.0.2", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/supabase-js": "^2.38.4", "@types/jsdom": "^21.1.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "docx": "^9.5.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-fileupload": "^1.4.2", "express-session": "^1.17.3", "file-saver": "^2.0.5", "form-data": "^4.0.2", "framer-motion": "^11.18.2", "google-auth-library": "^9.15.1", "googleapis": "^126.0.1", "html2pdf.js": "^0.10.3", "langchain": "^0.3.27", "llamaindex": "^0.3.17", "lucide-react": "^0.509.0", "marked": "^15.0.12", "multer": "^1.4.5-lts.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.56.4", "react-markdown": "^9.0.1", "react-router-dom": "^6.18.0", "react-syntax-highlighter": "^15.5.0", "tailwind-merge": "^3.2.0", "tailwindcss": "^3.3.5", "uuid": "^11.1.0", "zod": "^3.25.17"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.12.1", "@types/cors": "^2.8.15", "@types/express": "^4.17.20", "@types/express-fileupload": "^1.4.4", "@types/express-session": "^1.17.9", "@types/multer": "^1.4.11", "@types/node": "^20.8.10", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/react-syntax-highlighter": "^15.5.11", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^6.9.1", "@typescript-eslint/parser": "^6.9.1", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.16", "concurrently": "^8.2.2", "eslint": "^8.52.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "nodemon": "^3.0.1", "postcss": "^8.4.31", "ts-node": "^10.9.1", "typescript": "^5.2.2", "vite": "^4.4.5", "ws": "^8.18.2"}}