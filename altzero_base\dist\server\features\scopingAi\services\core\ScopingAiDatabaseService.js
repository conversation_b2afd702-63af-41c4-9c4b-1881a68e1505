"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.scopingAiDbService = exports.ScopingAiDatabaseService = void 0;
const supabase_1 = require("../../../../base/common/apps/supabase");
class ScopingAiDatabaseService {
    // =====================================================
    // PROPOSAL MANAGEMENT
    // =====================================================
    async createProposal(data) {
        try {
            const { data: proposal, error } = await supabase_1.supabase
                .from('scopingai_proposals')
                .insert({
                user_id: data.userId,
                client_id: data.clientId,
                title: data.title,
                description: data.description,
                status: data.status || 'draft',
                template_id: data.templateId,
                metadata: data.metadata || {},
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            })
                .select('id')
                .single();
            if (error) {
                throw new Error(`Failed to create proposal: ${error.message}`);
            }
            return proposal.id;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`Unexpected error creating proposal: ${errorMessage}`);
        }
    }
    async updateProposal(proposalId, updates) {
        try {
            const updateData = {
                updated_at: new Date().toISOString()
            };
            if (updates.title)
                updateData.title = updates.title;
            if (updates.description)
                updateData.description = updates.description;
            if (updates.status)
                updateData.status = updates.status;
            if (updates.content)
                updateData.content = updates.content;
            if (updates.metadata)
                updateData.metadata = updates.metadata;
            const { error } = await supabase_1.supabase
                .from('scopingai_proposals')
                .update(updateData)
                .eq('id', proposalId);
            if (error) {
                throw new Error(`Failed to update proposal: ${error.message}`);
            }
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`Unexpected error updating proposal: ${errorMessage}`);
        }
    }
    async getProposalById(proposalId) {
        try {
            const { data, error } = await supabase_1.supabase
                .from('scopingai_proposals')
                .select(`
          *,
          scopingai_clients (
            id,
            name,
            contact_person,
            email,
            company
          )
        `)
                .eq('id', proposalId)
                .single();
            if (error && error.code !== 'PGRST116') {
                throw new Error(`Failed to fetch proposal: ${error.message}`);
            }
            return data;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`Unexpected error fetching proposal: ${errorMessage}`);
        }
    }
    async getUserProposals(userId, options) {
        try {
            let query = supabase_1.supabase
                .from('scopingai_proposals')
                .select(`
          *,
          scopingai_clients (
            id,
            name,
            contact_person,
            email,
            company
          )
        `)
                .eq('user_id', userId)
                .order('updated_at', { ascending: false });
            if (options?.status) {
                query = query.eq('status', options.status);
            }
            if (options?.limit) {
                query = query.limit(options.limit);
            }
            if (options?.offset) {
                query = query.range(options.offset, (options.offset + (options.limit || 10)) - 1);
            }
            const { data, error } = await query;
            if (error) {
                throw new Error(`Failed to fetch user proposals: ${error.message}`);
            }
            return data || [];
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`Unexpected error fetching user proposals: ${errorMessage}`);
        }
    }
    // =====================================================
    // CLIENT MANAGEMENT
    // =====================================================
    async createClient(data) {
        try {
            const { data: client, error } = await supabase_1.supabase
                .from('scopingai_clients')
                .insert({
                user_id: data.userId,
                name: data.name,
                contact_person: data.contactPerson,
                email: data.email,
                phone: data.phone,
                company: data.company,
                industry: data.industry,
                metadata: data.metadata || {},
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            })
                .select('id')
                .single();
            if (error) {
                throw new Error(`Failed to create client: ${error.message}`);
            }
            return client.id;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`Unexpected error creating client: ${errorMessage}`);
        }
    }
    async getClientById(clientId) {
        try {
            const { data, error } = await supabase_1.supabase
                .from('scopingai_clients')
                .select('*')
                .eq('id', clientId)
                .single();
            if (error && error.code !== 'PGRST116') {
                throw new Error(`Failed to fetch client: ${error.message}`);
            }
            return data;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`Unexpected error fetching client: ${errorMessage}`);
        }
    }
    async getUserClients(userId) {
        try {
            const { data, error } = await supabase_1.supabase
                .from('scopingai_clients')
                .select('*')
                .eq('user_id', userId)
                .order('name', { ascending: true });
            if (error) {
                throw new Error(`Failed to fetch user clients: ${error.message}`);
            }
            return data || [];
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`Unexpected error fetching user clients: ${errorMessage}`);
        }
    }
    // =====================================================
    // TEMPLATE MANAGEMENT
    // =====================================================
    async getProposalTemplates(userId) {
        try {
            let query = supabase_1.supabase
                .from('scopingai_proposal_templates')
                .select('*')
                .order('name', { ascending: true });
            // Get both public templates and user-specific templates
            if (userId) {
                query = query.or(`is_public.eq.true,user_id.eq.${userId}`);
            }
            else {
                query = query.eq('is_public', true);
            }
            const { data, error } = await query;
            if (error) {
                throw new Error(`Failed to fetch proposal templates: ${error.message}`);
            }
            return data || [];
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`Unexpected error fetching proposal templates: ${errorMessage}`);
        }
    }
    // =====================================================
    // DOCUMENT MANAGEMENT
    // =====================================================
    async saveGeneratedDocument(data) {
        try {
            const { data: document, error } = await supabase_1.supabase
                .from('scopingai_documents')
                .insert({
                proposal_id: data.proposalId,
                user_id: data.userId,
                title: data.title,
                content: data.content,
                metadata: data.metadata || {},
                generation_settings: data.generationSettings || {},
                status: 'generated',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            })
                .select('id')
                .single();
            if (error) {
                throw new Error(`Failed to save generated document: ${error.message}`);
            }
            return document.id;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`Unexpected error saving generated document: ${errorMessage}`);
        }
    }
    // =====================================================
    // UTILITY METHODS
    // =====================================================
    async healthCheck() {
        try {
            const { error } = await supabase_1.supabase
                .from('scopingai_clients')
                .select('id')
                .limit(1);
            return !error;
        }
        catch (error) {
            return false;
        }
    }
}
exports.ScopingAiDatabaseService = ScopingAiDatabaseService;
exports.scopingAiDbService = new ScopingAiDatabaseService();
//# sourceMappingURL=ScopingAiDatabaseService.js.map