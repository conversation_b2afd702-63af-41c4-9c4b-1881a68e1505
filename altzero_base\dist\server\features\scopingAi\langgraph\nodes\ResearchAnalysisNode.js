"use strict";
// =====================================================
// RESEARCH ANALYSIS NODE - SCOPINGAI LANGGRAPH
// =====================================================
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResearchAnalysisNode = void 0;
class ResearchAnalysisNode {
    constructor() {
        this.name = 'research_analysis';
        this.description = 'Performs comprehensive research analysis combining knowledge base content and client analysis';
    }
    async execute(context) {
        const { state, tools, logger } = context;
        logger.info('Starting research analysis', {
            workflow_id: state.workflow_id,
            has_knowledge_content: !!(state.knowledge_base_content?.relevant_content),
            has_client_analysis: !!(state.client_analysis)
        });
        try {
            const startTime = Date.now();
            // Perform comprehensive research analysis
            const analysisResult = await this.performResearchAnalysis(state, tools, logger);
            const processingTime = Date.now() - startTime;
            logger.info('Research analysis completed', {
                content_length: analysisResult.content.length,
                key_findings: analysisResult.key_findings.length,
                recommendations: analysisResult.recommendations.length,
                confidence: analysisResult.confidence_score
            });
            return {
                research_analysis: {
                    content: analysisResult.content,
                    key_findings: analysisResult.key_findings,
                    recommendations: analysisResult.recommendations,
                    data_sources: analysisResult.data_sources
                },
                current_step: 'research_analysis_completed',
                progress: 50,
                processing_time: (state.processing_time || 0) + processingTime,
                api_calls_made: [
                    ...(state.api_calls_made || []),
                    {
                        provider: 'ai_research',
                        endpoint: 'comprehensive_analysis',
                        calls_made: 1,
                        success_rate: 1.0,
                        average_response_time: processingTime,
                        cost_estimate: 0.25,
                        timestamp: new Date().toISOString()
                    }
                ],
                last_updated: new Date().toISOString(),
                node_data: {
                    ...state.node_data,
                    research_analysis: analysisResult
                }
            };
        }
        catch (error) {
            logger.error('Research analysis failed', error);
            throw error;
        }
    }
    async performResearchAnalysis(state, tools, logger) {
        // Gather all available data sources
        const dataSources = this.gatherDataSources(state);
        // Create comprehensive research prompt
        const researchPrompt = this.createResearchPrompt(state, dataSources);
        // Generate research analysis
        const analysisContent = await tools.ai.generateText(researchPrompt, {
            model: 'gpt-4o-mini',
            temperature: 0.7,
            max_tokens: 3000
        });
        // Extract key findings
        const keyFindings = await this.extractKeyFindings(state, analysisContent, tools, logger);
        // Generate strategic recommendations
        const recommendations = await this.generateRecommendations(state, analysisContent, tools, logger);
        // Calculate confidence score
        const confidenceScore = this.calculateConfidenceScore(state, dataSources);
        return {
            content: analysisContent,
            key_findings: keyFindings,
            recommendations: recommendations,
            data_sources: dataSources,
            analysis_depth: this.calculateAnalysisDepth(analysisContent),
            confidence_score: confidenceScore
        };
    }
    gatherDataSources(state) {
        const sources = [];
        if (state.knowledge_base_content?.retrieved_documents?.length) {
            sources.push(`Knowledge Base (${state.knowledge_base_content.retrieved_documents.length} documents)`);
        }
        if (state.client_analysis?.industry_insights?.length) {
            sources.push('Industry Analysis');
        }
        if (state.client_analysis?.competitive_landscape?.length) {
            sources.push('Competitive Analysis');
        }
        if (state.client?.industry) {
            sources.push('Client Industry Context');
        }
        if (state.project?.description) {
            sources.push('Project Requirements');
        }
        return sources;
    }
    createResearchPrompt(state, dataSources) {
        const knowledgeContent = state.knowledge_base_content?.relevant_content || '';
        const clientAnalysis = state.client_analysis;
        const client = state.client;
        const project = state.project;
        return `You are a senior business analyst creating a comprehensive research foundation for a client proposal.

CLIENT & PROJECT OVERVIEW:
- Client Name: ${client?.name || 'Unknown'}
- Industry: ${client?.industry || 'Unknown'}
- Company Size: ${client?.size || 'Unknown'}
- Location: ${client?.location || 'Unknown'}
- Project Title: ${project?.title || 'Unknown'}
- Project Description: ${project?.description || 'No description provided'}
- Project Requirements: ${JSON.stringify(project?.requirements || {})}

${clientAnalysis ? `
CLIENT ANALYSIS INSIGHTS:
- Market Position: ${clientAnalysis.market_position}
- Key Challenges: ${clientAnalysis.key_challenges?.join(', ') || 'None identified'}
- Opportunities: ${clientAnalysis.opportunities?.join(', ') || 'None identified'}
- Industry Insights: ${clientAnalysis.industry_insights?.map(i => i.insight).join('; ') || 'None available'}
- Competitive Landscape: ${clientAnalysis.competitive_landscape?.length || 0} competitors analyzed
` : ''}

${knowledgeContent ? `
REFERENCE MATERIALS & KNOWLEDGE BASE:
${knowledgeContent}

INSTRUCTIONS FOR REFERENCE MATERIAL USAGE:
- Analyze the reference documents for relevant methodologies, best practices, and technical specifications
- Extract case studies, success metrics, and proven approaches that apply to this client's needs
- Identify industry-specific insights and compliance requirements
- Note any relevant cost models, timelines, or resource requirements from the reference materials
- Use reference data to validate and enhance your recommendations
` : ''}

RESEARCH OBJECTIVES:
1. Conduct thorough analysis of the client's industry and specific needs
2. Identify key challenges and opportunities for this project
3. Research industry best practices and standards relevant to the project
4. Analyze competitive landscape and market trends affecting the client
5. Determine technical requirements and constraints for the proposed solution
6. Assess resource needs, timeline considerations, and potential risks
7. Identify success metrics and KPIs for the project
${knowledgeContent ? '8. Leverage reference materials to provide data-driven insights and proven methodologies' : ''}

ANALYSIS FRAMEWORK:
- Current State Analysis: What is the client's current situation?
- Future State Vision: What does success look like for this project?
- Gap Analysis: What needs to change to achieve the future state?
- Solution Approach: How can we bridge the gap effectively?
- Risk Assessment: What are the potential challenges and mitigation strategies?
- Success Metrics: How will we measure project success?

DATA SOURCES UTILIZED:
${dataSources.join(', ')}

DELIVERABLE:
Provide a comprehensive research analysis that will serve as the foundation for a professional client proposal. Focus on actionable insights, data-driven recommendations, and industry-specific considerations. The analysis should demonstrate deep understanding of the client's business context and position our solution as the optimal choice for achieving their objectives.

Structure your response with clear sections covering:
1. Executive Research Summary
2. Current State Assessment
3. Market & Industry Context
4. Technical & Operational Requirements
5. Risk & Opportunity Analysis
6. Strategic Recommendations
7. Success Framework & Metrics

Additional Context: ${state.ai_prompts?.additional_instructions || 'None provided'}`;
    }
    async extractKeyFindings(state, analysisContent, tools, logger) {
        try {
            const prompt = `Extract 5-8 key findings from the following research analysis. Focus on the most important insights that will drive the proposal strategy.

Research Analysis:
${analysisContent}

Provide the key findings as a JSON array of strings, each finding should be concise but specific.`;
            const response = await tools.ai.generateStructuredData(prompt, {
                type: 'array',
                items: { type: 'string' }
            });
            return Array.isArray(response) ? response : response.findings || [];
        }
        catch (error) {
            logger.warn('Key findings extraction failed, using fallback', error);
            return [
                'Client requires comprehensive solution addressing current market challenges',
                'Industry trends indicate strong demand for digital transformation',
                'Competitive landscape presents opportunities for differentiation',
                'Technical requirements align with modern best practices',
                'Success metrics should focus on measurable business outcomes'
            ];
        }
    }
    async generateRecommendations(state, analysisContent, tools, logger) {
        try {
            const prompt = `Based on the research analysis, generate 5-7 strategic recommendations for the proposal. These should be actionable and directly address the client's needs.

Research Analysis:
${analysisContent}

Client Context:
- Name: ${state.client?.name}
- Industry: ${state.client?.industry}
- Project: ${state.project?.description}

Provide recommendations as a JSON array of strings. Each recommendation should be specific, actionable, and valuable to the client.`;
            const response = await tools.ai.generateStructuredData(prompt, {
                type: 'array',
                items: { type: 'string' }
            });
            return Array.isArray(response) ? response : response.recommendations || [];
        }
        catch (error) {
            logger.warn('Recommendations generation failed, using fallback', error);
            return [
                'Implement phased approach to minimize risk and ensure steady progress',
                'Focus on quick wins to demonstrate early value and build momentum',
                'Establish clear governance structure and communication protocols',
                'Invest in change management and user adoption strategies',
                'Develop comprehensive testing and quality assurance processes',
                'Create detailed project timeline with realistic milestones',
                'Ensure proper documentation and knowledge transfer'
            ];
        }
    }
    calculateAnalysisDepth(content) {
        // Simple heuristic to measure analysis depth
        const wordCount = content.split(/\s+/).length;
        const sectionCount = (content.match(/\d+\./g) || []).length;
        const technicalTerms = (content.match(/\b(implementation|strategy|framework|methodology|analysis|assessment|optimization|integration)\b/gi) || []).length;
        let depth = 0;
        if (wordCount > 1000)
            depth += 30;
        if (wordCount > 2000)
            depth += 20;
        if (sectionCount > 5)
            depth += 20;
        if (technicalTerms > 10)
            depth += 30;
        return Math.min(depth, 100);
    }
    calculateConfidenceScore(state, dataSources) {
        let score = 40; // Base score
        // Increase confidence based on available data
        if (state.knowledge_base_content?.retrieved_documents?.length) {
            score += 20;
        }
        if (state.client_analysis?.industry_insights?.length) {
            score += 15;
        }
        if (state.client_analysis?.competitive_landscape?.length) {
            score += 10;
        }
        if (state.client?.industry && state.client.industry !== 'Unknown') {
            score += 10;
        }
        if (state.project?.description && state.project.description.length > 100) {
            score += 5;
        }
        return Math.min(score, 95);
    }
}
exports.ResearchAnalysisNode = ResearchAnalysisNode;
//# sourceMappingURL=ResearchAnalysisNode.js.map