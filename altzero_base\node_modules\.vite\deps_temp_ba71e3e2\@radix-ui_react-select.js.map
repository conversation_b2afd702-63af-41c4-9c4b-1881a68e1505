{"version": 3, "sources": ["../../@radix-ui/react-select/src/select.tsx", "../../@radix-ui/number/src/number.ts", "../../@radix-ui/react-select/node_modules/@radix-ui/react-primitive/src/primitive.tsx"], "sourcesContent": ["import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { clamp } from '@radix-ui/number';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { useId } from '@radix-ui/react-id';\nimport * as PopperPrimitive from '@radix-ui/react-popper';\nimport { createPopperScope } from '@radix-ui/react-popper';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { createSlot } from '@radix-ui/react-slot';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { VISUALLY_HIDDEN_STYLES } from '@radix-ui/react-visually-hidden';\nimport { hideOthers } from 'aria-hidden';\nimport { RemoveScroll } from 'react-remove-scroll';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\nconst OPEN_KEYS = [' ', 'Enter', 'ArrowUp', 'ArrowDown'];\nconst SELECTION_KEYS = [' ', 'Enter'];\n\n/* -------------------------------------------------------------------------------------------------\n * Select\n * -----------------------------------------------------------------------------------------------*/\n\nconst SELECT_NAME = 'Select';\n\ntype ItemData = { value: string; disabled: boolean; textValue: string };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  SelectItemElement,\n  ItemData\n>(SELECT_NAME);\n\ntype ScopedProps<P> = P & { __scopeSelect?: Scope };\nconst [createSelectContext, createSelectScope] = createContextScope(SELECT_NAME, [\n  createCollectionScope,\n  createPopperScope,\n]);\nconst usePopperScope = createPopperScope();\n\ntype SelectContextValue = {\n  trigger: SelectTriggerElement | null;\n  onTriggerChange(node: SelectTriggerElement | null): void;\n  valueNode: SelectValueElement | null;\n  onValueNodeChange(node: SelectValueElement): void;\n  valueNodeHasChildren: boolean;\n  onValueNodeHasChildrenChange(hasChildren: boolean): void;\n  contentId: string;\n  value: string | undefined;\n  onValueChange(value: string): void;\n  open: boolean;\n  required?: boolean;\n  onOpenChange(open: boolean): void;\n  dir: SelectProps['dir'];\n  triggerPointerDownPosRef: React.MutableRefObject<{ x: number; y: number } | null>;\n  disabled?: boolean;\n};\n\nconst [SelectProvider, useSelectContext] = createSelectContext<SelectContextValue>(SELECT_NAME);\n\ntype NativeOption = React.ReactElement<React.ComponentProps<'option'>>;\n\ntype SelectNativeOptionsContextValue = {\n  onNativeOptionAdd(option: NativeOption): void;\n  onNativeOptionRemove(option: NativeOption): void;\n};\nconst [SelectNativeOptionsProvider, useSelectNativeOptionsContext] =\n  createSelectContext<SelectNativeOptionsContextValue>(SELECT_NAME);\n\ninterface ControlledClearableSelectProps {\n  value: string | undefined;\n  defaultValue?: never;\n  onValueChange: (value: string | undefined) => void;\n}\n\ninterface ControlledUnclearableSelectProps {\n  value: string;\n  defaultValue?: never;\n  onValueChange: (value: string) => void;\n}\n\ninterface UncontrolledSelectProps {\n  value?: never;\n  defaultValue?: string;\n  onValueChange?: {\n    (value: string): void;\n    (value: string | undefined): void;\n  };\n}\n\ntype SelectControlProps =\n  | ControlledClearableSelectProps\n  | ControlledUnclearableSelectProps\n  | UncontrolledSelectProps;\n\ninterface SelectSharedProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  dir?: Direction;\n  name?: string;\n  autoComplete?: string;\n  disabled?: boolean;\n  required?: boolean;\n  form?: string;\n}\n\n// TODO: Should improve typing somewhat, but this would be a breaking change.\n// Consider using in the next major version (along with some testing to be sure\n// it works as expected and doesn't cause problems)\ntype _FutureSelectProps = SelectSharedProps & SelectControlProps;\n\ntype SelectProps = SelectSharedProps & {\n  value?: string;\n  defaultValue?: string;\n  onValueChange?(value: string): void;\n};\n\nconst Select: React.FC<SelectProps> = (props: ScopedProps<SelectProps>) => {\n  const {\n    __scopeSelect,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    value: valueProp,\n    defaultValue,\n    onValueChange,\n    dir,\n    name,\n    autoComplete,\n    disabled,\n    required,\n    form,\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n  const [trigger, setTrigger] = React.useState<SelectTriggerElement | null>(null);\n  const [valueNode, setValueNode] = React.useState<SelectValueElement | null>(null);\n  const [valueNodeHasChildren, setValueNodeHasChildren] = React.useState(false);\n  const direction = useDirection(dir);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: SELECT_NAME,\n  });\n  const [value, setValue] = useControllableState({\n    prop: valueProp,\n    defaultProp: defaultValue,\n    onChange: onValueChange as any,\n    caller: SELECT_NAME,\n  });\n  const triggerPointerDownPosRef = React.useRef<{ x: number; y: number } | null>(null);\n\n  // We set this to true by default so that events bubble to forms without JS (SSR)\n  const isFormControl = trigger ? form || !!trigger.closest('form') : true;\n  const [nativeOptionsSet, setNativeOptionsSet] = React.useState(new Set<NativeOption>());\n\n  // The native `select` only associates the correct default value if the corresponding\n  // `option` is rendered as a child **at the same time** as itself.\n  // Because it might take a few renders for our items to gather the information to build\n  // the native `option`(s), we generate a key on the `select` to make sure React re-builds it\n  // each time the options change.\n  const nativeSelectKey = Array.from(nativeOptionsSet)\n    .map((option) => option.props.value)\n    .join(';');\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <SelectProvider\n        required={required}\n        scope={__scopeSelect}\n        trigger={trigger}\n        onTriggerChange={setTrigger}\n        valueNode={valueNode}\n        onValueNodeChange={setValueNode}\n        valueNodeHasChildren={valueNodeHasChildren}\n        onValueNodeHasChildrenChange={setValueNodeHasChildren}\n        contentId={useId()}\n        value={value}\n        onValueChange={setValue}\n        open={open}\n        onOpenChange={setOpen}\n        dir={direction}\n        triggerPointerDownPosRef={triggerPointerDownPosRef}\n        disabled={disabled}\n      >\n        <Collection.Provider scope={__scopeSelect}>\n          <SelectNativeOptionsProvider\n            scope={props.__scopeSelect}\n            onNativeOptionAdd={React.useCallback((option) => {\n              setNativeOptionsSet((prev) => new Set(prev).add(option));\n            }, [])}\n            onNativeOptionRemove={React.useCallback((option) => {\n              setNativeOptionsSet((prev) => {\n                const optionsSet = new Set(prev);\n                optionsSet.delete(option);\n                return optionsSet;\n              });\n            }, [])}\n          >\n            {children}\n          </SelectNativeOptionsProvider>\n        </Collection.Provider>\n\n        {isFormControl ? (\n          <SelectBubbleInput\n            key={nativeSelectKey}\n            aria-hidden\n            required={required}\n            tabIndex={-1}\n            name={name}\n            autoComplete={autoComplete}\n            value={value}\n            // enable form autofill\n            onChange={(event) => setValue(event.target.value)}\n            disabled={disabled}\n            form={form}\n          >\n            {value === undefined ? <option value=\"\" /> : null}\n            {Array.from(nativeOptionsSet)}\n          </SelectBubbleInput>\n        ) : null}\n      </SelectProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nSelect.displayName = SELECT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'SelectTrigger';\n\ntype SelectTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface SelectTriggerProps extends PrimitiveButtonProps {}\n\nconst SelectTrigger = React.forwardRef<SelectTriggerElement, SelectTriggerProps>(\n  (props: ScopedProps<SelectTriggerProps>, forwardedRef) => {\n    const { __scopeSelect, disabled = false, ...triggerProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(TRIGGER_NAME, __scopeSelect);\n    const isDisabled = context.disabled || disabled;\n    const composedRefs = useComposedRefs(forwardedRef, context.onTriggerChange);\n    const getItems = useCollection(__scopeSelect);\n    const pointerTypeRef = React.useRef<React.PointerEvent['pointerType']>('touch');\n\n    const [searchRef, handleTypeaheadSearch, resetTypeahead] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.value === context.value);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem !== undefined) {\n        context.onValueChange(nextItem.value);\n      }\n    });\n\n    const handleOpen = (pointerEvent?: React.MouseEvent | React.PointerEvent) => {\n      if (!isDisabled) {\n        context.onOpenChange(true);\n        // reset typeahead when we open\n        resetTypeahead();\n      }\n\n      if (pointerEvent) {\n        context.triggerPointerDownPosRef.current = {\n          x: Math.round(pointerEvent.pageX),\n          y: Math.round(pointerEvent.pageY),\n        };\n      }\n    };\n\n    return (\n      <PopperPrimitive.Anchor asChild {...popperScope}>\n        <Primitive.button\n          type=\"button\"\n          role=\"combobox\"\n          aria-controls={context.contentId}\n          aria-expanded={context.open}\n          aria-required={context.required}\n          aria-autocomplete=\"none\"\n          dir={context.dir}\n          data-state={context.open ? 'open' : 'closed'}\n          disabled={isDisabled}\n          data-disabled={isDisabled ? '' : undefined}\n          data-placeholder={shouldShowPlaceholder(context.value) ? '' : undefined}\n          {...triggerProps}\n          ref={composedRefs}\n          // Enable compatibility with native label or custom `Label` \"click\" for Safari:\n          onClick={composeEventHandlers(triggerProps.onClick, (event) => {\n            // Whilst browsers generally have no issue focusing the trigger when clicking\n            // on a label, Safari seems to struggle with the fact that there's no `onClick`.\n            // We force `focus` in this case. Note: this doesn't create any other side-effect\n            // because we are preventing default in `onPointerDown` so effectively\n            // this only runs for a label \"click\"\n            event.currentTarget.focus();\n\n            // Open on click when using a touch or pen device\n            if (pointerTypeRef.current !== 'mouse') {\n              handleOpen(event);\n            }\n          })}\n          onPointerDown={composeEventHandlers(triggerProps.onPointerDown, (event) => {\n            pointerTypeRef.current = event.pointerType;\n\n            // prevent implicit pointer capture\n            // https://www.w3.org/TR/pointerevents3/#implicit-pointer-capture\n            const target = event.target as HTMLElement;\n            if (target.hasPointerCapture(event.pointerId)) {\n              target.releasePointerCapture(event.pointerId);\n            }\n\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click); also not for touch\n            // devices because that would open the menu on scroll. (pen devices behave as touch on iOS).\n            if (event.button === 0 && event.ctrlKey === false && event.pointerType === 'mouse') {\n              handleOpen(event);\n              // prevent trigger from stealing focus from the active item after opening.\n              event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(triggerProps.onKeyDown, (event) => {\n            const isTypingAhead = searchRef.current !== '';\n            const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n            if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n            if (isTypingAhead && event.key === ' ') return;\n            if (OPEN_KEYS.includes(event.key)) {\n              handleOpen();\n              event.preventDefault();\n            }\n          })}\n        />\n      </PopperPrimitive.Anchor>\n    );\n  }\n);\n\nSelectTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectValue\n * -----------------------------------------------------------------------------------------------*/\n\nconst VALUE_NAME = 'SelectValue';\n\ntype SelectValueElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface SelectValueProps extends Omit<PrimitiveSpanProps, 'placeholder'> {\n  placeholder?: React.ReactNode;\n}\n\nconst SelectValue = React.forwardRef<SelectValueElement, SelectValueProps>(\n  (props: ScopedProps<SelectValueProps>, forwardedRef) => {\n    // We ignore `className` and `style` as this part shouldn't be styled.\n    const { __scopeSelect, className, style, children, placeholder = '', ...valueProps } = props;\n    const context = useSelectContext(VALUE_NAME, __scopeSelect);\n    const { onValueNodeHasChildrenChange } = context;\n    const hasChildren = children !== undefined;\n    const composedRefs = useComposedRefs(forwardedRef, context.onValueNodeChange);\n\n    useLayoutEffect(() => {\n      onValueNodeHasChildrenChange(hasChildren);\n    }, [onValueNodeHasChildrenChange, hasChildren]);\n\n    return (\n      <Primitive.span\n        {...valueProps}\n        ref={composedRefs}\n        // we don't want events from the portalled `SelectValue` children to bubble\n        // through the item they came from\n        style={{ pointerEvents: 'none' }}\n      >\n        {shouldShowPlaceholder(context.value) ? <>{placeholder}</> : children}\n      </Primitive.span>\n    );\n  }\n);\n\nSelectValue.displayName = VALUE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectIcon\n * -----------------------------------------------------------------------------------------------*/\n\nconst ICON_NAME = 'SelectIcon';\n\ntype SelectIconElement = React.ComponentRef<typeof Primitive.span>;\ninterface SelectIconProps extends PrimitiveSpanProps {}\n\nconst SelectIcon = React.forwardRef<SelectIconElement, SelectIconProps>(\n  (props: ScopedProps<SelectIconProps>, forwardedRef) => {\n    const { __scopeSelect, children, ...iconProps } = props;\n    return (\n      <Primitive.span aria-hidden {...iconProps} ref={forwardedRef}>\n        {children || '▼'}\n      </Primitive.span>\n    );\n  }\n);\n\nSelectIcon.displayName = ICON_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'SelectPortal';\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface SelectPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n}\n\nconst SelectPortal: React.FC<SelectPortalProps> = (props: ScopedProps<SelectPortalProps>) => {\n  return <PortalPrimitive asChild {...props} />;\n};\n\nSelectPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'SelectContent';\n\ntype SelectContentElement = SelectContentImplElement;\ninterface SelectContentProps extends SelectContentImplProps {}\n\nconst SelectContent = React.forwardRef<SelectContentElement, SelectContentProps>(\n  (props: ScopedProps<SelectContentProps>, forwardedRef) => {\n    const context = useSelectContext(CONTENT_NAME, props.__scopeSelect);\n    const [fragment, setFragment] = React.useState<DocumentFragment>();\n\n    // setting the fragment in `useLayoutEffect` as `DocumentFragment` doesn't exist on the server\n    useLayoutEffect(() => {\n      setFragment(new DocumentFragment());\n    }, []);\n\n    if (!context.open) {\n      const frag = fragment as Element | undefined;\n      return frag\n        ? ReactDOM.createPortal(\n            <SelectContentProvider scope={props.__scopeSelect}>\n              <Collection.Slot scope={props.__scopeSelect}>\n                <div>{props.children}</div>\n              </Collection.Slot>\n            </SelectContentProvider>,\n            frag\n          )\n        : null;\n    }\n\n    return <SelectContentImpl {...props} ref={forwardedRef} />;\n  }\n);\n\nSelectContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectContentImpl\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_MARGIN = 10;\n\ntype SelectContentContextValue = {\n  content?: SelectContentElement | null;\n  viewport?: SelectViewportElement | null;\n  onViewportChange?: (node: SelectViewportElement | null) => void;\n  itemRefCallback?: (node: SelectItemElement | null, value: string, disabled: boolean) => void;\n  selectedItem?: SelectItemElement | null;\n  onItemLeave?: () => void;\n  itemTextRefCallback?: (\n    node: SelectItemTextElement | null,\n    value: string,\n    disabled: boolean\n  ) => void;\n  focusSelectedItem?: () => void;\n  selectedItemText?: SelectItemTextElement | null;\n  position?: SelectContentProps['position'];\n  isPositioned?: boolean;\n  searchRef?: React.RefObject<string>;\n};\n\nconst [SelectContentProvider, useSelectContentContext] =\n  createSelectContext<SelectContentContextValue>(CONTENT_NAME);\n\nconst CONTENT_IMPL_NAME = 'SelectContentImpl';\n\ntype SelectContentImplElement = SelectPopperPositionElement | SelectItemAlignedPositionElement;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype FocusScopeProps = React.ComponentPropsWithoutRef<typeof FocusScope>;\n\ntype SelectPopperPrivateProps = { onPlaced?: PopperContentProps['onPlaced'] };\n\ninterface SelectContentImplProps\n  extends Omit<SelectPopperPositionProps, keyof SelectPopperPrivateProps>,\n    Omit<SelectItemAlignedPositionProps, keyof SelectPopperPrivateProps> {\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n  /**\n   * Event handler called when the escape key is down.\n   * Can be prevented.\n   */\n  onEscapeKeyDown?: DismissableLayerProps['onEscapeKeyDown'];\n  /**\n   * Event handler called when the a `pointerdown` event happens outside of the `DismissableLayer`.\n   * Can be prevented.\n   */\n  onPointerDownOutside?: DismissableLayerProps['onPointerDownOutside'];\n\n  position?: 'item-aligned' | 'popper';\n}\n\nconst Slot = createSlot('SelectContent.RemoveScroll');\n\nconst SelectContentImpl = React.forwardRef<SelectContentImplElement, SelectContentImplProps>(\n  (props: ScopedProps<SelectContentImplProps>, forwardedRef) => {\n    const {\n      __scopeSelect,\n      position = 'item-aligned',\n      onCloseAutoFocus,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      //\n      // PopperContent props\n      side,\n      sideOffset,\n      align,\n      alignOffset,\n      arrowPadding,\n      collisionBoundary,\n      collisionPadding,\n      sticky,\n      hideWhenDetached,\n      avoidCollisions,\n      //\n      ...contentProps\n    } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const [content, setContent] = React.useState<SelectContentImplElement | null>(null);\n    const [viewport, setViewport] = React.useState<SelectViewportElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n    const [selectedItem, setSelectedItem] = React.useState<SelectItemElement | null>(null);\n    const [selectedItemText, setSelectedItemText] = React.useState<SelectItemTextElement | null>(\n      null\n    );\n    const getItems = useCollection(__scopeSelect);\n    const [isPositioned, setIsPositioned] = React.useState(false);\n    const firstValidItemFoundRef = React.useRef(false);\n\n    // aria-hide everything except the content (better supported equivalent to setting aria-modal)\n    React.useEffect(() => {\n      if (content) return hideOthers(content);\n    }, [content]);\n\n    // Make sure the whole tree has focus guards as our `Select` may be\n    // the last element in the DOM (because of the `Portal`)\n    useFocusGuards();\n\n    const focusFirst = React.useCallback(\n      (candidates: Array<HTMLElement | null>) => {\n        const [firstItem, ...restItems] = getItems().map((item) => item.ref.current);\n        const [lastItem] = restItems.slice(-1);\n\n        const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n        for (const candidate of candidates) {\n          // if focus is already where we want to go, we don't want to keep going through the candidates\n          if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n          candidate?.scrollIntoView({ block: 'nearest' });\n          // viewport might have padding so scroll to its edges when focusing first/last items.\n          if (candidate === firstItem && viewport) viewport.scrollTop = 0;\n          if (candidate === lastItem && viewport) viewport.scrollTop = viewport.scrollHeight;\n          candidate?.focus();\n          if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n        }\n      },\n      [getItems, viewport]\n    );\n\n    const focusSelectedItem = React.useCallback(\n      () => focusFirst([selectedItem, content]),\n      [focusFirst, selectedItem, content]\n    );\n\n    // Since this is not dependent on layout, we want to ensure this runs at the same time as\n    // other effects across components. Hence why we don't call `focusSelectedItem` inside `position`.\n    React.useEffect(() => {\n      if (isPositioned) {\n        focusSelectedItem();\n      }\n    }, [isPositioned, focusSelectedItem]);\n\n    // prevent selecting items on `pointerup` in some cases after opening from `pointerdown`\n    // and close on `pointerup` outside.\n    const { onOpenChange, triggerPointerDownPosRef } = context;\n    React.useEffect(() => {\n      if (content) {\n        let pointerMoveDelta = { x: 0, y: 0 };\n\n        const handlePointerMove = (event: PointerEvent) => {\n          pointerMoveDelta = {\n            x: Math.abs(Math.round(event.pageX) - (triggerPointerDownPosRef.current?.x ?? 0)),\n            y: Math.abs(Math.round(event.pageY) - (triggerPointerDownPosRef.current?.y ?? 0)),\n          };\n        };\n        const handlePointerUp = (event: PointerEvent) => {\n          // If the pointer hasn't moved by a certain threshold then we prevent selecting item on `pointerup`.\n          if (pointerMoveDelta.x <= 10 && pointerMoveDelta.y <= 10) {\n            event.preventDefault();\n          } else {\n            // otherwise, if the event was outside the content, close.\n            if (!content.contains(event.target as HTMLElement)) {\n              onOpenChange(false);\n            }\n          }\n          document.removeEventListener('pointermove', handlePointerMove);\n          triggerPointerDownPosRef.current = null;\n        };\n\n        if (triggerPointerDownPosRef.current !== null) {\n          document.addEventListener('pointermove', handlePointerMove);\n          document.addEventListener('pointerup', handlePointerUp, { capture: true, once: true });\n        }\n\n        return () => {\n          document.removeEventListener('pointermove', handlePointerMove);\n          document.removeEventListener('pointerup', handlePointerUp, { capture: true });\n        };\n      }\n    }, [content, onOpenChange, triggerPointerDownPosRef]);\n\n    React.useEffect(() => {\n      const close = () => onOpenChange(false);\n      window.addEventListener('blur', close);\n      window.addEventListener('resize', close);\n      return () => {\n        window.removeEventListener('blur', close);\n        window.removeEventListener('resize', close);\n      };\n    }, [onOpenChange]);\n\n    const [searchRef, handleTypeaheadSearch] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.ref.current === document.activeElement);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem) {\n        /**\n         * Imperative focus during keydown is risky so we prevent React's batching updates\n         * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n         */\n        setTimeout(() => (nextItem.ref.current as HTMLElement).focus());\n      }\n    });\n\n    const itemRefCallback = React.useCallback(\n      (node: SelectItemElement | null, value: string, disabled: boolean) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== undefined && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItem(node);\n          if (isFirstValidItem) firstValidItemFoundRef.current = true;\n        }\n      },\n      [context.value]\n    );\n    const handleItemLeave = React.useCallback(() => content?.focus(), [content]);\n    const itemTextRefCallback = React.useCallback(\n      (node: SelectItemTextElement | null, value: string, disabled: boolean) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== undefined && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItemText(node);\n        }\n      },\n      [context.value]\n    );\n\n    const SelectPosition = position === 'popper' ? SelectPopperPosition : SelectItemAlignedPosition;\n\n    // Silently ignore props that are not supported by `SelectItemAlignedPosition`\n    const popperContentProps =\n      SelectPosition === SelectPopperPosition\n        ? {\n            side,\n            sideOffset,\n            align,\n            alignOffset,\n            arrowPadding,\n            collisionBoundary,\n            collisionPadding,\n            sticky,\n            hideWhenDetached,\n            avoidCollisions,\n          }\n        : {};\n\n    return (\n      <SelectContentProvider\n        scope={__scopeSelect}\n        content={content}\n        viewport={viewport}\n        onViewportChange={setViewport}\n        itemRefCallback={itemRefCallback}\n        selectedItem={selectedItem}\n        onItemLeave={handleItemLeave}\n        itemTextRefCallback={itemTextRefCallback}\n        focusSelectedItem={focusSelectedItem}\n        selectedItemText={selectedItemText}\n        position={position}\n        isPositioned={isPositioned}\n        searchRef={searchRef}\n      >\n        <RemoveScroll as={Slot} allowPinchZoom>\n          <FocusScope\n            asChild\n            // we make sure we're not trapping once it's been closed\n            // (closed !== unmounted when animating out)\n            trapped={context.open}\n            onMountAutoFocus={(event) => {\n              // we prevent open autofocus because we manually focus the selected item\n              event.preventDefault();\n            }}\n            onUnmountAutoFocus={composeEventHandlers(onCloseAutoFocus, (event) => {\n              context.trigger?.focus({ preventScroll: true });\n              event.preventDefault();\n            })}\n          >\n            <DismissableLayer\n              asChild\n              disableOutsidePointerEvents\n              onEscapeKeyDown={onEscapeKeyDown}\n              onPointerDownOutside={onPointerDownOutside}\n              // When focus is trapped, a focusout event may still happen.\n              // We make sure we don't trigger our `onDismiss` in such case.\n              onFocusOutside={(event) => event.preventDefault()}\n              onDismiss={() => context.onOpenChange(false)}\n            >\n              <SelectPosition\n                role=\"listbox\"\n                id={context.contentId}\n                data-state={context.open ? 'open' : 'closed'}\n                dir={context.dir}\n                onContextMenu={(event) => event.preventDefault()}\n                {...contentProps}\n                {...popperContentProps}\n                onPlaced={() => setIsPositioned(true)}\n                ref={composedRefs}\n                style={{\n                  // flex layout so we can place the scroll buttons properly\n                  display: 'flex',\n                  flexDirection: 'column',\n                  // reset the outline by default as the content MAY get focused\n                  outline: 'none',\n                  ...contentProps.style,\n                }}\n                onKeyDown={composeEventHandlers(contentProps.onKeyDown, (event) => {\n                  const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n\n                  // select should not be navigated using tab key so we prevent it\n                  if (event.key === 'Tab') event.preventDefault();\n\n                  if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n\n                  if (['ArrowUp', 'ArrowDown', 'Home', 'End'].includes(event.key)) {\n                    const items = getItems().filter((item) => !item.disabled);\n                    let candidateNodes = items.map((item) => item.ref.current!);\n\n                    if (['ArrowUp', 'End'].includes(event.key)) {\n                      candidateNodes = candidateNodes.slice().reverse();\n                    }\n                    if (['ArrowUp', 'ArrowDown'].includes(event.key)) {\n                      const currentElement = event.target as SelectItemElement;\n                      const currentIndex = candidateNodes.indexOf(currentElement);\n                      candidateNodes = candidateNodes.slice(currentIndex + 1);\n                    }\n\n                    /**\n                     * Imperative focus during keydown is risky so we prevent React's batching updates\n                     * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n                     */\n                    setTimeout(() => focusFirst(candidateNodes));\n\n                    event.preventDefault();\n                  }\n                })}\n              />\n            </DismissableLayer>\n          </FocusScope>\n        </RemoveScroll>\n      </SelectContentProvider>\n    );\n  }\n);\n\nSelectContentImpl.displayName = CONTENT_IMPL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemAlignedPosition\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_ALIGNED_POSITION_NAME = 'SelectItemAlignedPosition';\n\ntype SelectItemAlignedPositionElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectItemAlignedPositionProps extends PrimitiveDivProps, SelectPopperPrivateProps {}\n\nconst SelectItemAlignedPosition = React.forwardRef<\n  SelectItemAlignedPositionElement,\n  SelectItemAlignedPositionProps\n>((props: ScopedProps<SelectItemAlignedPositionProps>, forwardedRef) => {\n  const { __scopeSelect, onPlaced, ...popperProps } = props;\n  const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n  const contentContext = useSelectContentContext(CONTENT_NAME, __scopeSelect);\n  const [contentWrapper, setContentWrapper] = React.useState<HTMLDivElement | null>(null);\n  const [content, setContent] = React.useState<SelectItemAlignedPositionElement | null>(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n  const getItems = useCollection(__scopeSelect);\n  const shouldExpandOnScrollRef = React.useRef(false);\n  const shouldRepositionRef = React.useRef(true);\n\n  const { viewport, selectedItem, selectedItemText, focusSelectedItem } = contentContext;\n  const position = React.useCallback(() => {\n    if (\n      context.trigger &&\n      context.valueNode &&\n      contentWrapper &&\n      content &&\n      viewport &&\n      selectedItem &&\n      selectedItemText\n    ) {\n      const triggerRect = context.trigger.getBoundingClientRect();\n\n      // -----------------------------------------------------------------------------------------\n      //  Horizontal positioning\n      // -----------------------------------------------------------------------------------------\n      const contentRect = content.getBoundingClientRect();\n      const valueNodeRect = context.valueNode.getBoundingClientRect();\n      const itemTextRect = selectedItemText.getBoundingClientRect();\n\n      if (context.dir !== 'rtl') {\n        const itemTextOffset = itemTextRect.left - contentRect.left;\n        const left = valueNodeRect.left - itemTextOffset;\n        const leftDelta = triggerRect.left - left;\n        const minContentWidth = triggerRect.width + leftDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const rightEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedLeft = clamp(left, [\n          CONTENT_MARGIN,\n          // Prevents the content from going off the starting edge of the\n          // viewport. It may still go off the ending edge, but this can be\n          // controlled by the user since they may want to manage overflow in a\n          // specific way.\n          // https://github.com/radix-ui/primitives/issues/2049\n          Math.max(CONTENT_MARGIN, rightEdge - contentWidth),\n        ]);\n\n        contentWrapper.style.minWidth = minContentWidth + 'px';\n        contentWrapper.style.left = clampedLeft + 'px';\n      } else {\n        const itemTextOffset = contentRect.right - itemTextRect.right;\n        const right = window.innerWidth - valueNodeRect.right - itemTextOffset;\n        const rightDelta = window.innerWidth - triggerRect.right - right;\n        const minContentWidth = triggerRect.width + rightDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const leftEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedRight = clamp(right, [\n          CONTENT_MARGIN,\n          Math.max(CONTENT_MARGIN, leftEdge - contentWidth),\n        ]);\n\n        contentWrapper.style.minWidth = minContentWidth + 'px';\n        contentWrapper.style.right = clampedRight + 'px';\n      }\n\n      // -----------------------------------------------------------------------------------------\n      // Vertical positioning\n      // -----------------------------------------------------------------------------------------\n      const items = getItems();\n      const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n      const itemsHeight = viewport.scrollHeight;\n\n      const contentStyles = window.getComputedStyle(content);\n      const contentBorderTopWidth = parseInt(contentStyles.borderTopWidth, 10);\n      const contentPaddingTop = parseInt(contentStyles.paddingTop, 10);\n      const contentBorderBottomWidth = parseInt(contentStyles.borderBottomWidth, 10);\n      const contentPaddingBottom = parseInt(contentStyles.paddingBottom, 10);\n      const fullContentHeight = contentBorderTopWidth + contentPaddingTop + itemsHeight + contentPaddingBottom + contentBorderBottomWidth; // prettier-ignore\n      const minContentHeight = Math.min(selectedItem.offsetHeight * 5, fullContentHeight);\n\n      const viewportStyles = window.getComputedStyle(viewport);\n      const viewportPaddingTop = parseInt(viewportStyles.paddingTop, 10);\n      const viewportPaddingBottom = parseInt(viewportStyles.paddingBottom, 10);\n\n      const topEdgeToTriggerMiddle = triggerRect.top + triggerRect.height / 2 - CONTENT_MARGIN;\n      const triggerMiddleToBottomEdge = availableHeight - topEdgeToTriggerMiddle;\n\n      const selectedItemHalfHeight = selectedItem.offsetHeight / 2;\n      const itemOffsetMiddle = selectedItem.offsetTop + selectedItemHalfHeight;\n      const contentTopToItemMiddle = contentBorderTopWidth + contentPaddingTop + itemOffsetMiddle;\n      const itemMiddleToContentBottom = fullContentHeight - contentTopToItemMiddle;\n\n      const willAlignWithoutTopOverflow = contentTopToItemMiddle <= topEdgeToTriggerMiddle;\n\n      if (willAlignWithoutTopOverflow) {\n        const isLastItem =\n          items.length > 0 && selectedItem === items[items.length - 1]!.ref.current;\n        contentWrapper.style.bottom = 0 + 'px';\n        const viewportOffsetBottom =\n          content.clientHeight - viewport.offsetTop - viewport.offsetHeight;\n        const clampedTriggerMiddleToBottomEdge = Math.max(\n          triggerMiddleToBottomEdge,\n          selectedItemHalfHeight +\n            // viewport might have padding bottom, include it to avoid a scrollable viewport\n            (isLastItem ? viewportPaddingBottom : 0) +\n            viewportOffsetBottom +\n            contentBorderBottomWidth\n        );\n        const height = contentTopToItemMiddle + clampedTriggerMiddleToBottomEdge;\n        contentWrapper.style.height = height + 'px';\n      } else {\n        const isFirstItem = items.length > 0 && selectedItem === items[0]!.ref.current;\n        contentWrapper.style.top = 0 + 'px';\n        const clampedTopEdgeToTriggerMiddle = Math.max(\n          topEdgeToTriggerMiddle,\n          contentBorderTopWidth +\n            viewport.offsetTop +\n            // viewport might have padding top, include it to avoid a scrollable viewport\n            (isFirstItem ? viewportPaddingTop : 0) +\n            selectedItemHalfHeight\n        );\n        const height = clampedTopEdgeToTriggerMiddle + itemMiddleToContentBottom;\n        contentWrapper.style.height = height + 'px';\n        viewport.scrollTop = contentTopToItemMiddle - topEdgeToTriggerMiddle + viewport.offsetTop;\n      }\n\n      contentWrapper.style.margin = `${CONTENT_MARGIN}px 0`;\n      contentWrapper.style.minHeight = minContentHeight + 'px';\n      contentWrapper.style.maxHeight = availableHeight + 'px';\n      // -----------------------------------------------------------------------------------------\n\n      onPlaced?.();\n\n      // we don't want the initial scroll position adjustment to trigger \"expand on scroll\"\n      // so we explicitly turn it on only after they've registered.\n      requestAnimationFrame(() => (shouldExpandOnScrollRef.current = true));\n    }\n  }, [\n    getItems,\n    context.trigger,\n    context.valueNode,\n    contentWrapper,\n    content,\n    viewport,\n    selectedItem,\n    selectedItemText,\n    context.dir,\n    onPlaced,\n  ]);\n\n  useLayoutEffect(() => position(), [position]);\n\n  // copy z-index from content to wrapper\n  const [contentZIndex, setContentZIndex] = React.useState<string>();\n  useLayoutEffect(() => {\n    if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n  }, [content]);\n\n  // When the viewport becomes scrollable at the top, the scroll up button will mount.\n  // Because it is part of the normal flow, it will push down the viewport, thus throwing our\n  // trigger => selectedItem alignment off by the amount the viewport was pushed down.\n  // We wait for this to happen and then re-run the positining logic one more time to account for it.\n  const handleScrollButtonChange = React.useCallback(\n    (node: SelectScrollButtonImplElement | null) => {\n      if (node && shouldRepositionRef.current === true) {\n        position();\n        focusSelectedItem?.();\n        shouldRepositionRef.current = false;\n      }\n    },\n    [position, focusSelectedItem]\n  );\n\n  return (\n    <SelectViewportProvider\n      scope={__scopeSelect}\n      contentWrapper={contentWrapper}\n      shouldExpandOnScrollRef={shouldExpandOnScrollRef}\n      onScrollButtonChange={handleScrollButtonChange}\n    >\n      <div\n        ref={setContentWrapper}\n        style={{\n          display: 'flex',\n          flexDirection: 'column',\n          position: 'fixed',\n          zIndex: contentZIndex,\n        }}\n      >\n        <Primitive.div\n          {...popperProps}\n          ref={composedRefs}\n          style={{\n            // When we get the height of the content, it includes borders. If we were to set\n            // the height without having `boxSizing: 'border-box'` it would be too big.\n            boxSizing: 'border-box',\n            // We need to ensure the content doesn't get taller than the wrapper\n            maxHeight: '100%',\n            ...popperProps.style,\n          }}\n        />\n      </div>\n    </SelectViewportProvider>\n  );\n});\n\nSelectItemAlignedPosition.displayName = ITEM_ALIGNED_POSITION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectPopperPosition\n * -----------------------------------------------------------------------------------------------*/\n\nconst POPPER_POSITION_NAME = 'SelectPopperPosition';\n\ntype SelectPopperPositionElement = React.ComponentRef<typeof PopperPrimitive.Content>;\ntype PopperContentProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Content>;\ninterface SelectPopperPositionProps extends PopperContentProps, SelectPopperPrivateProps {}\n\nconst SelectPopperPosition = React.forwardRef<\n  SelectPopperPositionElement,\n  SelectPopperPositionProps\n>((props: ScopedProps<SelectPopperPositionProps>, forwardedRef) => {\n  const {\n    __scopeSelect,\n    align = 'start',\n    collisionPadding = CONTENT_MARGIN,\n    ...popperProps\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n\n  return (\n    <PopperPrimitive.Content\n      {...popperScope}\n      {...popperProps}\n      ref={forwardedRef}\n      align={align}\n      collisionPadding={collisionPadding}\n      style={{\n        // Ensure border-box for floating-ui calculations\n        boxSizing: 'border-box',\n        ...popperProps.style,\n        // re-namespace exposed content custom properties\n        ...{\n          '--radix-select-content-transform-origin': 'var(--radix-popper-transform-origin)',\n          '--radix-select-content-available-width': 'var(--radix-popper-available-width)',\n          '--radix-select-content-available-height': 'var(--radix-popper-available-height)',\n          '--radix-select-trigger-width': 'var(--radix-popper-anchor-width)',\n          '--radix-select-trigger-height': 'var(--radix-popper-anchor-height)',\n        },\n      }}\n    />\n  );\n});\n\nSelectPopperPosition.displayName = POPPER_POSITION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectViewport\n * -----------------------------------------------------------------------------------------------*/\n\ntype SelectViewportContextValue = {\n  contentWrapper?: HTMLDivElement | null;\n  shouldExpandOnScrollRef?: React.RefObject<boolean>;\n  onScrollButtonChange?: (node: SelectScrollButtonImplElement | null) => void;\n};\n\nconst [SelectViewportProvider, useSelectViewportContext] =\n  createSelectContext<SelectViewportContextValue>(CONTENT_NAME, {});\n\nconst VIEWPORT_NAME = 'SelectViewport';\n\ntype SelectViewportElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface SelectViewportProps extends PrimitiveDivProps {\n  nonce?: string;\n}\n\nconst SelectViewport = React.forwardRef<SelectViewportElement, SelectViewportProps>(\n  (props: ScopedProps<SelectViewportProps>, forwardedRef) => {\n    const { __scopeSelect, nonce, ...viewportProps } = props;\n    const contentContext = useSelectContentContext(VIEWPORT_NAME, __scopeSelect);\n    const viewportContext = useSelectViewportContext(VIEWPORT_NAME, __scopeSelect);\n    const composedRefs = useComposedRefs(forwardedRef, contentContext.onViewportChange);\n    const prevScrollTopRef = React.useRef(0);\n    return (\n      <>\n        {/* Hide scrollbars cross-browser and enable momentum scroll for touch devices */}\n        <style\n          dangerouslySetInnerHTML={{\n            __html: `[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}`,\n          }}\n          nonce={nonce}\n        />\n        <Collection.Slot scope={__scopeSelect}>\n          <Primitive.div\n            data-radix-select-viewport=\"\"\n            role=\"presentation\"\n            {...viewportProps}\n            ref={composedRefs}\n            style={{\n              // we use position: 'relative' here on the `viewport` so that when we call\n              // `selectedItem.offsetTop` in calculations, the offset is relative to the viewport\n              // (independent of the scrollUpButton).\n              position: 'relative',\n              flex: 1,\n              // Viewport should only be scrollable in the vertical direction.\n              // This won't work in vertical writing modes, so we'll need to\n              // revisit this if/when that is supported\n              // https://developer.chrome.com/blog/vertical-form-controls\n              overflow: 'hidden auto',\n              ...viewportProps.style,\n            }}\n            onScroll={composeEventHandlers(viewportProps.onScroll, (event) => {\n              const viewport = event.currentTarget;\n              const { contentWrapper, shouldExpandOnScrollRef } = viewportContext;\n              if (shouldExpandOnScrollRef?.current && contentWrapper) {\n                const scrolledBy = Math.abs(prevScrollTopRef.current - viewport.scrollTop);\n                if (scrolledBy > 0) {\n                  const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n                  const cssMinHeight = parseFloat(contentWrapper.style.minHeight);\n                  const cssHeight = parseFloat(contentWrapper.style.height);\n                  const prevHeight = Math.max(cssMinHeight, cssHeight);\n\n                  if (prevHeight < availableHeight) {\n                    const nextHeight = prevHeight + scrolledBy;\n                    const clampedNextHeight = Math.min(availableHeight, nextHeight);\n                    const heightDiff = nextHeight - clampedNextHeight;\n\n                    contentWrapper.style.height = clampedNextHeight + 'px';\n                    if (contentWrapper.style.bottom === '0px') {\n                      viewport.scrollTop = heightDiff > 0 ? heightDiff : 0;\n                      // ensure the content stays pinned to the bottom\n                      contentWrapper.style.justifyContent = 'flex-end';\n                    }\n                  }\n                }\n              }\n              prevScrollTopRef.current = viewport.scrollTop;\n            })}\n          />\n        </Collection.Slot>\n      </>\n    );\n  }\n);\n\nSelectViewport.displayName = VIEWPORT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'SelectGroup';\n\ntype SelectGroupContextValue = { id: string };\n\nconst [SelectGroupContextProvider, useSelectGroupContext] =\n  createSelectContext<SelectGroupContextValue>(GROUP_NAME);\n\ntype SelectGroupElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectGroupProps extends PrimitiveDivProps {}\n\nconst SelectGroup = React.forwardRef<SelectGroupElement, SelectGroupProps>(\n  (props: ScopedProps<SelectGroupProps>, forwardedRef) => {\n    const { __scopeSelect, ...groupProps } = props;\n    const groupId = useId();\n    return (\n      <SelectGroupContextProvider scope={__scopeSelect} id={groupId}>\n        <Primitive.div role=\"group\" aria-labelledby={groupId} {...groupProps} ref={forwardedRef} />\n      </SelectGroupContextProvider>\n    );\n  }\n);\n\nSelectGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'SelectLabel';\n\ntype SelectLabelElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectLabelProps extends PrimitiveDivProps {}\n\nconst SelectLabel = React.forwardRef<SelectLabelElement, SelectLabelProps>(\n  (props: ScopedProps<SelectLabelProps>, forwardedRef) => {\n    const { __scopeSelect, ...labelProps } = props;\n    const groupContext = useSelectGroupContext(LABEL_NAME, __scopeSelect);\n    return <Primitive.div id={groupContext.id} {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nSelectLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'SelectItem';\n\ntype SelectItemContextValue = {\n  value: string;\n  disabled: boolean;\n  textId: string;\n  isSelected: boolean;\n  onItemTextChange(node: SelectItemTextElement | null): void;\n};\n\nconst [SelectItemContextProvider, useSelectItemContext] =\n  createSelectContext<SelectItemContextValue>(ITEM_NAME);\n\ntype SelectItemElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectItemProps extends PrimitiveDivProps {\n  value: string;\n  disabled?: boolean;\n  textValue?: string;\n}\n\nconst SelectItem = React.forwardRef<SelectItemElement, SelectItemProps>(\n  (props: ScopedProps<SelectItemProps>, forwardedRef) => {\n    const {\n      __scopeSelect,\n      value,\n      disabled = false,\n      textValue: textValueProp,\n      ...itemProps\n    } = props;\n    const context = useSelectContext(ITEM_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_NAME, __scopeSelect);\n    const isSelected = context.value === value;\n    const [textValue, setTextValue] = React.useState(textValueProp ?? '');\n    const [isFocused, setIsFocused] = React.useState(false);\n    const composedRefs = useComposedRefs(forwardedRef, (node) =>\n      contentContext.itemRefCallback?.(node, value, disabled)\n    );\n    const textId = useId();\n    const pointerTypeRef = React.useRef<React.PointerEvent['pointerType']>('touch');\n\n    const handleSelect = () => {\n      if (!disabled) {\n        context.onValueChange(value);\n        context.onOpenChange(false);\n      }\n    };\n\n    if (value === '') {\n      throw new Error(\n        'A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.'\n      );\n    }\n\n    return (\n      <SelectItemContextProvider\n        scope={__scopeSelect}\n        value={value}\n        disabled={disabled}\n        textId={textId}\n        isSelected={isSelected}\n        onItemTextChange={React.useCallback((node) => {\n          setTextValue((prevTextValue) => prevTextValue || (node?.textContent ?? '').trim());\n        }, [])}\n      >\n        <Collection.ItemSlot\n          scope={__scopeSelect}\n          value={value}\n          disabled={disabled}\n          textValue={textValue}\n        >\n          <Primitive.div\n            role=\"option\"\n            aria-labelledby={textId}\n            data-highlighted={isFocused ? '' : undefined}\n            // `isFocused` caveat fixes stuttering in VoiceOver\n            aria-selected={isSelected && isFocused}\n            data-state={isSelected ? 'checked' : 'unchecked'}\n            aria-disabled={disabled || undefined}\n            data-disabled={disabled ? '' : undefined}\n            tabIndex={disabled ? undefined : -1}\n            {...itemProps}\n            ref={composedRefs}\n            onFocus={composeEventHandlers(itemProps.onFocus, () => setIsFocused(true))}\n            onBlur={composeEventHandlers(itemProps.onBlur, () => setIsFocused(false))}\n            onClick={composeEventHandlers(itemProps.onClick, () => {\n              // Open on click when using a touch or pen device\n              if (pointerTypeRef.current !== 'mouse') handleSelect();\n            })}\n            onPointerUp={composeEventHandlers(itemProps.onPointerUp, () => {\n              // Using a mouse you should be able to do pointer down, move through\n              // the list, and release the pointer over the item to select it.\n              if (pointerTypeRef.current === 'mouse') handleSelect();\n            })}\n            onPointerDown={composeEventHandlers(itemProps.onPointerDown, (event) => {\n              pointerTypeRef.current = event.pointerType;\n            })}\n            onPointerMove={composeEventHandlers(itemProps.onPointerMove, (event) => {\n              // Remember pointer type when sliding over to this item from another one\n              pointerTypeRef.current = event.pointerType;\n              if (disabled) {\n                contentContext.onItemLeave?.();\n              } else if (pointerTypeRef.current === 'mouse') {\n                // even though safari doesn't support this option, it's acceptable\n                // as it only means it might scroll a few pixels when using the pointer.\n                event.currentTarget.focus({ preventScroll: true });\n              }\n            })}\n            onPointerLeave={composeEventHandlers(itemProps.onPointerLeave, (event) => {\n              if (event.currentTarget === document.activeElement) {\n                contentContext.onItemLeave?.();\n              }\n            })}\n            onKeyDown={composeEventHandlers(itemProps.onKeyDown, (event) => {\n              const isTypingAhead = contentContext.searchRef?.current !== '';\n              if (isTypingAhead && event.key === ' ') return;\n              if (SELECTION_KEYS.includes(event.key)) handleSelect();\n              // prevent page scroll if using the space key to select an item\n              if (event.key === ' ') event.preventDefault();\n            })}\n          />\n        </Collection.ItemSlot>\n      </SelectItemContextProvider>\n    );\n  }\n);\n\nSelectItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemText\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_TEXT_NAME = 'SelectItemText';\n\ntype SelectItemTextElement = React.ComponentRef<typeof Primitive.span>;\ninterface SelectItemTextProps extends PrimitiveSpanProps {}\n\nconst SelectItemText = React.forwardRef<SelectItemTextElement, SelectItemTextProps>(\n  (props: ScopedProps<SelectItemTextProps>, forwardedRef) => {\n    // We ignore `className` and `style` as this part shouldn't be styled.\n    const { __scopeSelect, className, style, ...itemTextProps } = props;\n    const context = useSelectContext(ITEM_TEXT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_TEXT_NAME, __scopeSelect);\n    const itemContext = useSelectItemContext(ITEM_TEXT_NAME, __scopeSelect);\n    const nativeOptionsContext = useSelectNativeOptionsContext(ITEM_TEXT_NAME, __scopeSelect);\n    const [itemTextNode, setItemTextNode] = React.useState<SelectItemTextElement | null>(null);\n    const composedRefs = useComposedRefs(\n      forwardedRef,\n      (node) => setItemTextNode(node),\n      itemContext.onItemTextChange,\n      (node) => contentContext.itemTextRefCallback?.(node, itemContext.value, itemContext.disabled)\n    );\n\n    const textContent = itemTextNode?.textContent;\n    const nativeOption = React.useMemo(\n      () => (\n        <option key={itemContext.value} value={itemContext.value} disabled={itemContext.disabled}>\n          {textContent}\n        </option>\n      ),\n      [itemContext.disabled, itemContext.value, textContent]\n    );\n\n    const { onNativeOptionAdd, onNativeOptionRemove } = nativeOptionsContext;\n    useLayoutEffect(() => {\n      onNativeOptionAdd(nativeOption);\n      return () => onNativeOptionRemove(nativeOption);\n    }, [onNativeOptionAdd, onNativeOptionRemove, nativeOption]);\n\n    return (\n      <>\n        <Primitive.span id={itemContext.textId} {...itemTextProps} ref={composedRefs} />\n\n        {/* Portal the select item text into the trigger value node */}\n        {itemContext.isSelected && context.valueNode && !context.valueNodeHasChildren\n          ? ReactDOM.createPortal(itemTextProps.children, context.valueNode)\n          : null}\n      </>\n    );\n  }\n);\n\nSelectItemText.displayName = ITEM_TEXT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_INDICATOR_NAME = 'SelectItemIndicator';\n\ntype SelectItemIndicatorElement = React.ComponentRef<typeof Primitive.span>;\ninterface SelectItemIndicatorProps extends PrimitiveSpanProps {}\n\nconst SelectItemIndicator = React.forwardRef<SelectItemIndicatorElement, SelectItemIndicatorProps>(\n  (props: ScopedProps<SelectItemIndicatorProps>, forwardedRef) => {\n    const { __scopeSelect, ...itemIndicatorProps } = props;\n    const itemContext = useSelectItemContext(ITEM_INDICATOR_NAME, __scopeSelect);\n    return itemContext.isSelected ? (\n      <Primitive.span aria-hidden {...itemIndicatorProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nSelectItemIndicator.displayName = ITEM_INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectScrollUpButton\n * -----------------------------------------------------------------------------------------------*/\n\nconst SCROLL_UP_BUTTON_NAME = 'SelectScrollUpButton';\n\ntype SelectScrollUpButtonElement = SelectScrollButtonImplElement;\ninterface SelectScrollUpButtonProps extends Omit<SelectScrollButtonImplProps, 'onAutoScroll'> {}\n\nconst SelectScrollUpButton = React.forwardRef<\n  SelectScrollUpButtonElement,\n  SelectScrollUpButtonProps\n>((props: ScopedProps<SelectScrollUpButtonProps>, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollUp, setCanScrollUp] = React.useState(false);\n  const composedRefs = useComposedRefs(forwardedRef, viewportContext.onScrollButtonChange);\n\n  useLayoutEffect(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      const viewport = contentContext.viewport;\n      function handleScroll() {\n        const canScrollUp = viewport.scrollTop > 0;\n        setCanScrollUp(canScrollUp);\n      }\n      handleScroll();\n      viewport.addEventListener('scroll', handleScroll);\n      return () => viewport.removeEventListener('scroll', handleScroll);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n\n  return canScrollUp ? (\n    <SelectScrollButtonImpl\n      {...props}\n      ref={composedRefs}\n      onAutoScroll={() => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop - selectedItem.offsetHeight;\n        }\n      }}\n    />\n  ) : null;\n});\n\nSelectScrollUpButton.displayName = SCROLL_UP_BUTTON_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectScrollDownButton\n * -----------------------------------------------------------------------------------------------*/\n\nconst SCROLL_DOWN_BUTTON_NAME = 'SelectScrollDownButton';\n\ntype SelectScrollDownButtonElement = SelectScrollButtonImplElement;\ninterface SelectScrollDownButtonProps extends Omit<SelectScrollButtonImplProps, 'onAutoScroll'> {}\n\nconst SelectScrollDownButton = React.forwardRef<\n  SelectScrollDownButtonElement,\n  SelectScrollDownButtonProps\n>((props: ScopedProps<SelectScrollDownButtonProps>, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollDown, setCanScrollDown] = React.useState(false);\n  const composedRefs = useComposedRefs(forwardedRef, viewportContext.onScrollButtonChange);\n\n  useLayoutEffect(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      const viewport = contentContext.viewport;\n      function handleScroll() {\n        const maxScroll = viewport.scrollHeight - viewport.clientHeight;\n        // we use Math.ceil here because if the UI is zoomed-in\n        // `scrollTop` is not always reported as an integer\n        const canScrollDown = Math.ceil(viewport.scrollTop) < maxScroll;\n        setCanScrollDown(canScrollDown);\n      }\n      handleScroll();\n      viewport.addEventListener('scroll', handleScroll);\n      return () => viewport.removeEventListener('scroll', handleScroll);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n\n  return canScrollDown ? (\n    <SelectScrollButtonImpl\n      {...props}\n      ref={composedRefs}\n      onAutoScroll={() => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop + selectedItem.offsetHeight;\n        }\n      }}\n    />\n  ) : null;\n});\n\nSelectScrollDownButton.displayName = SCROLL_DOWN_BUTTON_NAME;\n\ntype SelectScrollButtonImplElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectScrollButtonImplProps extends PrimitiveDivProps {\n  onAutoScroll(): void;\n}\n\nconst SelectScrollButtonImpl = React.forwardRef<\n  SelectScrollButtonImplElement,\n  SelectScrollButtonImplProps\n>((props: ScopedProps<SelectScrollButtonImplProps>, forwardedRef) => {\n  const { __scopeSelect, onAutoScroll, ...scrollIndicatorProps } = props;\n  const contentContext = useSelectContentContext('SelectScrollButton', __scopeSelect);\n  const autoScrollTimerRef = React.useRef<number | null>(null);\n  const getItems = useCollection(__scopeSelect);\n\n  const clearAutoScrollTimer = React.useCallback(() => {\n    if (autoScrollTimerRef.current !== null) {\n      window.clearInterval(autoScrollTimerRef.current);\n      autoScrollTimerRef.current = null;\n    }\n  }, []);\n\n  React.useEffect(() => {\n    return () => clearAutoScrollTimer();\n  }, [clearAutoScrollTimer]);\n\n  // When the viewport becomes scrollable on either side, the relevant scroll button will mount.\n  // Because it is part of the normal flow, it will push down (top button) or shrink (bottom button)\n  // the viewport, potentially causing the active item to now be partially out of view.\n  // We re-run the `scrollIntoView` logic to make sure it stays within the viewport.\n  useLayoutEffect(() => {\n    const activeItem = getItems().find((item) => item.ref.current === document.activeElement);\n    activeItem?.ref.current?.scrollIntoView({ block: 'nearest' });\n  }, [getItems]);\n\n  return (\n    <Primitive.div\n      aria-hidden\n      {...scrollIndicatorProps}\n      ref={forwardedRef}\n      style={{ flexShrink: 0, ...scrollIndicatorProps.style }}\n      onPointerDown={composeEventHandlers(scrollIndicatorProps.onPointerDown, () => {\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      })}\n      onPointerMove={composeEventHandlers(scrollIndicatorProps.onPointerMove, () => {\n        contentContext.onItemLeave?.();\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      })}\n      onPointerLeave={composeEventHandlers(scrollIndicatorProps.onPointerLeave, () => {\n        clearAutoScrollTimer();\n      })}\n    />\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * SelectSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'SelectSeparator';\n\ntype SelectSeparatorElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectSeparatorProps extends PrimitiveDivProps {}\n\nconst SelectSeparator = React.forwardRef<SelectSeparatorElement, SelectSeparatorProps>(\n  (props: ScopedProps<SelectSeparatorProps>, forwardedRef) => {\n    const { __scopeSelect, ...separatorProps } = props;\n    return <Primitive.div aria-hidden {...separatorProps} ref={forwardedRef} />;\n  }\n);\n\nSelectSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'SelectArrow';\n\ntype SelectArrowElement = React.ComponentRef<typeof PopperPrimitive.Arrow>;\ntype PopperArrowProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Arrow>;\ninterface SelectArrowProps extends PopperArrowProps {}\n\nconst SelectArrow = React.forwardRef<SelectArrowElement, SelectArrowProps>(\n  (props: ScopedProps<SelectArrowProps>, forwardedRef) => {\n    const { __scopeSelect, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(ARROW_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ARROW_NAME, __scopeSelect);\n    return context.open && contentContext.position === 'popper' ? (\n      <PopperPrimitive.Arrow {...popperScope} {...arrowProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nSelectArrow.displayName = ARROW_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectBubbleInput\n * -----------------------------------------------------------------------------------------------*/\n\nconst BUBBLE_INPUT_NAME = 'SelectBubbleInput';\n\ntype InputProps = React.ComponentPropsWithoutRef<typeof Primitive.select>;\ninterface SwitchBubbleInputProps extends InputProps {}\n\nconst SelectBubbleInput = React.forwardRef<HTMLSelectElement, SwitchBubbleInputProps>(\n  ({ __scopeSelect, value, ...props }: ScopedProps<SwitchBubbleInputProps>, forwardedRef) => {\n    const ref = React.useRef<HTMLSelectElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const prevValue = usePrevious(value);\n\n    // Bubble value change to parents (e.g form change event)\n    React.useEffect(() => {\n      const select = ref.current;\n      if (!select) return;\n\n      const selectProto = window.HTMLSelectElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        selectProto,\n        'value'\n      ) as PropertyDescriptor;\n      const setValue = descriptor.set;\n      if (prevValue !== value && setValue) {\n        const event = new Event('change', { bubbles: true });\n        setValue.call(select, value);\n        select.dispatchEvent(event);\n      }\n    }, [prevValue, value]);\n\n    /**\n     * We purposefully use a `select` here to support form autofill as much as\n     * possible.\n     *\n     * We purposefully do not add the `value` attribute here to allow the value\n     * to be set programmatically and bubble to any parent form `onChange`\n     * event. Adding the `value` will cause React to consider the programmatic\n     * dispatch a duplicate and it will get swallowed.\n     *\n     * We use visually hidden styles rather than `display: \"none\"` because\n     * Safari autofill won't work otherwise.\n     */\n    return (\n      <Primitive.select\n        {...props}\n        style={{ ...VISUALLY_HIDDEN_STYLES, ...props.style }}\n        ref={composedRefs}\n        defaultValue={value}\n      />\n    );\n  }\n);\n\nSelectBubbleInput.displayName = BUBBLE_INPUT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction shouldShowPlaceholder(value?: string) {\n  return value === '' || value === undefined;\n}\n\nfunction useTypeaheadSearch(onSearchChange: (search: string) => void) {\n  const handleSearchChange = useCallbackRef(onSearchChange);\n  const searchRef = React.useRef('');\n  const timerRef = React.useRef(0);\n\n  const handleTypeaheadSearch = React.useCallback(\n    (key: string) => {\n      const search = searchRef.current + key;\n      handleSearchChange(search);\n\n      (function updateSearch(value: string) {\n        searchRef.current = value;\n        window.clearTimeout(timerRef.current);\n        // Reset `searchRef` 1 second after it was last updated\n        if (value !== '') timerRef.current = window.setTimeout(() => updateSearch(''), 1000);\n      })(search);\n    },\n    [handleSearchChange]\n  );\n\n  const resetTypeahead = React.useCallback(() => {\n    searchRef.current = '';\n    window.clearTimeout(timerRef.current);\n  }, []);\n\n  React.useEffect(() => {\n    return () => window.clearTimeout(timerRef.current);\n  }, []);\n\n  return [searchRef, handleTypeaheadSearch, resetTypeahead] as const;\n}\n\n/**\n * This is the \"meat\" of the typeahead matching logic. It takes in a list of items,\n * the search and the current item, and returns the next item (or `undefined`).\n *\n * We normalize the search because if a user has repeatedly pressed a character,\n * we want the exact same behavior as if we only had that one character\n * (ie. cycle through items starting with that character)\n *\n * We also reorder the items by wrapping the array around the current item.\n * This is so we always look forward from the current item, and picking the first\n * item will always be the correct one.\n *\n * Finally, if the normalized search is exactly one character, we exclude the\n * current item from the values because otherwise it would be the first to match always\n * and focus would never move. This is as opposed to the regular case, where we\n * don't want focus to move if the current item still matches.\n */\nfunction findNextItem<T extends { textValue: string }>(\n  items: T[],\n  search: string,\n  currentItem?: T\n) {\n  const isRepeated = search.length > 1 && Array.from(search).every((char) => char === search[0]);\n  const normalizedSearch = isRepeated ? search[0]! : search;\n  const currentItemIndex = currentItem ? items.indexOf(currentItem) : -1;\n  let wrappedItems = wrapArray(items, Math.max(currentItemIndex, 0));\n  const excludeCurrentItem = normalizedSearch.length === 1;\n  if (excludeCurrentItem) wrappedItems = wrappedItems.filter((v) => v !== currentItem);\n  const nextItem = wrappedItems.find((item) =>\n    item.textValue.toLowerCase().startsWith(normalizedSearch.toLowerCase())\n  );\n  return nextItem !== currentItem ? nextItem : undefined;\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map<T>((_, index) => array[(startIndex + index) % array.length]!);\n}\n\nconst Root = Select;\nconst Trigger = SelectTrigger;\nconst Value = SelectValue;\nconst Icon = SelectIcon;\nconst Portal = SelectPortal;\nconst Content = SelectContent;\nconst Viewport = SelectViewport;\nconst Group = SelectGroup;\nconst Label = SelectLabel;\nconst Item = SelectItem;\nconst ItemText = SelectItemText;\nconst ItemIndicator = SelectItemIndicator;\nconst ScrollUpButton = SelectScrollUpButton;\nconst ScrollDownButton = SelectScrollDownButton;\nconst Separator = SelectSeparator;\nconst Arrow = SelectArrow;\n\nexport {\n  createSelectScope,\n  //\n  Select,\n  SelectTrigger,\n  SelectValue,\n  SelectIcon,\n  SelectPortal,\n  SelectContent,\n  SelectViewport,\n  SelectGroup,\n  SelectLabel,\n  SelectItem,\n  SelectItemText,\n  SelectItemIndicator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n  SelectSeparator,\n  SelectArrow,\n  //\n  Root,\n  Trigger,\n  Value,\n  Icon,\n  Portal,\n  Content,\n  Viewport,\n  Group,\n  Label,\n  Item,\n  ItemText,\n  ItemIndicator,\n  ScrollUpButton,\n  ScrollDownButton,\n  Separator,\n  Arrow,\n};\nexport type {\n  SelectProps,\n  SelectTriggerProps,\n  SelectValueProps,\n  SelectIconProps,\n  SelectPortalProps,\n  SelectContentProps,\n  SelectViewportProps,\n  SelectGroupProps,\n  SelectLabelProps,\n  SelectItemProps,\n  SelectItemTextProps,\n  SelectItemIndicatorProps,\n  SelectScrollUpButtonProps,\n  SelectScrollDownButtonProps,\n  SelectSeparatorProps,\n  SelectArrowProps,\n};\n", "function clamp(value: number, [min, max]: [number, number]): number {\n  return Math.min(max, Math.max(min, value));\n}\n\nexport { clamp };\n", "import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { createSlot } from '@radix-ui/react-slot';\n\nconst NODES = [\n  'a',\n  'button',\n  'div',\n  'form',\n  'h2',\n  'h3',\n  'img',\n  'input',\n  'label',\n  'li',\n  'nav',\n  'ol',\n  'p',\n  'select',\n  'span',\n  'svg',\n  'ul',\n] as const;\n\ntype Primitives = { [E in (typeof NODES)[number]]: PrimitiveForwardRefComponent<E> };\ntype PrimitivePropsWithRef<E extends React.ElementType> = React.ComponentPropsWithRef<E> & {\n  asChild?: boolean;\n};\n\ninterface PrimitiveForwardRefComponent<E extends React.ElementType>\n  extends React.ForwardRefExoticComponent<PrimitivePropsWithRef<E>> {}\n\n/* -------------------------------------------------------------------------------------------------\n * Primitive\n * -----------------------------------------------------------------------------------------------*/\n\nconst Primitive = NODES.reduce((primitive, node) => {\n  const Slot = createSlot(`Primitive.${node}`);\n  const Node = React.forwardRef((props: PrimitivePropsWithRef<typeof node>, forwardedRef: any) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp: any = asChild ? Slot : node;\n\n    if (typeof window !== 'undefined') {\n      (window as any)[Symbol.for('radix-ui')] = true;\n    }\n\n    return <Comp {...primitiveProps} ref={forwardedRef} />;\n  });\n\n  Node.displayName = `Primitive.${node}`;\n\n  return { ...primitive, [node]: Node };\n}, {} as Primitives);\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Flush custom event dispatch\n * https://github.com/radix-ui/primitives/pull/1378\n *\n * React batches *all* event handlers since version 18, this introduces certain considerations when using custom event types.\n *\n * Internally, React prioritises events in the following order:\n *  - discrete\n *  - continuous\n *  - default\n *\n * https://github.com/facebook/react/blob/a8a4742f1c54493df00da648a3f9d26e3db9c8b5/packages/react-dom/src/events/ReactDOMEventListener.js#L294-L350\n *\n * `discrete` is an  important distinction as updates within these events are applied immediately.\n * React however, is not able to infer the priority of custom event types due to how they are detected internally.\n * Because of this, it's possible for updates from custom events to be unexpectedly batched when\n * dispatched by another `discrete` event.\n *\n * In order to ensure that updates from custom events are applied predictably, we need to manually flush the batch.\n * This utility should be used when dispatching a custom event from within another `discrete` event, this utility\n * is not necessary when dispatching known event types, or if dispatching a custom type inside a non-discrete event.\n * For example:\n *\n * dispatching a known click 👎\n * target.dispatchEvent(new Event(‘click’))\n *\n * dispatching a custom type within a non-discrete event 👎\n * onScroll={(event) => event.target.dispatchEvent(new CustomEvent(‘customType’))}\n *\n * dispatching a custom type within a `discrete` event 👍\n * onPointerDown={(event) => dispatchDiscreteCustomEvent(event.target, new CustomEvent(‘customType’))}\n *\n * Note: though React classifies `focus`, `focusin` and `focusout` events as `discrete`, it's  not recommended to use\n * this utility with them. This is because it's possible for those handlers to be called implicitly during render\n * e.g. when focus is within a component as it is unmounted, or when managing focus on mount.\n */\n\nfunction dispatchDiscreteCustomEvent<E extends CustomEvent>(target: E['target'], event: E) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Primitive;\n\nexport {\n  Primitive,\n  //\n  Root,\n  //\n  dispatchDiscreteCustomEvent,\n};\nexport type { PrimitivePropsWithRef };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,SAAuB;AACvB,IAAAC,YAA0B;;;ACD1B,SAAS,MAAM,OAAe,CAAC,KAAK,GAAG,GAA6B;AAClE,SAAO,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC;AAC3C;;;ACFA,YAAuB;AACvB,eAA0B;AA6Cf,yBAAA;AA1CX,IAAM,QAAQ;EACZ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACF;AAcA,IAAM,YAAY,MAAM,OAAO,CAAC,WAAW,SAAS;AAClD,QAAMC,QAAO,WAAW,aAAa,IAAI,EAAE;AAC3C,QAAM,OAAa,iBAAW,CAAC,OAA2C,iBAAsB;AAC9F,UAAM,EAAE,SAAS,GAAG,eAAe,IAAI;AACvC,UAAM,OAAY,UAAUA,QAAO;AAEnC,QAAI,OAAO,WAAW,aAAa;AAChC,aAAe,OAAO,IAAI,UAAU,CAAC,IAAI;IAC5C;AAEA,eAAO,wBAAC,MAAA,EAAM,GAAG,gBAAgB,KAAK,aAAA,CAAc;EACtD,CAAC;AAED,OAAK,cAAc,aAAa,IAAI;AAEpC,SAAO,EAAE,GAAG,WAAW,CAAC,IAAI,GAAG,KAAK;AACtC,GAAG,CAAC,CAAe;;;AFoJT,IAAAC,sBAAA;AA3KV,IAAM,YAAY,CAAC,KAAK,SAAS,WAAW,WAAW;AACvD,IAAM,iBAAiB,CAAC,KAAK,OAAO;AAMpC,IAAM,cAAc;AAGpB,IAAM,CAAC,YAAY,eAAe,qBAAqB,IAAI,iBAGzD,WAAW;AAGb,IAAM,CAAC,qBAAqB,iBAAiB,IAAI,mBAAmB,aAAa;EAC/E;EACA;AACF,CAAC;AACD,IAAM,iBAAiB,kBAAkB;AAoBzC,IAAM,CAAC,gBAAgB,gBAAgB,IAAI,oBAAwC,WAAW;AAQ9F,IAAM,CAAC,6BAA6B,6BAA6B,IAC/D,oBAAqD,WAAW;AAoDlE,IAAM,SAAgC,CAAC,UAAoC;AACzE,QAAM;IACJ;IACA;IACA,MAAM;IACN;IACA;IACA,OAAO;IACP;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF,IAAI;AACJ,QAAM,cAAc,eAAe,aAAa;AAChD,QAAM,CAAC,SAAS,UAAU,IAAU,gBAAsC,IAAI;AAC9E,QAAM,CAAC,WAAW,YAAY,IAAU,gBAAoC,IAAI;AAChF,QAAM,CAAC,sBAAsB,uBAAuB,IAAU,gBAAS,KAAK;AAC5E,QAAM,YAAY,aAAa,GAAG;AAClC,QAAM,CAAC,MAAM,OAAO,IAAI,qBAAqB;IAC3C,MAAM;IACN,aAAa,eAAe;IAC5B,UAAU;IACV,QAAQ;EACV,CAAC;AACD,QAAM,CAAC,OAAO,QAAQ,IAAI,qBAAqB;IAC7C,MAAM;IACN,aAAa;IACb,UAAU;IACV,QAAQ;EACV,CAAC;AACD,QAAM,2BAAiC,cAAwC,IAAI;AAGnF,QAAM,gBAAgB,UAAU,QAAQ,CAAC,CAAC,QAAQ,QAAQ,MAAM,IAAI;AACpE,QAAM,CAAC,kBAAkB,mBAAmB,IAAU,gBAAS,oBAAI,IAAkB,CAAC;AAOtF,QAAM,kBAAkB,MAAM,KAAK,gBAAgB,EAChD,IAAI,CAAC,WAAW,OAAO,MAAM,KAAK,EAClC,KAAK,GAAG;AAEX,aACE,yBAAiB,OAAhB,EAAsB,GAAG,aACxB,cAAA;IAAC;IAAA;MACC;MACA,OAAO;MACP;MACA,iBAAiB;MACjB;MACA,mBAAmB;MACnB;MACA,8BAA8B;MAC9B,WAAW,MAAM;MACjB;MACA,eAAe;MACf;MACA,cAAc;MACd,KAAK;MACL;MACA;MAEA,UAAA;YAAA,yBAAC,WAAW,UAAX,EAAoB,OAAO,eAC1B,cAAA;UAAC;UAAA;YACC,OAAO,MAAM;YACb,mBAAyB,mBAAY,CAAC,WAAW;AAC/C,kCAAoB,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE,IAAI,MAAM,CAAC;YACzD,GAAG,CAAC,CAAC;YACL,sBAA4B,mBAAY,CAAC,WAAW;AAClD,kCAAoB,CAAC,SAAS;AAC5B,sBAAM,aAAa,IAAI,IAAI,IAAI;AAC/B,2BAAW,OAAO,MAAM;AACxB,uBAAO;cACT,CAAC;YACH,GAAG,CAAC,CAAC;YAEJ;UAAA;QACH,EAAA,CACF;QAEC,oBACC;UAAC;UAAA;YAEC,eAAW;YACX;YACA,UAAU;YACV;YACA;YACA;YAEA,UAAU,CAAC,UAAU,SAAS,MAAM,OAAO,KAAK;YAChD;YACA;YAEC,UAAA;cAAA,UAAU,aAAY,yBAAC,UAAA,EAAO,OAAM,GAAA,CAAG,IAAK;cAC5C,MAAM,KAAK,gBAAgB;YAAA;UAAA;UAbvB;QAcP,IACE;MAAA;IAAA;EACN,EAAA,CACF;AAEJ;AAEA,OAAO,cAAc;AAMrB,IAAM,eAAe;AAMrB,IAAM,gBAAsB;EAC1B,CAAC,OAAwC,iBAAiB;AACxD,UAAM,EAAE,eAAe,WAAW,OAAO,GAAG,aAAa,IAAI;AAC7D,UAAM,cAAc,eAAe,aAAa;AAChD,UAAM,UAAU,iBAAiB,cAAc,aAAa;AAC5D,UAAM,aAAa,QAAQ,YAAY;AACvC,UAAM,eAAe,gBAAgB,cAAc,QAAQ,eAAe;AAC1E,UAAM,WAAW,cAAc,aAAa;AAC5C,UAAM,iBAAuB,cAA0C,OAAO;AAE9E,UAAM,CAAC,WAAW,uBAAuB,cAAc,IAAI,mBAAmB,CAAC,WAAW;AACxF,YAAM,eAAe,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK,QAAQ;AAC/D,YAAM,cAAc,aAAa,KAAK,CAAC,SAAS,KAAK,UAAU,QAAQ,KAAK;AAC5E,YAAM,WAAW,aAAa,cAAc,QAAQ,WAAW;AAC/D,UAAI,aAAa,QAAW;AAC1B,gBAAQ,cAAc,SAAS,KAAK;MACtC;IACF,CAAC;AAED,UAAM,aAAa,CAAC,iBAAyD;AAC3E,UAAI,CAAC,YAAY;AACf,gBAAQ,aAAa,IAAI;AAEzB,uBAAe;MACjB;AAEA,UAAI,cAAc;AAChB,gBAAQ,yBAAyB,UAAU;UACzC,GAAG,KAAK,MAAM,aAAa,KAAK;UAChC,GAAG,KAAK,MAAM,aAAa,KAAK;QAClC;MACF;IACF;AAEA,eACE,yBAAiB,QAAhB,EAAuB,SAAO,MAAE,GAAG,aAClC,cAAA;MAAC,UAAU;MAAV;QACC,MAAK;QACL,MAAK;QACL,iBAAe,QAAQ;QACvB,iBAAe,QAAQ;QACvB,iBAAe,QAAQ;QACvB,qBAAkB;QAClB,KAAK,QAAQ;QACb,cAAY,QAAQ,OAAO,SAAS;QACpC,UAAU;QACV,iBAAe,aAAa,KAAK;QACjC,oBAAkB,sBAAsB,QAAQ,KAAK,IAAI,KAAK;QAC7D,GAAG;QACJ,KAAK;QAEL,SAAS,qBAAqB,aAAa,SAAS,CAAC,UAAU;AAM7D,gBAAM,cAAc,MAAM;AAG1B,cAAI,eAAe,YAAY,SAAS;AACtC,uBAAW,KAAK;UAClB;QACF,CAAC;QACD,eAAe,qBAAqB,aAAa,eAAe,CAAC,UAAU;AACzE,yBAAe,UAAU,MAAM;AAI/B,gBAAM,SAAS,MAAM;AACrB,cAAI,OAAO,kBAAkB,MAAM,SAAS,GAAG;AAC7C,mBAAO,sBAAsB,MAAM,SAAS;UAC9C;AAKA,cAAI,MAAM,WAAW,KAAK,MAAM,YAAY,SAAS,MAAM,gBAAgB,SAAS;AAClF,uBAAW,KAAK;AAEhB,kBAAM,eAAe;UACvB;QACF,CAAC;QACD,WAAW,qBAAqB,aAAa,WAAW,CAAC,UAAU;AACjE,gBAAM,gBAAgB,UAAU,YAAY;AAC5C,gBAAM,gBAAgB,MAAM,WAAW,MAAM,UAAU,MAAM;AAC7D,cAAI,CAAC,iBAAiB,MAAM,IAAI,WAAW;AAAG,kCAAsB,MAAM,GAAG;AAC7E,cAAI,iBAAiB,MAAM,QAAQ;AAAK;AACxC,cAAI,UAAU,SAAS,MAAM,GAAG,GAAG;AACjC,uBAAW;AACX,kBAAM,eAAe;UACvB;QACF,CAAC;MAAA;IACH,EAAA,CACF;EAEJ;AACF;AAEA,cAAc,cAAc;AAM5B,IAAM,aAAa;AAQnB,IAAM,cAAoB;EACxB,CAAC,OAAsC,iBAAiB;AAEtD,UAAM,EAAE,eAAe,WAAW,OAAO,UAAU,cAAc,IAAI,GAAG,WAAW,IAAI;AACvF,UAAM,UAAU,iBAAiB,YAAY,aAAa;AAC1D,UAAM,EAAE,6BAA6B,IAAI;AACzC,UAAM,cAAc,aAAa;AACjC,UAAM,eAAe,gBAAgB,cAAc,QAAQ,iBAAiB;AAE5E,qBAAgB,MAAM;AACpB,mCAA6B,WAAW;IAC1C,GAAG,CAAC,8BAA8B,WAAW,CAAC;AAE9C,eACE;MAAC,UAAU;MAAV;QACE,GAAG;QACJ,KAAK;QAGL,OAAO,EAAE,eAAe,OAAO;QAE9B,UAAA,sBAAsB,QAAQ,KAAK,QAAI,yBAAA,8BAAA,EAAG,UAAA,YAAA,CAAY,IAAM;MAAA;IAC/D;EAEJ;AACF;AAEA,YAAY,cAAc;AAM1B,IAAM,YAAY;AAKlB,IAAM,aAAmB;EACvB,CAAC,OAAqC,iBAAiB;AACrD,UAAM,EAAE,eAAe,UAAU,GAAG,UAAU,IAAI;AAClD,eACE,yBAAC,UAAU,MAAV,EAAe,eAAW,MAAE,GAAG,WAAW,KAAK,cAC7C,UAAA,YAAY,IAAA,CACf;EAEJ;AACF;AAEA,WAAW,cAAc;AAMzB,IAAM,cAAc;AAWpB,IAAM,eAA4C,CAAC,UAA0C;AAC3F,aAAO,yBAAC,QAAA,EAAgB,SAAO,MAAE,GAAG,MAAA,CAAO;AAC7C;AAEA,aAAa,cAAc;AAM3B,IAAM,eAAe;AAKrB,IAAM,gBAAsB;EAC1B,CAAC,OAAwC,iBAAiB;AACxD,UAAM,UAAU,iBAAiB,cAAc,MAAM,aAAa;AAClE,UAAM,CAAC,UAAU,WAAW,IAAU,gBAA2B;AAGjE,qBAAgB,MAAM;AACpB,kBAAY,IAAI,iBAAiB,CAAC;IACpC,GAAG,CAAC,CAAC;AAEL,QAAI,CAAC,QAAQ,MAAM;AACjB,YAAM,OAAO;AACb,aAAO,OACM;YACP,yBAAC,uBAAA,EAAsB,OAAO,MAAM,eAClC,cAAA,yBAAC,WAAW,MAAX,EAAgB,OAAO,MAAM,eAC5B,cAAA,yBAAC,OAAA,EAAK,UAAA,MAAM,SAAA,CAAS,EAAA,CACvB,EAAA,CACF;QACA;MACF,IACA;IACN;AAEA,eAAO,yBAAC,mBAAA,EAAmB,GAAG,OAAO,KAAK,aAAA,CAAc;EAC1D;AACF;AAEA,cAAc,cAAc;AAM5B,IAAM,iBAAiB;AAqBvB,IAAM,CAAC,uBAAuB,uBAAuB,IACnD,oBAA+C,YAAY;AAE7D,IAAM,oBAAoB;AA8B1B,IAAM,OAAO,WAAW,4BAA4B;AAEpD,IAAM,oBAA0B;EAC9B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM;MACJ;MACA,WAAW;MACX;MACA;MACA;;;MAGA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA,GAAG;IACL,IAAI;AACJ,UAAM,UAAU,iBAAiB,cAAc,aAAa;AAC5D,UAAM,CAAC,SAAS,UAAU,IAAU,gBAA0C,IAAI;AAClF,UAAM,CAAC,UAAU,WAAW,IAAU,gBAAuC,IAAI;AACjF,UAAM,eAAe,gBAAgB,cAAc,CAAC,SAAS,WAAW,IAAI,CAAC;AAC7E,UAAM,CAAC,cAAc,eAAe,IAAU,gBAAmC,IAAI;AACrF,UAAM,CAAC,kBAAkB,mBAAmB,IAAU;MACpD;IACF;AACA,UAAM,WAAW,cAAc,aAAa;AAC5C,UAAM,CAAC,cAAc,eAAe,IAAU,gBAAS,KAAK;AAC5D,UAAM,yBAA+B,cAAO,KAAK;AAG3C,IAAA,iBAAU,MAAM;AACpB,UAAI;AAAS,eAAO,WAAW,OAAO;IACxC,GAAG,CAAC,OAAO,CAAC;AAIZ,mBAAe;AAEf,UAAM,aAAmB;MACvB,CAAC,eAA0C;AACzC,cAAM,CAAC,WAAW,GAAG,SAAS,IAAI,SAAS,EAAE,IAAI,CAAC,SAAS,KAAK,IAAI,OAAO;AAC3E,cAAM,CAAC,QAAQ,IAAI,UAAU,MAAM,EAAE;AAErC,cAAM,6BAA6B,SAAS;AAC5C,mBAAW,aAAa,YAAY;AAElC,cAAI,cAAc;AAA4B;AAC9C,iDAAW,eAAe,EAAE,OAAO,UAAU;AAE7C,cAAI,cAAc,aAAa;AAAU,qBAAS,YAAY;AAC9D,cAAI,cAAc,YAAY;AAAU,qBAAS,YAAY,SAAS;AACtE,iDAAW;AACX,cAAI,SAAS,kBAAkB;AAA4B;QAC7D;MACF;MACA,CAAC,UAAU,QAAQ;IACrB;AAEA,UAAM,oBAA0B;MAC9B,MAAM,WAAW,CAAC,cAAc,OAAO,CAAC;MACxC,CAAC,YAAY,cAAc,OAAO;IACpC;AAIM,IAAA,iBAAU,MAAM;AACpB,UAAI,cAAc;AAChB,0BAAkB;MACpB;IACF,GAAG,CAAC,cAAc,iBAAiB,CAAC;AAIpC,UAAM,EAAE,cAAc,yBAAyB,IAAI;AAC7C,IAAA,iBAAU,MAAM;AACpB,UAAI,SAAS;AACX,YAAI,mBAAmB,EAAE,GAAG,GAAG,GAAG,EAAE;AAEpC,cAAM,oBAAoB,CAAC,UAAwB;;AACjD,6BAAmB;YACjB,GAAG,KAAK,IAAI,KAAK,MAAM,MAAM,KAAK,OAAK,8BAAyB,YAAzB,mBAAkC,MAAK,EAAE;YAChF,GAAG,KAAK,IAAI,KAAK,MAAM,MAAM,KAAK,OAAK,8BAAyB,YAAzB,mBAAkC,MAAK,EAAE;UAClF;QACF;AACA,cAAM,kBAAkB,CAAC,UAAwB;AAE/C,cAAI,iBAAiB,KAAK,MAAM,iBAAiB,KAAK,IAAI;AACxD,kBAAM,eAAe;UACvB,OAAO;AAEL,gBAAI,CAAC,QAAQ,SAAS,MAAM,MAAqB,GAAG;AAClD,2BAAa,KAAK;YACpB;UACF;AACA,mBAAS,oBAAoB,eAAe,iBAAiB;AAC7D,mCAAyB,UAAU;QACrC;AAEA,YAAI,yBAAyB,YAAY,MAAM;AAC7C,mBAAS,iBAAiB,eAAe,iBAAiB;AAC1D,mBAAS,iBAAiB,aAAa,iBAAiB,EAAE,SAAS,MAAM,MAAM,KAAK,CAAC;QACvF;AAEA,eAAO,MAAM;AACX,mBAAS,oBAAoB,eAAe,iBAAiB;AAC7D,mBAAS,oBAAoB,aAAa,iBAAiB,EAAE,SAAS,KAAK,CAAC;QAC9E;MACF;IACF,GAAG,CAAC,SAAS,cAAc,wBAAwB,CAAC;AAE9C,IAAA,iBAAU,MAAM;AACpB,YAAM,QAAQ,MAAM,aAAa,KAAK;AACtC,aAAO,iBAAiB,QAAQ,KAAK;AACrC,aAAO,iBAAiB,UAAU,KAAK;AACvC,aAAO,MAAM;AACX,eAAO,oBAAoB,QAAQ,KAAK;AACxC,eAAO,oBAAoB,UAAU,KAAK;MAC5C;IACF,GAAG,CAAC,YAAY,CAAC;AAEjB,UAAM,CAAC,WAAW,qBAAqB,IAAI,mBAAmB,CAAC,WAAW;AACxE,YAAM,eAAe,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK,QAAQ;AAC/D,YAAM,cAAc,aAAa,KAAK,CAAC,SAAS,KAAK,IAAI,YAAY,SAAS,aAAa;AAC3F,YAAM,WAAW,aAAa,cAAc,QAAQ,WAAW;AAC/D,UAAI,UAAU;AAKZ,mBAAW,MAAO,SAAS,IAAI,QAAwB,MAAM,CAAC;MAChE;IACF,CAAC;AAED,UAAM,kBAAwB;MAC5B,CAAC,MAAgC,OAAe,aAAsB;AACpE,cAAM,mBAAmB,CAAC,uBAAuB,WAAW,CAAC;AAC7D,cAAM,iBAAiB,QAAQ,UAAU,UAAa,QAAQ,UAAU;AACxE,YAAI,kBAAkB,kBAAkB;AACtC,0BAAgB,IAAI;AACpB,cAAI;AAAkB,mCAAuB,UAAU;QACzD;MACF;MACA,CAAC,QAAQ,KAAK;IAChB;AACA,UAAM,kBAAwB,mBAAY,MAAM,mCAAS,SAAS,CAAC,OAAO,CAAC;AAC3E,UAAM,sBAA4B;MAChC,CAAC,MAAoC,OAAe,aAAsB;AACxE,cAAM,mBAAmB,CAAC,uBAAuB,WAAW,CAAC;AAC7D,cAAM,iBAAiB,QAAQ,UAAU,UAAa,QAAQ,UAAU;AACxE,YAAI,kBAAkB,kBAAkB;AACtC,8BAAoB,IAAI;QAC1B;MACF;MACA,CAAC,QAAQ,KAAK;IAChB;AAEA,UAAM,iBAAiB,aAAa,WAAW,uBAAuB;AAGtE,UAAM,qBACJ,mBAAmB,uBACf;MACE;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACF,IACA,CAAC;AAEP,eACE;MAAC;MAAA;QACC,OAAO;QACP;QACA;QACA,kBAAkB;QAClB;QACA;QACA,aAAa;QACb;QACA;QACA;QACA;QACA;QACA;QAEA,cAAA,yBAAC,qBAAA,EAAa,IAAI,MAAM,gBAAc,MACpC,cAAA;UAAC;UAAA;YACC,SAAO;YAGP,SAAS,QAAQ;YACjB,kBAAkB,CAAC,UAAU;AAE3B,oBAAM,eAAe;YACvB;YACA,oBAAoB,qBAAqB,kBAAkB,CAAC,UAAU;;AACpE,4BAAQ,YAAR,mBAAiB,MAAM,EAAE,eAAe,KAAK;AAC7C,oBAAM,eAAe;YACvB,CAAC;YAED,cAAA;cAAC;cAAA;gBACC,SAAO;gBACP,6BAA2B;gBAC3B;gBACA;gBAGA,gBAAgB,CAAC,UAAU,MAAM,eAAe;gBAChD,WAAW,MAAM,QAAQ,aAAa,KAAK;gBAE3C,cAAA;kBAAC;kBAAA;oBACC,MAAK;oBACL,IAAI,QAAQ;oBACZ,cAAY,QAAQ,OAAO,SAAS;oBACpC,KAAK,QAAQ;oBACb,eAAe,CAAC,UAAU,MAAM,eAAe;oBAC9C,GAAG;oBACH,GAAG;oBACJ,UAAU,MAAM,gBAAgB,IAAI;oBACpC,KAAK;oBACL,OAAO;;sBAEL,SAAS;sBACT,eAAe;;sBAEf,SAAS;sBACT,GAAG,aAAa;oBAClB;oBACA,WAAW,qBAAqB,aAAa,WAAW,CAAC,UAAU;AACjE,4BAAM,gBAAgB,MAAM,WAAW,MAAM,UAAU,MAAM;AAG7D,0BAAI,MAAM,QAAQ;AAAO,8BAAM,eAAe;AAE9C,0BAAI,CAAC,iBAAiB,MAAM,IAAI,WAAW;AAAG,8CAAsB,MAAM,GAAG;AAE7E,0BAAI,CAAC,WAAW,aAAa,QAAQ,KAAK,EAAE,SAAS,MAAM,GAAG,GAAG;AAC/D,8BAAM,QAAQ,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK,QAAQ;AACxD,4BAAI,iBAAiB,MAAM,IAAI,CAAC,SAAS,KAAK,IAAI,OAAQ;AAE1D,4BAAI,CAAC,WAAW,KAAK,EAAE,SAAS,MAAM,GAAG,GAAG;AAC1C,2CAAiB,eAAe,MAAM,EAAE,QAAQ;wBAClD;AACA,4BAAI,CAAC,WAAW,WAAW,EAAE,SAAS,MAAM,GAAG,GAAG;AAChD,gCAAM,iBAAiB,MAAM;AAC7B,gCAAM,eAAe,eAAe,QAAQ,cAAc;AAC1D,2CAAiB,eAAe,MAAM,eAAe,CAAC;wBACxD;AAMA,mCAAW,MAAM,WAAW,cAAc,CAAC;AAE3C,8BAAM,eAAe;sBACvB;oBACF,CAAC;kBAAA;gBACH;cAAA;YACF;UAAA;QACF,EAAA,CACF;MAAA;IACF;EAEJ;AACF;AAEA,kBAAkB,cAAc;AAMhC,IAAM,6BAA6B;AAKnC,IAAM,4BAAkC,kBAGtC,CAAC,OAAoD,iBAAiB;AACtE,QAAM,EAAE,eAAe,UAAU,GAAG,YAAY,IAAI;AACpD,QAAM,UAAU,iBAAiB,cAAc,aAAa;AAC5D,QAAM,iBAAiB,wBAAwB,cAAc,aAAa;AAC1E,QAAM,CAAC,gBAAgB,iBAAiB,IAAU,gBAAgC,IAAI;AACtF,QAAM,CAAC,SAAS,UAAU,IAAU,gBAAkD,IAAI;AAC1F,QAAM,eAAe,gBAAgB,cAAc,CAAC,SAAS,WAAW,IAAI,CAAC;AAC7E,QAAM,WAAW,cAAc,aAAa;AAC5C,QAAM,0BAAgC,cAAO,KAAK;AAClD,QAAM,sBAA4B,cAAO,IAAI;AAE7C,QAAM,EAAE,UAAU,cAAc,kBAAkB,kBAAkB,IAAI;AACxE,QAAM,WAAiB,mBAAY,MAAM;AACvC,QACE,QAAQ,WACR,QAAQ,aACR,kBACA,WACA,YACA,gBACA,kBACA;AACA,YAAM,cAAc,QAAQ,QAAQ,sBAAsB;AAK1D,YAAM,cAAc,QAAQ,sBAAsB;AAClD,YAAM,gBAAgB,QAAQ,UAAU,sBAAsB;AAC9D,YAAM,eAAe,iBAAiB,sBAAsB;AAE5D,UAAI,QAAQ,QAAQ,OAAO;AACzB,cAAM,iBAAiB,aAAa,OAAO,YAAY;AACvD,cAAM,OAAO,cAAc,OAAO;AAClC,cAAM,YAAY,YAAY,OAAO;AACrC,cAAM,kBAAkB,YAAY,QAAQ;AAC5C,cAAM,eAAe,KAAK,IAAI,iBAAiB,YAAY,KAAK;AAChE,cAAM,YAAY,OAAO,aAAa;AACtC,cAAM,cAAc,MAAM,MAAM;UAC9B;;;;;;UAMA,KAAK,IAAI,gBAAgB,YAAY,YAAY;QACnD,CAAC;AAED,uBAAe,MAAM,WAAW,kBAAkB;AAClD,uBAAe,MAAM,OAAO,cAAc;MAC5C,OAAO;AACL,cAAM,iBAAiB,YAAY,QAAQ,aAAa;AACxD,cAAM,QAAQ,OAAO,aAAa,cAAc,QAAQ;AACxD,cAAM,aAAa,OAAO,aAAa,YAAY,QAAQ;AAC3D,cAAM,kBAAkB,YAAY,QAAQ;AAC5C,cAAM,eAAe,KAAK,IAAI,iBAAiB,YAAY,KAAK;AAChE,cAAM,WAAW,OAAO,aAAa;AACrC,cAAM,eAAe,MAAM,OAAO;UAChC;UACA,KAAK,IAAI,gBAAgB,WAAW,YAAY;QAClD,CAAC;AAED,uBAAe,MAAM,WAAW,kBAAkB;AAClD,uBAAe,MAAM,QAAQ,eAAe;MAC9C;AAKA,YAAM,QAAQ,SAAS;AACvB,YAAM,kBAAkB,OAAO,cAAc,iBAAiB;AAC9D,YAAM,cAAc,SAAS;AAE7B,YAAM,gBAAgB,OAAO,iBAAiB,OAAO;AACrD,YAAM,wBAAwB,SAAS,cAAc,gBAAgB,EAAE;AACvE,YAAM,oBAAoB,SAAS,cAAc,YAAY,EAAE;AAC/D,YAAM,2BAA2B,SAAS,cAAc,mBAAmB,EAAE;AAC7E,YAAM,uBAAuB,SAAS,cAAc,eAAe,EAAE;AACrE,YAAM,oBAAoB,wBAAwB,oBAAoB,cAAc,uBAAuB;AAC3G,YAAM,mBAAmB,KAAK,IAAI,aAAa,eAAe,GAAG,iBAAiB;AAElF,YAAM,iBAAiB,OAAO,iBAAiB,QAAQ;AACvD,YAAM,qBAAqB,SAAS,eAAe,YAAY,EAAE;AACjE,YAAM,wBAAwB,SAAS,eAAe,eAAe,EAAE;AAEvE,YAAM,yBAAyB,YAAY,MAAM,YAAY,SAAS,IAAI;AAC1E,YAAM,4BAA4B,kBAAkB;AAEpD,YAAM,yBAAyB,aAAa,eAAe;AAC3D,YAAM,mBAAmB,aAAa,YAAY;AAClD,YAAM,yBAAyB,wBAAwB,oBAAoB;AAC3E,YAAM,4BAA4B,oBAAoB;AAEtD,YAAM,8BAA8B,0BAA0B;AAE9D,UAAI,6BAA6B;AAC/B,cAAM,aACJ,MAAM,SAAS,KAAK,iBAAiB,MAAM,MAAM,SAAS,CAAC,EAAG,IAAI;AACpE,uBAAe,MAAM,SAAS;AAC9B,cAAM,uBACJ,QAAQ,eAAe,SAAS,YAAY,SAAS;AACvD,cAAM,mCAAmC,KAAK;UAC5C;UACA;WAEG,aAAa,wBAAwB,KACtC,uBACA;QACJ;AACA,cAAM,SAAS,yBAAyB;AACxC,uBAAe,MAAM,SAAS,SAAS;MACzC,OAAO;AACL,cAAM,cAAc,MAAM,SAAS,KAAK,iBAAiB,MAAM,CAAC,EAAG,IAAI;AACvE,uBAAe,MAAM,MAAM;AAC3B,cAAM,gCAAgC,KAAK;UACzC;UACA,wBACE,SAAS;WAER,cAAc,qBAAqB,KACpC;QACJ;AACA,cAAM,SAAS,gCAAgC;AAC/C,uBAAe,MAAM,SAAS,SAAS;AACvC,iBAAS,YAAY,yBAAyB,yBAAyB,SAAS;MAClF;AAEA,qBAAe,MAAM,SAAS,GAAG,cAAc;AAC/C,qBAAe,MAAM,YAAY,mBAAmB;AACpD,qBAAe,MAAM,YAAY,kBAAkB;AAGnD;AAIA,4BAAsB,MAAO,wBAAwB,UAAU,IAAK;IACtE;EACF,GAAG;IACD;IACA,QAAQ;IACR,QAAQ;IACR;IACA;IACA;IACA;IACA;IACA,QAAQ;IACR;EACF,CAAC;AAED,mBAAgB,MAAM,SAAS,GAAG,CAAC,QAAQ,CAAC;AAG5C,QAAM,CAAC,eAAe,gBAAgB,IAAU,gBAAiB;AACjE,mBAAgB,MAAM;AACpB,QAAI;AAAS,uBAAiB,OAAO,iBAAiB,OAAO,EAAE,MAAM;EACvE,GAAG,CAAC,OAAO,CAAC;AAMZ,QAAM,2BAAiC;IACrC,CAAC,SAA+C;AAC9C,UAAI,QAAQ,oBAAoB,YAAY,MAAM;AAChD,iBAAS;AACT;AACA,4BAAoB,UAAU;MAChC;IACF;IACA,CAAC,UAAU,iBAAiB;EAC9B;AAEA,aACE;IAAC;IAAA;MACC,OAAO;MACP;MACA;MACA,sBAAsB;MAEtB,cAAA;QAAC;QAAA;UACC,KAAK;UACL,OAAO;YACL,SAAS;YACT,eAAe;YACf,UAAU;YACV,QAAQ;UACV;UAEA,cAAA;YAAC,UAAU;YAAV;cACE,GAAG;cACJ,KAAK;cACL,OAAO;;;gBAGL,WAAW;;gBAEX,WAAW;gBACX,GAAG,YAAY;cACjB;YAAA;UACF;QAAA;MACF;IAAA;EACF;AAEJ,CAAC;AAED,0BAA0B,cAAc;AAMxC,IAAM,uBAAuB;AAM7B,IAAM,uBAA6B,kBAGjC,CAAC,OAA+C,iBAAiB;AACjE,QAAM;IACJ;IACA,QAAQ;IACR,mBAAmB;IACnB,GAAG;EACL,IAAI;AACJ,QAAM,cAAc,eAAe,aAAa;AAEhD,aACE;IAAiB;IAAhB;MACE,GAAG;MACH,GAAG;MACJ,KAAK;MACL;MACA;MACA,OAAO;;QAEL,WAAW;QACX,GAAG,YAAY;;QAEf,GAAG;UACD,2CAA2C;UAC3C,0CAA0C;UAC1C,2CAA2C;UAC3C,gCAAgC;UAChC,iCAAiC;QACnC;MACF;IAAA;EACF;AAEJ,CAAC;AAED,qBAAqB,cAAc;AAYnC,IAAM,CAAC,wBAAwB,wBAAwB,IACrD,oBAAgD,cAAc,CAAC,CAAC;AAElE,IAAM,gBAAgB;AAQtB,IAAM,iBAAuB;EAC3B,CAAC,OAAyC,iBAAiB;AACzD,UAAM,EAAE,eAAe,OAAO,GAAG,cAAc,IAAI;AACnD,UAAM,iBAAiB,wBAAwB,eAAe,aAAa;AAC3E,UAAM,kBAAkB,yBAAyB,eAAe,aAAa;AAC7E,UAAM,eAAe,gBAAgB,cAAc,eAAe,gBAAgB;AAClF,UAAM,mBAAyB,cAAO,CAAC;AACvC,eACE,0BAAA,8BAAA,EAEE,UAAA;UAAA;QAAC;QAAA;UACC,yBAAyB;YACvB,QAAQ;UACV;UACA;QAAA;MACF;UACA,yBAAC,WAAW,MAAX,EAAgB,OAAO,eACtB,cAAA;QAAC,UAAU;QAAV;UACC,8BAA2B;UAC3B,MAAK;UACJ,GAAG;UACJ,KAAK;UACL,OAAO;;;;YAIL,UAAU;YACV,MAAM;;;;;YAKN,UAAU;YACV,GAAG,cAAc;UACnB;UACA,UAAU,qBAAqB,cAAc,UAAU,CAAC,UAAU;AAChE,kBAAM,WAAW,MAAM;AACvB,kBAAM,EAAE,gBAAgB,wBAAwB,IAAI;AACpD,iBAAI,mEAAyB,YAAW,gBAAgB;AACtD,oBAAM,aAAa,KAAK,IAAI,iBAAiB,UAAU,SAAS,SAAS;AACzE,kBAAI,aAAa,GAAG;AAClB,sBAAM,kBAAkB,OAAO,cAAc,iBAAiB;AAC9D,sBAAM,eAAe,WAAW,eAAe,MAAM,SAAS;AAC9D,sBAAM,YAAY,WAAW,eAAe,MAAM,MAAM;AACxD,sBAAM,aAAa,KAAK,IAAI,cAAc,SAAS;AAEnD,oBAAI,aAAa,iBAAiB;AAChC,wBAAM,aAAa,aAAa;AAChC,wBAAM,oBAAoB,KAAK,IAAI,iBAAiB,UAAU;AAC9D,wBAAM,aAAa,aAAa;AAEhC,iCAAe,MAAM,SAAS,oBAAoB;AAClD,sBAAI,eAAe,MAAM,WAAW,OAAO;AACzC,6BAAS,YAAY,aAAa,IAAI,aAAa;AAEnD,mCAAe,MAAM,iBAAiB;kBACxC;gBACF;cACF;YACF;AACA,6BAAiB,UAAU,SAAS;UACtC,CAAC;QAAA;MACH,EAAA,CACF;IAAA,EAAA,CACF;EAEJ;AACF;AAEA,eAAe,cAAc;AAM7B,IAAM,aAAa;AAInB,IAAM,CAAC,4BAA4B,qBAAqB,IACtD,oBAA6C,UAAU;AAKzD,IAAM,cAAoB;EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,EAAE,eAAe,GAAG,WAAW,IAAI;AACzC,UAAM,UAAU,MAAM;AACtB,eACE,yBAAC,4BAAA,EAA2B,OAAO,eAAe,IAAI,SACpD,cAAA,yBAAC,UAAU,KAAV,EAAc,MAAK,SAAQ,mBAAiB,SAAU,GAAG,YAAY,KAAK,aAAA,CAAc,EAAA,CAC3F;EAEJ;AACF;AAEA,YAAY,cAAc;AAM1B,IAAM,aAAa;AAKnB,IAAM,cAAoB;EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,EAAE,eAAe,GAAG,WAAW,IAAI;AACzC,UAAM,eAAe,sBAAsB,YAAY,aAAa;AACpE,eAAO,yBAAC,UAAU,KAAV,EAAc,IAAI,aAAa,IAAK,GAAG,YAAY,KAAK,aAAA,CAAc;EAChF;AACF;AAEA,YAAY,cAAc;AAM1B,IAAM,YAAY;AAUlB,IAAM,CAAC,2BAA2B,oBAAoB,IACpD,oBAA4C,SAAS;AASvD,IAAM,aAAmB;EACvB,CAAC,OAAqC,iBAAiB;AACrD,UAAM;MACJ;MACA;MACA,WAAW;MACX,WAAW;MACX,GAAG;IACL,IAAI;AACJ,UAAM,UAAU,iBAAiB,WAAW,aAAa;AACzD,UAAM,iBAAiB,wBAAwB,WAAW,aAAa;AACvE,UAAM,aAAa,QAAQ,UAAU;AACrC,UAAM,CAAC,WAAW,YAAY,IAAU,gBAAS,iBAAiB,EAAE;AACpE,UAAM,CAAC,WAAW,YAAY,IAAU,gBAAS,KAAK;AACtD,UAAM,eAAe;MAAgB;MAAc,CAAC,SAAA;;AAClD,oCAAe,oBAAf,wCAAiC,MAAM,OAAO;;IAChD;AACA,UAAM,SAAS,MAAM;AACrB,UAAM,iBAAuB,cAA0C,OAAO;AAE9E,UAAM,eAAe,MAAM;AACzB,UAAI,CAAC,UAAU;AACb,gBAAQ,cAAc,KAAK;AAC3B,gBAAQ,aAAa,KAAK;MAC5B;IACF;AAEA,QAAI,UAAU,IAAI;AAChB,YAAM,IAAI;QACR;MACF;IACF;AAEA,eACE;MAAC;MAAA;QACC,OAAO;QACP;QACA;QACA;QACA;QACA,kBAAwB,mBAAY,CAAC,SAAS;AAC5C,uBAAa,CAAC,kBAAkB,mBAAkB,6BAAM,gBAAe,IAAI,KAAK,CAAC;QACnF,GAAG,CAAC,CAAC;QAEL,cAAA;UAAC,WAAW;UAAX;YACC,OAAO;YACP;YACA;YACA;YAEA,cAAA;cAAC,UAAU;cAAV;gBACC,MAAK;gBACL,mBAAiB;gBACjB,oBAAkB,YAAY,KAAK;gBAEnC,iBAAe,cAAc;gBAC7B,cAAY,aAAa,YAAY;gBACrC,iBAAe,YAAY;gBAC3B,iBAAe,WAAW,KAAK;gBAC/B,UAAU,WAAW,SAAY;gBAChC,GAAG;gBACJ,KAAK;gBACL,SAAS,qBAAqB,UAAU,SAAS,MAAM,aAAa,IAAI,CAAC;gBACzE,QAAQ,qBAAqB,UAAU,QAAQ,MAAM,aAAa,KAAK,CAAC;gBACxE,SAAS,qBAAqB,UAAU,SAAS,MAAM;AAErD,sBAAI,eAAe,YAAY;AAAS,iCAAa;gBACvD,CAAC;gBACD,aAAa,qBAAqB,UAAU,aAAa,MAAM;AAG7D,sBAAI,eAAe,YAAY;AAAS,iCAAa;gBACvD,CAAC;gBACD,eAAe,qBAAqB,UAAU,eAAe,CAAC,UAAU;AACtE,iCAAe,UAAU,MAAM;gBACjC,CAAC;gBACD,eAAe,qBAAqB,UAAU,eAAe,CAAC,UAAU;;AAEtE,iCAAe,UAAU,MAAM;AAC/B,sBAAI,UAAU;AACZ,yCAAe,gBAAf;kBACF,WAAW,eAAe,YAAY,SAAS;AAG7C,0BAAM,cAAc,MAAM,EAAE,eAAe,KAAK,CAAC;kBACnD;gBACF,CAAC;gBACD,gBAAgB,qBAAqB,UAAU,gBAAgB,CAAC,UAAU;;AACxE,sBAAI,MAAM,kBAAkB,SAAS,eAAe;AAClD,yCAAe,gBAAf;kBACF;gBACF,CAAC;gBACD,WAAW,qBAAqB,UAAU,WAAW,CAAC,UAAU;;AAC9D,wBAAM,kBAAgB,oBAAe,cAAf,mBAA0B,aAAY;AAC5D,sBAAI,iBAAiB,MAAM,QAAQ;AAAK;AACxC,sBAAI,eAAe,SAAS,MAAM,GAAG;AAAG,iCAAa;AAErD,sBAAI,MAAM,QAAQ;AAAK,0BAAM,eAAe;gBAC9C,CAAC;cAAA;YACH;UAAA;QACF;MAAA;IACF;EAEJ;AACF;AAEA,WAAW,cAAc;AAMzB,IAAM,iBAAiB;AAKvB,IAAM,iBAAuB;EAC3B,CAAC,OAAyC,iBAAiB;AAEzD,UAAM,EAAE,eAAe,WAAW,OAAO,GAAG,cAAc,IAAI;AAC9D,UAAM,UAAU,iBAAiB,gBAAgB,aAAa;AAC9D,UAAM,iBAAiB,wBAAwB,gBAAgB,aAAa;AAC5E,UAAM,cAAc,qBAAqB,gBAAgB,aAAa;AACtE,UAAM,uBAAuB,8BAA8B,gBAAgB,aAAa;AACxF,UAAM,CAAC,cAAc,eAAe,IAAU,gBAAuC,IAAI;AACzF,UAAM,eAAe;MACnB;MACA,CAAC,SAAS,gBAAgB,IAAI;MAC9B,YAAY;MACZ,CAAC,SAAA;;AAAS,oCAAe,wBAAf,wCAAqC,MAAM,YAAY,OAAO,YAAY;;IACtF;AAEA,UAAM,cAAc,6CAAc;AAClC,UAAM,eAAqB;MACzB,UACE,yBAAC,UAAA,EAA+B,OAAO,YAAY,OAAO,UAAU,YAAY,UAC7E,UAAA,YAAA,GADU,YAAY,KAEzB;MAEF,CAAC,YAAY,UAAU,YAAY,OAAO,WAAW;IACvD;AAEA,UAAM,EAAE,mBAAmB,qBAAqB,IAAI;AACpD,qBAAgB,MAAM;AACpB,wBAAkB,YAAY;AAC9B,aAAO,MAAM,qBAAqB,YAAY;IAChD,GAAG,CAAC,mBAAmB,sBAAsB,YAAY,CAAC;AAE1D,eACE,0BAAA,8BAAA,EACE,UAAA;UAAA,yBAAC,UAAU,MAAV,EAAe,IAAI,YAAY,QAAS,GAAG,eAAe,KAAK,aAAA,CAAc;MAG7E,YAAY,cAAc,QAAQ,aAAa,CAAC,QAAQ,uBAC5C,uBAAa,cAAc,UAAU,QAAQ,SAAS,IAC/D;IAAA,EAAA,CACN;EAEJ;AACF;AAEA,eAAe,cAAc;AAM7B,IAAM,sBAAsB;AAK5B,IAAM,sBAA4B;EAChC,CAAC,OAA8C,iBAAiB;AAC9D,UAAM,EAAE,eAAe,GAAG,mBAAmB,IAAI;AACjD,UAAM,cAAc,qBAAqB,qBAAqB,aAAa;AAC3E,WAAO,YAAY,iBACjB,yBAAC,UAAU,MAAV,EAAe,eAAW,MAAE,GAAG,oBAAoB,KAAK,aAAA,CAAc,IACrE;EACN;AACF;AAEA,oBAAoB,cAAc;AAMlC,IAAM,wBAAwB;AAK9B,IAAM,uBAA6B,kBAGjC,CAAC,OAA+C,iBAAiB;AACjE,QAAM,iBAAiB,wBAAwB,uBAAuB,MAAM,aAAa;AACzF,QAAM,kBAAkB,yBAAyB,uBAAuB,MAAM,aAAa;AAC3F,QAAM,CAAC,aAAa,cAAc,IAAU,gBAAS,KAAK;AAC1D,QAAM,eAAe,gBAAgB,cAAc,gBAAgB,oBAAoB;AAEvF,mBAAgB,MAAM;AACpB,QAAI,eAAe,YAAY,eAAe,cAAc;AAE1D,UAASC,gBAAT,WAAwB;AACtB,cAAMC,eAAc,SAAS,YAAY;AACzC,uBAAeA,YAAW;MAC5B;AAHS,UAAA,eAAAD;AADT,YAAM,WAAW,eAAe;AAKhCA,oBAAa;AACb,eAAS,iBAAiB,UAAUA,aAAY;AAChD,aAAO,MAAM,SAAS,oBAAoB,UAAUA,aAAY;IAClE;EACF,GAAG,CAAC,eAAe,UAAU,eAAe,YAAY,CAAC;AAEzD,SAAO,kBACL;IAAC;IAAA;MACE,GAAG;MACJ,KAAK;MACL,cAAc,MAAM;AAClB,cAAM,EAAE,UAAU,aAAa,IAAI;AACnC,YAAI,YAAY,cAAc;AAC5B,mBAAS,YAAY,SAAS,YAAY,aAAa;QACzD;MACF;IAAA;EACF,IACE;AACN,CAAC;AAED,qBAAqB,cAAc;AAMnC,IAAM,0BAA0B;AAKhC,IAAM,yBAA+B,kBAGnC,CAAC,OAAiD,iBAAiB;AACnE,QAAM,iBAAiB,wBAAwB,yBAAyB,MAAM,aAAa;AAC3F,QAAM,kBAAkB,yBAAyB,yBAAyB,MAAM,aAAa;AAC7F,QAAM,CAAC,eAAe,gBAAgB,IAAU,gBAAS,KAAK;AAC9D,QAAM,eAAe,gBAAgB,cAAc,gBAAgB,oBAAoB;AAEvF,mBAAgB,MAAM;AACpB,QAAI,eAAe,YAAY,eAAe,cAAc;AAE1D,UAASA,gBAAT,WAAwB;AACtB,cAAM,YAAY,SAAS,eAAe,SAAS;AAGnD,cAAME,iBAAgB,KAAK,KAAK,SAAS,SAAS,IAAI;AACtD,yBAAiBA,cAAa;MAChC;AANS,UAAA,eAAAF;AADT,YAAM,WAAW,eAAe;AAQhCA,oBAAa;AACb,eAAS,iBAAiB,UAAUA,aAAY;AAChD,aAAO,MAAM,SAAS,oBAAoB,UAAUA,aAAY;IAClE;EACF,GAAG,CAAC,eAAe,UAAU,eAAe,YAAY,CAAC;AAEzD,SAAO,oBACL;IAAC;IAAA;MACE,GAAG;MACJ,KAAK;MACL,cAAc,MAAM;AAClB,cAAM,EAAE,UAAU,aAAa,IAAI;AACnC,YAAI,YAAY,cAAc;AAC5B,mBAAS,YAAY,SAAS,YAAY,aAAa;QACzD;MACF;IAAA;EACF,IACE;AACN,CAAC;AAED,uBAAuB,cAAc;AAOrC,IAAM,yBAA+B,kBAGnC,CAAC,OAAiD,iBAAiB;AACnE,QAAM,EAAE,eAAe,cAAc,GAAG,qBAAqB,IAAI;AACjE,QAAM,iBAAiB,wBAAwB,sBAAsB,aAAa;AAClF,QAAM,qBAA2B,cAAsB,IAAI;AAC3D,QAAM,WAAW,cAAc,aAAa;AAE5C,QAAM,uBAA6B,mBAAY,MAAM;AACnD,QAAI,mBAAmB,YAAY,MAAM;AACvC,aAAO,cAAc,mBAAmB,OAAO;AAC/C,yBAAmB,UAAU;IAC/B;EACF,GAAG,CAAC,CAAC;AAEC,EAAA,iBAAU,MAAM;AACpB,WAAO,MAAM,qBAAqB;EACpC,GAAG,CAAC,oBAAoB,CAAC;AAMzB,mBAAgB,MAAM;;AACpB,UAAM,aAAa,SAAS,EAAE,KAAK,CAAC,SAAS,KAAK,IAAI,YAAY,SAAS,aAAa;AACxF,mDAAY,IAAI,YAAhB,mBAAyB,eAAe,EAAE,OAAO,UAAU;EAC7D,GAAG,CAAC,QAAQ,CAAC;AAEb,aACE;IAAC,UAAU;IAAV;MACC,eAAW;MACV,GAAG;MACJ,KAAK;MACL,OAAO,EAAE,YAAY,GAAG,GAAG,qBAAqB,MAAM;MACtD,eAAe,qBAAqB,qBAAqB,eAAe,MAAM;AAC5E,YAAI,mBAAmB,YAAY,MAAM;AACvC,6BAAmB,UAAU,OAAO,YAAY,cAAc,EAAE;QAClE;MACF,CAAC;MACD,eAAe,qBAAqB,qBAAqB,eAAe,MAAM;;AAC5E,6BAAe,gBAAf;AACA,YAAI,mBAAmB,YAAY,MAAM;AACvC,6BAAmB,UAAU,OAAO,YAAY,cAAc,EAAE;QAClE;MACF,CAAC;MACD,gBAAgB,qBAAqB,qBAAqB,gBAAgB,MAAM;AAC9E,6BAAqB;MACvB,CAAC;IAAA;EACH;AAEJ,CAAC;AAMD,IAAM,iBAAiB;AAKvB,IAAM,kBAAwB;EAC5B,CAAC,OAA0C,iBAAiB;AAC1D,UAAM,EAAE,eAAe,GAAG,eAAe,IAAI;AAC7C,eAAO,yBAAC,UAAU,KAAV,EAAc,eAAW,MAAE,GAAG,gBAAgB,KAAK,aAAA,CAAc;EAC3E;AACF;AAEA,gBAAgB,cAAc;AAM9B,IAAM,aAAa;AAMnB,IAAM,cAAoB;EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,EAAE,eAAe,GAAG,WAAW,IAAI;AACzC,UAAM,cAAc,eAAe,aAAa;AAChD,UAAM,UAAU,iBAAiB,YAAY,aAAa;AAC1D,UAAM,iBAAiB,wBAAwB,YAAY,aAAa;AACxE,WAAO,QAAQ,QAAQ,eAAe,aAAa,eACjD,yBAAiB,OAAhB,EAAuB,GAAG,aAAc,GAAG,YAAY,KAAK,aAAA,CAAc,IACzE;EACN;AACF;AAEA,YAAY,cAAc;AAM1B,IAAM,oBAAoB;AAK1B,IAAM,oBAA0B;EAC9B,CAAC,EAAE,eAAe,OAAO,GAAG,MAAM,GAAwC,iBAAiB;AACzF,UAAM,MAAY,cAA0B,IAAI;AAChD,UAAM,eAAe,gBAAgB,cAAc,GAAG;AACtD,UAAM,YAAY,YAAY,KAAK;AAG7B,IAAA,iBAAU,MAAM;AACpB,YAAM,SAAS,IAAI;AACnB,UAAI,CAAC;AAAQ;AAEb,YAAM,cAAc,OAAO,kBAAkB;AAC7C,YAAM,aAAa,OAAO;QACxB;QACA;MACF;AACA,YAAM,WAAW,WAAW;AAC5B,UAAI,cAAc,SAAS,UAAU;AACnC,cAAM,QAAQ,IAAI,MAAM,UAAU,EAAE,SAAS,KAAK,CAAC;AACnD,iBAAS,KAAK,QAAQ,KAAK;AAC3B,eAAO,cAAc,KAAK;MAC5B;IACF,GAAG,CAAC,WAAW,KAAK,CAAC;AAcrB,eACE;MAAC,UAAU;MAAV;QACE,GAAG;QACJ,OAAO,EAAE,GAAG,wBAAwB,GAAG,MAAM,MAAM;QACnD,KAAK;QACL,cAAc;MAAA;IAChB;EAEJ;AACF;AAEA,kBAAkB,cAAc;AAIhC,SAAS,sBAAsB,OAAgB;AAC7C,SAAO,UAAU,MAAM,UAAU;AACnC;AAEA,SAAS,mBAAmB,gBAA0C;AACpE,QAAM,qBAAqB,eAAe,cAAc;AACxD,QAAM,YAAkB,cAAO,EAAE;AACjC,QAAM,WAAiB,cAAO,CAAC;AAE/B,QAAM,wBAA8B;IAClC,CAAC,QAAgB;AACf,YAAM,SAAS,UAAU,UAAU;AACnC,yBAAmB,MAAM;AAEzB,OAAC,SAAS,aAAa,OAAe;AACpC,kBAAU,UAAU;AACpB,eAAO,aAAa,SAAS,OAAO;AAEpC,YAAI,UAAU;AAAI,mBAAS,UAAU,OAAO,WAAW,MAAM,aAAa,EAAE,GAAG,GAAI;MACrF,GAAG,MAAM;IACX;IACA,CAAC,kBAAkB;EACrB;AAEA,QAAM,iBAAuB,mBAAY,MAAM;AAC7C,cAAU,UAAU;AACpB,WAAO,aAAa,SAAS,OAAO;EACtC,GAAG,CAAC,CAAC;AAEC,EAAA,iBAAU,MAAM;AACpB,WAAO,MAAM,OAAO,aAAa,SAAS,OAAO;EACnD,GAAG,CAAC,CAAC;AAEL,SAAO,CAAC,WAAW,uBAAuB,cAAc;AAC1D;AAmBA,SAAS,aACP,OACA,QACA,aACA;AACA,QAAM,aAAa,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM,EAAE,MAAM,CAAC,SAAS,SAAS,OAAO,CAAC,CAAC;AAC7F,QAAM,mBAAmB,aAAa,OAAO,CAAC,IAAK;AACnD,QAAM,mBAAmB,cAAc,MAAM,QAAQ,WAAW,IAAI;AACpE,MAAI,eAAe,UAAU,OAAO,KAAK,IAAI,kBAAkB,CAAC,CAAC;AACjE,QAAM,qBAAqB,iBAAiB,WAAW;AACvD,MAAI;AAAoB,mBAAe,aAAa,OAAO,CAAC,MAAM,MAAM,WAAW;AACnF,QAAM,WAAW,aAAa;IAAK,CAAC,SAClC,KAAK,UAAU,YAAY,EAAE,WAAW,iBAAiB,YAAY,CAAC;EACxE;AACA,SAAO,aAAa,cAAc,WAAW;AAC/C;AAMA,SAAS,UAAa,OAAY,YAAoB;AACpD,SAAO,MAAM,IAAO,CAAC,GAAG,UAAU,OAAO,aAAa,SAAS,MAAM,MAAM,CAAE;AAC/E;AAEA,IAAMG,SAAO;AACb,IAAM,UAAU;AAChB,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAMC,UAAS;AACf,IAAMC,WAAU;AAChB,IAAM,WAAW;AACjB,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,WAAW;AACjB,IAAM,gBAAgB;AACtB,IAAM,iBAAiB;AACvB,IAAM,mBAAmB;AACzB,IAAM,YAAY;AAClB,IAAMC,SAAQ;", "names": ["React", "ReactDOM", "Slot", "import_jsx_runtime", "handleScroll", "canScrollUp", "canScrollDown", "Root", "Portal", "Content", "Arrow"]}