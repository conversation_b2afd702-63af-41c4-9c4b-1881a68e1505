"use strict";
// =====================================================
// AGENTIC pSEO TYPES - CORE AGENT SYSTEM
// =====================================================
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentError = void 0;
// =====================================================
// ERROR HANDLING TYPES
// =====================================================
class AgentError extends Error {
    constructor(message, agent_name, error_code, retry_possible = true, metadata) {
        super(message);
        this.name = 'AgentError';
        this.agent_name = agent_name;
        this.error_code = error_code;
        this.retry_possible = retry_possible;
        this.metadata = metadata;
    }
}
exports.AgentError = AgentError;
//# sourceMappingURL=AgentTypes.js.map