// =====================================================
// INTENT ANALYSIS NODE - AI CHAT WORKFLOW
// =====================================================

import { BaseNode, IntentAnalysisResult } from "../types/NodeTypes";
import { WorkflowContext, ChatMessage } from "../types/WorkflowState";

export class IntentAnalysisNode implements BaseNode {
  name = "intent_analysis";
  description =
    "Analyzes user intent from chat messages to determine appropriate actions";

  async execute(context: WorkflowContext): Promise<any> {
    const { state, tools, logger } = context;
    const startTime = Date.now();

    try {
      logger.info("Starting intent analysis", {
        workflow_id: state.workflow_id,
        messages_count: state.messages.length,
      });

      // Get the latest user message
      const userMessages = state.messages.filter((msg) => msg.role === "user");
      if (userMessages.length === 0) {
        throw new Error("No user messages found for intent analysis");
      }

      const latestMessage = userMessages[userMessages.length - 1];
      logger.debug("Analyzing message:", { content: latestMessage.content });

      // Analyze intent using AI
      const intent = await tools.ai.analyzeIntent(latestMessage.content);
      logger.info("Intent detected:", { intent });

      // Extract entities from the message
      const entities = await tools.ai.extractEntities(latestMessage.content);
      logger.debug("Entities extracted:", entities);

      // Determine confidence based on keywords and patterns
      const confidence = this.calculateConfidence(
        latestMessage.content,
        intent,
        entities
      );

      // Generate reasoning for the intent classification
      const reasoning = this.generateReasoning(
        latestMessage.content,
        intent,
        entities
      );

      // Suggest appropriate actions based on intent
      const suggestedActions = this.generateSuggestedActions(intent, entities);

      const result: IntentAnalysisResult = {
        intent: intent as any,
        confidence,
        entities,
        reasoning,
        suggested_actions: suggestedActions,
      };

      const executionTime = Date.now() - startTime;

      logger.info("Intent analysis completed", {
        intent: result.intent,
        confidence: result.confidence,
        execution_time: executionTime,
      });

      return {
        current_intent: result.intent,
        detected_entities: result.entities,
        ai_response: {
          ...state.ai_response,
          confidence: result.confidence,
          reasoning: result.reasoning,
          suggested_actions: result.suggested_actions,
        },
        node_data: {
          ...state.node_data,
          intent_analysis: result,
        },
        processing_time: (state.processing_time || 0) + executionTime,
        last_updated: new Date().toISOString(),
      };
    } catch (error) {
      const executionTime = Date.now() - startTime;
      logger.error("Intent analysis failed", error);

      return {
        current_intent: "unknown",
        detected_entities: {},
        errors: [
          ...state.errors,
          {
            node_name: this.name,
            error_message:
              error instanceof Error ? error.message : "Unknown error",
            error_code: "INTENT_ANALYSIS_FAILED",
            timestamp: new Date().toISOString(),
            recoverable: true,
          },
        ],
        processing_time: (state.processing_time || 0) + executionTime,
        last_updated: new Date().toISOString(),
      };
    }
  }

  // Calculate confidence score based on message analysis
  private calculateConfidence(
    message: string,
    intent: string,
    entities: any
  ): number {
    let confidence = 0.5; // Base confidence

    // Navigation intent confidence
    if (intent === "navigation") {
      const navigationKeywords = [
        "navigate",
        "go to",
        "switch to",
        "open",
        "show me",
        "take me to",
      ];
      const hasNavigationKeyword = navigationKeywords.some((keyword) =>
        message.toLowerCase().includes(keyword)
      );

      if (hasNavigationKeyword) confidence += 0.3;
      if (entities.page_name) confidence += 0.2;
    }

    // Database query intent confidence
    if (intent === "database_query") {
      const queryKeywords = [
        "find",
        "search",
        "show",
        "list",
        "get",
        "query",
        "data",
      ];
      const hasQueryKeyword = queryKeywords.some((keyword) =>
        message.toLowerCase().includes(keyword)
      );

      if (hasQueryKeyword) confidence += 0.3;
      if (entities.entity_type) confidence += 0.2;
    }

    // Knowledge search intent confidence
    if (intent === "knowledge_search") {
      const searchKeywords = [
        "information",
        "document",
        "knowledge",
        "learn",
        "explain",
      ];
      const hasSearchKeyword = searchKeywords.some((keyword) =>
        message.toLowerCase().includes(keyword)
      );

      if (hasSearchKeyword) confidence += 0.3;
      if (entities.search_terms) confidence += 0.2;
    }

    return Math.min(confidence, 1.0);
  }

  // Generate reasoning for intent classification
  private generateReasoning(
    message: string,
    intent: string,
    entities: any
  ): string {
    switch (intent) {
      case "navigation":
        return `Detected navigation intent because the message contains navigation-related keywords and ${
          entities.page_name
            ? `mentions the page "${entities.page_name}"`
            : "indicates a desire to move to a different section"
        }`;

      case "database_query":
        return `Detected database query intent because the message contains search/query keywords and ${
          entities.entity_type
            ? `refers to "${entities.entity_type}" data`
            : "indicates a need to retrieve information"
        }`;

      case "knowledge_search":
        return `Detected knowledge search intent because the message contains information-seeking keywords and ${
          entities.search_terms
            ? `includes search terms "${entities.search_terms}"`
            : "indicates a need for documentation or explanations"
        }`;

      case "general_chat":
        return "Detected general chat intent because the message appears to be conversational without specific action requirements";

      default:
        return "Could not determine specific intent from the message content";
    }
  }

  // Generate suggested actions based on intent and entities
  private generateSuggestedActions(intent: string, entities: any): string[] {
    const actions: string[] = [];

    switch (intent) {
      case "navigation":
        if (entities.page_name) {
          actions.push(`Navigate to ${entities.page_name}`);
        } else {
          actions.push("Ask user to specify which page to navigate to");
        }
        break;

      case "database_query":
        if (entities.entity_type) {
          actions.push(`Search for ${entities.entity_type} data`);
        }
        if (entities.time_period) {
          actions.push(`Filter results by ${entities.time_period}`);
        }
        if (!entities.entity_type) {
          actions.push("Ask user to specify what type of data to search for");
        }
        break;

      case "knowledge_search":
        if (entities.search_terms) {
          actions.push(`Search knowledge base for "${entities.search_terms}"`);
        } else {
          actions.push("Ask user to provide more specific search terms");
        }
        break;

      case "general_chat":
        actions.push("Provide helpful conversational response");
        actions.push(
          "Offer to help with navigation, data queries, or information search"
        );
        break;

      default:
        actions.push("Ask user to clarify their request");
        actions.push("Provide examples of available actions");
    }

    return actions;
  }

  // Validate message content
  private validateMessage(message: string): boolean {
    return !!(message && message.trim().length > 0 && message.length < 10000);
  }

  // Extract page names from message
  private extractPageNames(message: string): string[] {
    const pageKeywords = [
      "dashboard",
      "knowledge",
      "teams",
      "settings",
      "profile",
      "pseo",
      "scopingai",
      "crm",
      "chat",
    ];

    const foundPages: string[] = [];
    const lowerMessage = message.toLowerCase();

    pageKeywords.forEach((page) => {
      if (lowerMessage.includes(page)) {
        foundPages.push(page);
      }
    });

    return foundPages;
  }

  // Extract entity types from message
  private extractEntityTypes(message: string): string[] {
    const entityKeywords = [
      "customers",
      "users",
      "orders",
      "products",
      "companies",
      "contacts",
      "leads",
      "opportunities",
      "activities",
      "events",
    ];

    const foundEntities: string[] = [];
    const lowerMessage = message.toLowerCase();

    entityKeywords.forEach((entity) => {
      if (lowerMessage.includes(entity)) {
        foundEntities.push(entity);
      }
    });

    return foundEntities;
  }
}
