{"version": 3, "file": "ScopingAiDatabaseService.js", "sourceRoot": "", "sources": ["../../../../../../server/features/scopingAi/services/core/ScopingAiDatabaseService.ts"], "names": [], "mappings": ";;;AAAA,oEAAiE;AAEjE,MAAa,wBAAwB;IACnC,wDAAwD;IACxD,sBAAsB;IACtB,wDAAwD;IAExD,KAAK,CAAC,cAAc,CAAC,IAQpB;QACC,IAAI,CAAC;YACH,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,MAAM,mBAAQ;iBAC7C,IAAI,CAAC,qBAAqB,CAAC;iBAC3B,MAAM,CAAC;gBACN,OAAO,EAAE,IAAI,CAAC,MAAM;gBACpB,SAAS,EAAE,IAAI,CAAC,QAAQ;gBACxB,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,MAAM,EAAE,IAAI,CAAC,MAAM,IAAI,OAAO;gBAC9B,WAAW,EAAE,IAAI,CAAC,UAAU;gBAC5B,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,EAAE;gBAC7B,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACpC,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACrC,CAAC;iBACD,MAAM,CAAC,IAAI,CAAC;iBACZ,MAAM,EAAE,CAAC;YAEZ,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,IAAI,KAAK,CAAC,8BAA8B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACjE,CAAC;YAED,OAAO,QAAQ,CAAC,EAAE,CAAC;QACrB,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;YAC9E,MAAM,IAAI,KAAK,CAAC,uCAAuC,YAAY,EAAE,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,UAAkB,EAAE,OAMxC;QACC,IAAI,CAAC;YACH,MAAM,UAAU,GAA4B;gBAC1C,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACrC,CAAC;YAEF,IAAI,OAAO,CAAC,KAAK;gBAAE,UAAU,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;YACpD,IAAI,OAAO,CAAC,WAAW;gBAAE,UAAU,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;YACtE,IAAI,OAAO,CAAC,MAAM;gBAAE,UAAU,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;YACvD,IAAI,OAAO,CAAC,OAAO;gBAAE,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;YAC1D,IAAI,OAAO,CAAC,QAAQ;gBAAE,UAAU,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;YAE7D,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,mBAAQ;iBAC7B,IAAI,CAAC,qBAAqB,CAAC;iBAC3B,MAAM,CAAC,UAAU,CAAC;iBAClB,EAAE,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;YAExB,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,IAAI,KAAK,CAAC,8BAA8B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACjE,CAAC;QACH,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;YAC9E,MAAM,IAAI,KAAK,CAAC,uCAAuC,YAAY,EAAE,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,UAAkB;QACtC,IAAI,CAAC;YACH,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,mBAAQ;iBACnC,IAAI,CAAC,qBAAqB,CAAC;iBAC3B,MAAM,CAAC;;;;;;;;;SASP,CAAC;iBACD,EAAE,CAAC,IAAI,EAAE,UAAU,CAAC;iBACpB,MAAM,EAAE,CAAC;YAEZ,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;gBACvC,MAAM,IAAI,KAAK,CAAC,6BAA6B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAChE,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;YAC9E,MAAM,IAAI,KAAK,CAAC,uCAAuC,YAAY,EAAE,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,MAAc,EAAE,OAItC;QACC,IAAI,CAAC;YACH,IAAI,KAAK,GAAG,mBAAQ;iBACjB,IAAI,CAAC,qBAAqB,CAAC;iBAC3B,MAAM,CAAC;;;;;;;;;SASP,CAAC;iBACD,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;iBACrB,KAAK,CAAC,YAAY,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;YAE7C,IAAI,OAAO,EAAE,MAAM,EAAE,CAAC;gBACpB,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;YAC7C,CAAC;YACD,IAAI,OAAO,EAAE,KAAK,EAAE,CAAC;gBACnB,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACrC,CAAC;YACD,IAAI,OAAO,EAAE,MAAM,EAAE,CAAC;gBACpB,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACpF,CAAC;YAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,KAAK,CAAC;YAEpC,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,IAAI,KAAK,CAAC,mCAAmC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACtE,CAAC;YAED,OAAO,IAAI,IAAI,EAAE,CAAC;QACpB,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;YAC9E,MAAM,IAAI,KAAK,CAAC,6CAA6C,YAAY,EAAE,CAAC,CAAC;QAC/E,CAAC;IACH,CAAC;IAED,wDAAwD;IACxD,oBAAoB;IACpB,wDAAwD;IAExD,KAAK,CAAC,YAAY,CAAC,IASlB;QACC,IAAI,CAAC;YACH,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,mBAAQ;iBAC3C,IAAI,CAAC,mBAAmB,CAAC;iBACzB,MAAM,CAAC;gBACN,OAAO,EAAE,IAAI,CAAC,MAAM;gBACpB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,cAAc,EAAE,IAAI,CAAC,aAAa;gBAClC,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,EAAE;gBAC7B,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACpC,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACrC,CAAC;iBACD,MAAM,CAAC,IAAI,CAAC;iBACZ,MAAM,EAAE,CAAC;YAEZ,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,IAAI,KAAK,CAAC,4BAA4B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC/D,CAAC;YAED,OAAO,MAAM,CAAC,EAAE,CAAC;QACnB,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;YAC9E,MAAM,IAAI,KAAK,CAAC,qCAAqC,YAAY,EAAE,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,QAAgB;QAClC,IAAI,CAAC;YACH,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,mBAAQ;iBACnC,IAAI,CAAC,mBAAmB,CAAC;iBACzB,MAAM,CAAC,GAAG,CAAC;iBACX,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC;iBAClB,MAAM,EAAE,CAAC;YAEZ,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;gBACvC,MAAM,IAAI,KAAK,CAAC,2BAA2B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC9D,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;YAC9E,MAAM,IAAI,KAAK,CAAC,qCAAqC,YAAY,EAAE,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,MAAc;QACjC,IAAI,CAAC;YACH,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,mBAAQ;iBACnC,IAAI,CAAC,mBAAmB,CAAC;iBACzB,MAAM,CAAC,GAAG,CAAC;iBACX,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;iBACrB,KAAK,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAEtC,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,IAAI,KAAK,CAAC,iCAAiC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACpE,CAAC;YAED,OAAO,IAAI,IAAI,EAAE,CAAC;QACpB,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;YAC9E,MAAM,IAAI,KAAK,CAAC,2CAA2C,YAAY,EAAE,CAAC,CAAC;QAC7E,CAAC;IACH,CAAC;IAED,wDAAwD;IACxD,sBAAsB;IACtB,wDAAwD;IAExD,KAAK,CAAC,oBAAoB,CAAC,MAAe;QACxC,IAAI,CAAC;YACH,IAAI,KAAK,GAAG,mBAAQ;iBACjB,IAAI,CAAC,8BAA8B,CAAC;iBACpC,MAAM,CAAC,GAAG,CAAC;iBACX,KAAK,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAEtC,wDAAwD;YACxD,IAAI,MAAM,EAAE,CAAC;gBACX,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,gCAAgC,MAAM,EAAE,CAAC,CAAC;YAC7D,CAAC;iBAAM,CAAC;gBACN,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;YACtC,CAAC;YAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,KAAK,CAAC;YAEpC,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,IAAI,KAAK,CAAC,uCAAuC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC1E,CAAC;YAED,OAAO,IAAI,IAAI,EAAE,CAAC;QACpB,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;YAC9E,MAAM,IAAI,KAAK,CAAC,iDAAiD,YAAY,EAAE,CAAC,CAAC;QACnF,CAAC;IACH,CAAC;IAED,wDAAwD;IACxD,sBAAsB;IACtB,wDAAwD;IAExD,KAAK,CAAC,qBAAqB,CAAC,IAO3B;QACC,IAAI,CAAC;YACH,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,MAAM,mBAAQ;iBAC7C,IAAI,CAAC,qBAAqB,CAAC;iBAC3B,MAAM,CAAC;gBACN,WAAW,EAAE,IAAI,CAAC,UAAU;gBAC5B,OAAO,EAAE,IAAI,CAAC,MAAM;gBACpB,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,EAAE;gBAC7B,mBAAmB,EAAE,IAAI,CAAC,kBAAkB,IAAI,EAAE;gBAClD,MAAM,EAAE,WAAW;gBACnB,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACpC,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACrC,CAAC;iBACD,MAAM,CAAC,IAAI,CAAC;iBACZ,MAAM,EAAE,CAAC;YAEZ,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,IAAI,KAAK,CAAC,sCAAsC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACzE,CAAC;YAED,OAAO,QAAQ,CAAC,EAAE,CAAC;QACrB,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;YAC9E,MAAM,IAAI,KAAK,CAAC,+CAA+C,YAAY,EAAE,CAAC,CAAC;QACjF,CAAC;IACH,CAAC;IAED,wDAAwD;IACxD,kBAAkB;IAClB,wDAAwD;IAExD,KAAK,CAAC,WAAW;QACf,IAAI,CAAC;YACH,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,mBAAQ;iBAC7B,IAAI,CAAC,mBAAmB,CAAC;iBACzB,MAAM,CAAC,IAAI,CAAC;iBACZ,KAAK,CAAC,CAAC,CAAC,CAAC;YAEZ,OAAO,CAAC,KAAK,CAAC;QAChB,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF;AA1TD,4DA0TC;AAEY,QAAA,kBAAkB,GAAG,IAAI,wBAAwB,EAAE,CAAC"}