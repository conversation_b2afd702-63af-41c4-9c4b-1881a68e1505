// =====================================================
// AI CHAT LANGGRAPH ROUTES
// =====================================================

import express from "express";
import { validateApiKey } from "../../../base/common/routes/auth";
import { AIChatWorkflow } from "../langgraph/workflows/AIChatWorkflow";
import { StateManager } from "../langgraph/core/StateManager";

const router = express.Router();
const stateManager = StateManager.getInstance();

// CORS middleware
router.use((req, res, next) => {
  res.header("Access-Control-Allow-Origin", "http://localhost:5173");
  res.header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
  res.header(
    "Access-Control-Allow-Headers",
    "Content-Type, Authorization, x-api-key, x-user-id, Accept, Origin, X-Requested-With"
  );
  res.header("Access-Control-Allow-Credentials", "true");

  if (req.method === "OPTIONS") {
    res.status(200).end();
    return;
  }

  next();
});

// Health check for LangGraph workflows
router.get('/health', async (req, res) => {
  try {
    const config = AIChatWorkflow.getDefaultConfig();
    const validation = AIChatWorkflow.validateConfig(config);
    
    res.status(200).json({
      status: 'ok',
      langgraph: 'healthy',
      workflow: 'AIChatWorkflow',
      configuration: {
        valid: validation.valid,
        issues: validation.issues,
        capabilities: AIChatWorkflow.getCapabilities(),
        supported_intents: AIChatWorkflow.getSupportedIntents(),
        available_tools: {
          openai: !!config.openai_api_key,
          navigation: config.navigation_enabled,
          database: config.database_access_enabled,
          knowledge_base: config.knowledge_base_enabled
        }
      },
      workflow_stats: stateManager.getWorkflowStats(),
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('AI Chat LangGraph health check error:', error);
    res.status(500).json({
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

// Process chat message with LangGraph workflow
router.post('/chat', validateApiKey, async (req, res) => {
  try {
    const userId = req.headers['x-user-id'] as string;
    const { message, session_id, conversation_id, context } = req.body;

    // Validate required fields
    if (!userId) {
      return res.status(400).json({
        error: 'Missing user ID',
        message: 'x-user-id header is required'
      });
    }

    if (!message || typeof message !== 'string' || message.trim().length === 0) {
      return res.status(400).json({
        error: 'Invalid message',
        message: 'Message is required and must be a non-empty string'
      });
    }

    if (!session_id) {
      return res.status(400).json({
        error: 'Missing session ID',
        message: 'session_id is required'
      });
    }

    console.log(`🤖 Processing chat message via LangGraph for user: ${userId}`);
    console.log(`📝 Message: "${message}"`);

    // Create workflow instance
    const workflow = await AIChatWorkflow.create();

    // Process the message
    const result = await workflow.processMessage({
      user_id: userId,
      session_id,
      conversation_id,
      message: message.trim(),
      context
    });

    console.log(`✅ Chat workflow completed with status: ${result.status}`);

    // Return the result in CopilotKit compatible format
    const response = {
      success: true,
      data: {
        workflow_id: result.workflow_id,
        status: result.status,
        progress: result.progress,
        current_step: result.current_step,
        ai_response: result.ai_response,
        actions_performed: {
          navigation: result.navigation_actions,
          database_queries: result.database_queries,
          knowledge_searches: result.knowledge_searches
        },
        processing_time: result.processing_time,
        total_tokens_used: result.total_tokens_used,
        errors: result.errors
      },
      // CopilotKit compatible response format
      copilotkit_response: {
        data: {
          generateCopilotResponse: {
            threadId: result.conversation_id,
            runId: result.workflow_id,
            extensions: {
              navigation_actions: result.navigation_actions,
              database_queries: result.database_queries,
              knowledge_searches: result.knowledge_searches
            },
            status: {
              code: result.status === 'completed' ? 'SUCCESS' : 'ERROR',
              __typename: "BaseResponseStatus"
            },
            messages: [
              {
                __typename: "TextMessageOutput",
                id: `msg-${Date.now()}`,
                createdAt: new Date().toISOString(),
                content: [result.ai_response.content],
                role: "assistant",
                parentMessageId: null,
                status: {
                  code: "SUCCESS",
                  __typename: "SuccessMessageStatus"
                }
              }
            ],
            metaEvents: [],
            __typename: "CopilotResponse"
          }
        }
      }
    };

    res.status(200).json(response);

  } catch (error) {
    console.error('❌ Chat workflow processing failed:', error);
    res.status(500).json({
      error: 'Chat processing failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

// Get workflow status
router.get('/workflow/:workflowId/status', validateApiKey, async (req, res) => {
  try {
    const { workflowId } = req.params;
    const userId = req.headers['x-user-id'] as string;

    if (!userId) {
      return res.status(400).json({
        error: 'Missing user ID',
        message: 'x-user-id header is required'
      });
    }

    const status = await stateManager.getWorkflowStatus(workflowId);
    
    if (!status || status.status === 'not_found') {
      return res.status(404).json({
        error: 'Workflow not found',
        message: `Workflow ${workflowId} not found`
      });
    }

    res.status(200).json({
      success: true,
      data: status
    });

  } catch (error) {
    console.error('❌ Failed to get workflow status:', error);
    res.status(500).json({
      error: 'Failed to get workflow status',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Cancel workflow
router.post('/workflow/:workflowId/cancel', validateApiKey, async (req, res) => {
  try {
    const { workflowId } = req.params;
    const userId = req.headers['x-user-id'] as string;

    if (!userId) {
      return res.status(400).json({
        error: 'Missing user ID',
        message: 'x-user-id header is required'
      });
    }

    const cancelled = await stateManager.cancelWorkflow(workflowId);
    
    if (!cancelled) {
      return res.status(404).json({
        error: 'Workflow not found or not cancellable',
        message: `Workflow ${workflowId} not found or already completed`
      });
    }

    res.status(200).json({
      success: true,
      message: `Workflow ${workflowId} cancelled successfully`
    });

  } catch (error) {
    console.error('❌ Failed to cancel workflow:', error);
    res.status(500).json({
      error: 'Failed to cancel workflow',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get workflow capabilities
router.get('/capabilities', async (req, res) => {
  try {
    res.status(200).json({
      success: true,
      data: {
        capabilities: AIChatWorkflow.getCapabilities(),
        supported_intents: AIChatWorkflow.getSupportedIntents(),
        workflow_name: 'AIChatWorkflow',
        version: '1.0.0',
        features: {
          navigation: 'Automatic page navigation based on user requests',
          database_queries: 'Smart database searches and queries',
          knowledge_search: 'Knowledge base document searches',
          ai_responses: 'Contextual AI-generated responses',
          multi_turn: 'Multi-turn conversation support',
          error_recovery: 'Automatic error handling and recovery'
        }
      }
    });
  } catch (error) {
    console.error('❌ Failed to get capabilities:', error);
    res.status(500).json({
      error: 'Failed to get capabilities',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// List active workflows
router.get('/workflows/active', validateApiKey, async (req, res) => {
  try {
    const userId = req.headers['x-user-id'] as string;

    if (!userId) {
      return res.status(400).json({
        error: 'Missing user ID',
        message: 'x-user-id header is required'
      });
    }

    const activeWorkflows = stateManager.listActiveWorkflows();
    
    res.status(200).json({
      success: true,
      data: {
        active_workflows: activeWorkflows,
        count: activeWorkflows.length
      }
    });

  } catch (error) {
    console.error('❌ Failed to list active workflows:', error);
    res.status(500).json({
      error: 'Failed to list active workflows',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get workflow summary
router.get('/workflow/:workflowId/summary', validateApiKey, async (req, res) => {
  try {
    const { workflowId } = req.params;
    const userId = req.headers['x-user-id'] as string;

    if (!userId) {
      return res.status(400).json({
        error: 'Missing user ID',
        message: 'x-user-id header is required'
      });
    }

    const summary = stateManager.getWorkflowSummary(workflowId);
    
    if (!summary) {
      return res.status(404).json({
        error: 'Workflow not found',
        message: `Workflow ${workflowId} not found`
      });
    }

    res.status(200).json({
      success: true,
      data: summary
    });

  } catch (error) {
    console.error('❌ Failed to get workflow summary:', error);
    res.status(500).json({
      error: 'Failed to get workflow summary',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
