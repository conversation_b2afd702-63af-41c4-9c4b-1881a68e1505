// =====================================================
// AI CHAT LANGGRAPH NODE TYPE DEFINITIONS
// =====================================================

import { WorkflowContext, NodeResult } from './WorkflowState';

// Base node interface that all workflow nodes must implement
export interface BaseNode {
  name: string;
  description: string;
  execute(context: WorkflowContext): Promise<any>;
}

// Node metadata for workflow orchestration
export interface NodeMetadata {
  node_name: string;
  execution_order: number;
  dependencies: string[];
  optional: boolean;
  timeout_seconds: number;
  retry_attempts: number;
  resource_requirements: {
    memory_mb: number;
    cpu_cores: number;
  };
}

// Detailed node execution result
export interface DetailedNodeResult extends NodeResult {
  node_name: string;
  status: NodeStatus;
  started_at: string;
  completed_at: string;
  execution_time_ms: number;
  retry_count: number;
  warnings: string[];
}

// Node execution status
export type NodeStatus = 'pending' | 'running' | 'completed' | 'failed' | 'skipped' | 'cancelled';

// Intent Analysis Node Result
export interface IntentAnalysisResult {
  intent: 'navigation' | 'database_query' | 'knowledge_search' | 'general_chat' | 'unknown';
  confidence: number;
  entities: Record<string, any>;
  reasoning: string;
  suggested_actions: string[];
}

// Navigation Node Result
export interface NavigationNodeResult {
  navigation_triggered: boolean;
  target_page: string;
  target_url: string;
  success: boolean;
  execution_time: number;
  error?: string;
}

// Database Query Node Result
export interface DatabaseQueryNodeResult {
  query_executed: boolean;
  query: string;
  results: any[];
  total_count: number;
  execution_time: number;
  success: boolean;
  error?: string;
}

// Knowledge Base Search Node Result
export interface KnowledgeSearchNodeResult {
  search_executed: boolean;
  query: string;
  results: Array<{
    id: string;
    title: string;
    content: string;
    relevance_score: number;
  }>;
  total_found: number;
  execution_time: number;
  success: boolean;
  error?: string;
}

// AI Response Generation Node Result
export interface AIResponseNodeResult {
  response_generated: boolean;
  content: string;
  confidence: number;
  reasoning: string;
  tokens_used: number;
  processing_time: number;
  model_used: string;
  success: boolean;
  error?: string;
}

// Validation Node Result
export interface ValidationNodeResult {
  validation_passed: boolean;
  user_authenticated: boolean;
  input_valid: boolean;
  permissions_checked: boolean;
  rate_limit_ok: boolean;
  validation_errors: string[];
  warnings: string[];
}

// Node execution context with additional AI Chat specific data
export interface AIChatNodeContext extends WorkflowContext {
  // Additional context specific to AI Chat workflows
  user_preferences?: {
    preferred_navigation_style: 'direct' | 'guided';
    enable_proactive_suggestions: boolean;
    response_verbosity: 'concise' | 'detailed';
  };
  session_data?: {
    previous_actions: string[];
    conversation_history: any[];
    user_location?: string;
    device_type?: string;
  };
}

// Node factory interface for creating nodes dynamically
export interface NodeFactory {
  createNode(nodeType: string, config?: any): BaseNode;
  getAvailableNodeTypes(): string[];
  validateNodeConfig(nodeType: string, config: any): boolean;
}

// Node registry for managing available nodes
export interface NodeRegistry {
  registerNode(nodeType: string, nodeClass: new () => BaseNode): void;
  getNode(nodeType: string): BaseNode | null;
  listRegisteredNodes(): string[];
}

// Performance metrics for node execution
export interface NodePerformanceMetrics {
  node_name: string;
  execution_count: number;
  average_execution_time: number;
  success_rate: number;
  last_execution: string;
  total_processing_time: number;
  memory_usage_mb: number;
}

// Node health status
export interface NodeHealthStatus {
  node_name: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  last_check: string;
  response_time_ms: number;
  error_rate: number;
  dependencies_status: Record<string, boolean>;
}

// Node configuration interface
export interface NodeConfig {
  enabled: boolean;
  timeout_ms: number;
  retry_attempts: number;
  cache_enabled: boolean;
  cache_ttl_seconds: number;
  rate_limit?: {
    requests_per_minute: number;
    burst_limit: number;
  };
  custom_settings?: Record<string, any>;
}

// Conditional execution interface for nodes
export interface ConditionalExecution {
  condition: (context: WorkflowContext) => boolean;
  on_true: string; // Next node if condition is true
  on_false: string; // Next node if condition is false
  description: string;
}

// Node execution plan
export interface NodeExecutionPlan {
  node_name: string;
  execution_order: number;
  dependencies_resolved: boolean;
  estimated_duration_ms: number;
  resource_allocation: {
    memory_mb: number;
    cpu_percentage: number;
  };
  conditional_paths?: ConditionalExecution[];
}

// Workflow node state for tracking execution
export interface WorkflowNodeState {
  node_name: string;
  status: NodeStatus;
  started_at?: string;
  completed_at?: string;
  execution_time_ms?: number;
  retry_count: number;
  last_error?: string;
  output_data?: any;
  performance_metrics?: NodePerformanceMetrics;
}
