"use strict";
// =====================================================
// AGENT ORCHESTRATOR - MULTI-AGENT COORDINATION
// =====================================================
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentOrchestrator = void 0;
const AgentTypes_1 = require("./AgentTypes");
// Import all agent implementations
const PageDiscoveryAgent_1 = require("../specialists/PageDiscoveryAgent");
const KeywordResearchAgent_1 = require("../specialists/KeywordResearchAgent");
const ContentGenerationAgent_1 = require("../specialists/ContentGenerationAgent");
class AgentOrchestrator {
    constructor(config) {
        this.config = {
            max_concurrent_agents: 3,
            default_timeout_seconds: 300,
            retry_failed_jobs: true,
            max_retry_attempts: 3,
            job_queue_size: 100,
            enable_job_persistence: true,
            ...config
        };
        this.state = {
            active_jobs: new Map(),
            agent_registry: new Map(),
            job_queue: [],
            system_metrics: this.initializeMetrics()
        };
        this.executionQueue = new Map();
        this.activeExecutions = new Map();
        // Register all available agents
        this.registerAgents();
    }
    // =====================================================
    // AGENT REGISTRATION
    // =====================================================
    registerAgents() {
        // Register specialized agents
        this.registerAgent(new PageDiscoveryAgent_1.PageDiscoveryAgent());
        this.registerAgent(new KeywordResearchAgent_1.KeywordResearchAgent());
        this.registerAgent(new ContentGenerationAgent_1.ContentGenerationAgent());
        console.log(`Registered ${this.state.agent_registry.size} agents`);
    }
    registerAgent(agent) {
        this.state.agent_registry.set(agent.name, agent);
        console.log(`Registered agent: ${agent.name}`);
    }
    // =====================================================
    // JOB EXECUTION ORCHESTRATION
    // =====================================================
    async executeFullSiteAnalysis(website, analysisTypes, options = {}, context) {
        try {
            context.logger.info('Starting full site analysis orchestration', {
                website_id: website.id,
                domain: website.domain,
                analysis_types: analysisTypes
            });
            // Create execution plan
            const plan = await this.createExecutionPlan(website, analysisTypes, options, context);
            // Execute the plan
            const results = await this.executePlan(plan, context);
            // Update metrics
            this.updateMetrics(plan, results);
            context.logger.info('Full site analysis completed', {
                website_id: website.id,
                successful_agents: Object.values(results).filter(r => r.success).length,
                failed_agents: Object.values(results).filter(r => !r.success).length
            });
            return {
                success: Object.values(results).every(r => r.success),
                results,
                executionPlan: plan
            };
        }
        catch (error) {
            context.logger.error('Full site analysis orchestration failed', error);
            throw new AgentTypes_1.AgentError('Orchestration failed', 'AgentOrchestrator', 'ORCHESTRATION_ERROR', false, { website_id: website.id, analysis_types: analysisTypes });
        }
    }
    async executeAgentJob(job, website, context) {
        try {
            context.logger.info('Executing agent job', {
                job_id: job.id,
                agent_name: job.agent_name,
                job_type: job.job_type
            });
            const agent = this.state.agent_registry.get(job.agent_name);
            if (!agent) {
                throw new AgentTypes_1.AgentError(`Agent not found: ${job.agent_name}`, 'AgentOrchestrator', 'AGENT_NOT_FOUND', false);
            }
            // Prepare agent context
            const agentContext = {
                ...context,
                website,
                job
            };
            // Execute the agent
            const result = await agent.execute(agentContext);
            // Log result
            context.logger.info('Agent job completed', {
                job_id: job.id,
                agent_name: job.agent_name,
                success: result.success,
                execution_time: result.metrics.execution_time_ms
            });
            return result;
        }
        catch (error) {
            context.logger.error(`Agent job failed: ${job.id}`, error);
            throw error;
        }
    }
    // =====================================================
    // EXECUTION PLAN CREATION
    // =====================================================
    async createExecutionPlan(website, analysisTypes, options, context) {
        const planId = `plan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const agents = [];
        const dependencies = [];
        // Define agent execution order and dependencies
        const agentDefinitions = this.getAgentDefinitions(analysisTypes);
        for (const [index, agentDef] of agentDefinitions.entries()) {
            agents.push({
                agent_name: agentDef.name,
                execution_order: index,
                depends_on: agentDef.dependencies || [],
                input_mapping: agentDef.input_mapping || {},
                output_mapping: agentDef.output_mapping || {},
                timeout_seconds: agentDef.timeout || this.config.default_timeout_seconds
            });
        }
        const plan = {
            job_id: planId,
            website_id: website.id,
            agents,
            dependencies,
            estimated_duration_minutes: this.estimateExecutionTime(agents),
            priority: options.priority || 0
        };
        this.executionQueue.set(planId, plan);
        this.state.active_jobs.set(planId, plan);
        context.logger.info('Created execution plan', {
            plan_id: planId,
            agents_count: agents.length,
            estimated_duration: plan.estimated_duration_minutes
        });
        return plan;
    }
    getAgentDefinitions(analysisTypes) {
        const definitions = [];
        // Page discovery (usually first)
        if (analysisTypes.includes('page_discovery')) {
            definitions.push({
                name: 'PageDiscoveryAgent',
                dependencies: [],
                input_mapping: {
                    discovery_methods: ['sitemap', 'crawl'],
                    max_pages: 100,
                    crawl_depth: 3
                },
                timeout: 600 // 10 minutes
            });
        }
        // Keyword research (can run in parallel with page discovery)
        if (analysisTypes.includes('keyword_research')) {
            definitions.push({
                name: 'KeywordResearchAgent',
                dependencies: [],
                input_mapping: {
                    data_sources: ['google_planner', 'ubersuggest'],
                    max_keywords: 500
                },
                timeout: 300 // 5 minutes
            });
        }
        // Content generation (depends on both page discovery and keyword research)
        if (analysisTypes.includes('content_generation')) {
            const dependencies = [];
            if (analysisTypes.includes('page_discovery'))
                dependencies.push('PageDiscoveryAgent');
            if (analysisTypes.includes('keyword_research'))
                dependencies.push('KeywordResearchAgent');
            definitions.push({
                name: 'ContentGenerationAgent',
                dependencies,
                input_mapping: {
                    target_keywords: [], // Will be populated from keyword research results
                    content_types: ['blog', 'guide', 'faq'],
                    ai_model: 'gpt-4o'
                },
                timeout: 900 // 15 minutes
            });
        }
        return definitions;
    }
    // =====================================================
    // PLAN EXECUTION
    // =====================================================
    async executePlan(plan, context) {
        const results = {};
        const completedAgents = new Set();
        context.logger.info('Starting plan execution', {
            plan_id: plan.job_id,
            agents_count: plan.agents.length
        });
        // Sort agents by execution order
        const sortedAgents = [...plan.agents].sort((a, b) => a.execution_order - b.execution_order);
        for (const agentExecution of sortedAgents) {
            try {
                // Check dependencies
                const dependenciesMet = agentExecution.depends_on.every(dep => completedAgents.has(dep));
                if (!dependenciesMet) {
                    const missingDeps = agentExecution.depends_on.filter(dep => !completedAgents.has(dep));
                    throw new AgentTypes_1.AgentError(`Dependencies not met: ${missingDeps.join(', ')}`, agentExecution.agent_name, 'DEPENDENCY_ERROR', false);
                }
                // Prepare agent input with dependency results
                const agentInput = this.prepareAgentInput(agentExecution, results, context);
                // Create agent job
                const job = {
                    id: `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                    website_id: plan.website_id,
                    job_type: this.getJobType(agentExecution.agent_name),
                    agent_name: agentExecution.agent_name,
                    status: 'running',
                    priority: plan.priority,
                    result_data: agentInput,
                    retry_count: 0,
                    max_retries: this.config.max_retry_attempts,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                };
                // Execute agent
                context.logger.info(`Executing agent: ${agentExecution.agent_name}`, {
                    plan_id: plan.job_id,
                    job_id: job.id
                });
                const result = await this.executeAgentJob(job, context.website, context);
                results[agentExecution.agent_name] = result;
                if (result.success) {
                    completedAgents.add(agentExecution.agent_name);
                    context.logger.info(`Agent completed successfully: ${agentExecution.agent_name}`);
                }
                else {
                    context.logger.error(`Agent failed: ${agentExecution.agent_name}`, {
                        error: result.error
                    });
                    // Decide whether to continue or abort based on agent criticality
                    if (this.isCriticalAgent(agentExecution.agent_name)) {
                        throw new AgentTypes_1.AgentError(`Critical agent failed: ${agentExecution.agent_name}`, 'AgentOrchestrator', 'CRITICAL_AGENT_FAILURE', false);
                    }
                }
            }
            catch (error) {
                const agentError = error instanceof AgentTypes_1.AgentError ? error : new AgentTypes_1.AgentError(`Agent execution failed: ${agentExecution.agent_name}`, agentExecution.agent_name, 'EXECUTION_ERROR', true);
                results[agentExecution.agent_name] = {
                    success: false,
                    error: agentError.message,
                    metrics: {
                        execution_time_ms: 0,
                        api_calls_made: 0,
                        data_points_processed: 0,
                        errors_encountered: 1,
                        cache_hits: 0
                    }
                };
                context.logger.error(`Agent execution failed: ${agentExecution.agent_name}`, agentError);
            }
        }
        // Clean up
        this.state.active_jobs.delete(plan.job_id);
        this.executionQueue.delete(plan.job_id);
        return results;
    }
    // =====================================================
    // UTILITY METHODS
    // =====================================================
    prepareAgentInput(agentExecution, previousResults, context) {
        const input = {
            website_id: context.website.id,
            parameters: { ...agentExecution.input_mapping }
        };
        // Map outputs from dependency agents to this agent's input
        for (const [outputKey, inputKey] of Object.entries(agentExecution.output_mapping)) {
            const [agentName, resultField] = outputKey.split('.');
            const dependencyResult = previousResults[agentName];
            if (dependencyResult?.success && dependencyResult.data) {
                const value = this.getNestedValue(dependencyResult.data, resultField);
                if (value !== undefined) {
                    this.setNestedValue(input.parameters, inputKey, value);
                }
            }
        }
        // Special handling for keyword research -> content generation
        if (agentExecution.agent_name === 'ContentGenerationAgent') {
            const keywordResult = previousResults['KeywordResearchAgent'];
            if (keywordResult?.success && keywordResult.data?.keywords_found) {
                const keywords = keywordResult.data.keywords_found
                    .slice(0, 20) // Limit for efficiency
                    .map(k => k.keyword);
                input.parameters.target_keywords = keywords;
            }
        }
        return input;
    }
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => {
            return current && typeof current === 'object' ? current[key] : undefined;
        }, obj);
    }
    setNestedValue(obj, path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        const target = keys.reduce((current, key) => {
            if (!(key in current)) {
                current[key] = {};
            }
            return current[key];
        }, obj);
        target[lastKey] = value;
    }
    getJobType(agentName) {
        const mapping = {
            'PageDiscoveryAgent': 'page_discovery',
            'KeywordResearchAgent': 'keyword_research',
            'BacklinkAnalysisAgent': 'backlink_analysis',
            'ContentGenerationAgent': 'content_generation'
        };
        return mapping[agentName] || 'full_site_audit';
    }
    isCriticalAgent(agentName) {
        const criticalAgents = ['PageDiscoveryAgent']; // Page discovery is critical for other agents
        return criticalAgents.includes(agentName);
    }
    estimateExecutionTime(agents) {
        // Estimate based on agent timeouts and parallelization opportunities
        let totalTime = 0;
        const parallelGroups = this.groupAgentsByDependencies(agents);
        for (const group of parallelGroups) {
            const maxTimeInGroup = Math.max(...group.map(agent => agent.timeout_seconds || 300));
            totalTime += maxTimeInGroup;
        }
        return Math.ceil(totalTime / 60); // Convert to minutes
    }
    groupAgentsByDependencies(agents) {
        const groups = [];
        const processedAgents = new Set();
        // Simple grouping: agents with no unmet dependencies can run in parallel
        while (processedAgents.size < agents.length) {
            const currentGroup = [];
            for (const agent of agents) {
                if (processedAgents.has(agent.agent_name))
                    continue;
                const dependenciesMet = agent.depends_on.every(dep => processedAgents.has(dep));
                if (dependenciesMet) {
                    currentGroup.push(agent);
                }
            }
            if (currentGroup.length === 0) {
                // Circular dependency or other issue
                const remaining = agents.filter(a => !processedAgents.has(a.agent_name));
                currentGroup.push(...remaining);
            }
            groups.push(currentGroup);
            currentGroup.forEach(agent => processedAgents.add(agent.agent_name));
        }
        return groups;
    }
    // =====================================================
    // METRICS AND MONITORING
    // =====================================================
    initializeMetrics() {
        return {
            total_jobs_processed: 0,
            average_job_duration_minutes: 0,
            success_rate: 100,
            active_agents: 0,
            queue_length: 0,
            system_load: 0
        };
    }
    updateMetrics(plan, results) {
        const successfulJobs = Object.values(results).filter(r => r.success).length;
        const totalJobs = Object.values(results).length;
        this.state.system_metrics.total_jobs_processed += totalJobs;
        this.state.system_metrics.success_rate = ((this.state.system_metrics.success_rate * (this.state.system_metrics.total_jobs_processed - totalJobs) +
            (successfulJobs / totalJobs) * 100) / this.state.system_metrics.total_jobs_processed);
        // Calculate average execution time
        const totalExecutionTime = Object.values(results)
            .reduce((sum, result) => sum + (result.metrics.execution_time_ms || 0), 0);
        const avgTimeMinutes = totalExecutionTime / (1000 * 60 * totalJobs);
        this.state.system_metrics.average_job_duration_minutes = ((this.state.system_metrics.average_job_duration_minutes * (this.state.system_metrics.total_jobs_processed - totalJobs) +
            avgTimeMinutes * totalJobs) / this.state.system_metrics.total_jobs_processed);
        this.state.system_metrics.queue_length = this.executionQueue.size;
        this.state.system_metrics.active_agents = this.activeExecutions.size;
    }
    // =====================================================
    // PUBLIC API
    // =====================================================
    getSystemMetrics() {
        return { ...this.state.system_metrics };
    }
    getActiveJobs() {
        return Array.from(this.state.active_jobs.values());
    }
    getRegisteredAgents() {
        return Array.from(this.state.agent_registry.keys());
    }
    async cancelJob(jobId) {
        const plan = this.state.active_jobs.get(jobId);
        if (!plan) {
            return false;
        }
        // Cancel active execution if running
        const activeExecution = this.activeExecutions.get(jobId);
        if (activeExecution) {
            // Note: In a production system, you'd implement proper cancellation
            console.log(`Cancelling job: ${jobId}`);
        }
        // Clean up
        this.state.active_jobs.delete(jobId);
        this.executionQueue.delete(jobId);
        this.activeExecutions.delete(jobId);
        return true;
    }
    // Validation method
    validateExecutionPlan(plan) {
        const errors = [];
        // Check if all agents exist
        for (const agentExecution of plan.agents) {
            if (!this.state.agent_registry.has(agentExecution.agent_name)) {
                errors.push(`Agent not registered: ${agentExecution.agent_name}`);
            }
        }
        // Check for circular dependencies
        const hasCycles = this.detectCycles(plan.agents);
        if (hasCycles) {
            errors.push('Circular dependencies detected in execution plan');
        }
        return {
            valid: errors.length === 0,
            errors
        };
    }
    detectCycles(agents) {
        const visited = new Set();
        const recursionStack = new Set();
        const hasCycle = (agentName) => {
            if (recursionStack.has(agentName)) {
                return true;
            }
            if (visited.has(agentName)) {
                return false;
            }
            visited.add(agentName);
            recursionStack.add(agentName);
            const agent = agents.find(a => a.agent_name === agentName);
            if (agent) {
                for (const dependency of agent.depends_on) {
                    if (hasCycle(dependency)) {
                        return true;
                    }
                }
            }
            recursionStack.delete(agentName);
            return false;
        };
        for (const agent of agents) {
            if (hasCycle(agent.agent_name)) {
                return true;
            }
        }
        return false;
    }
}
exports.AgentOrchestrator = AgentOrchestrator;
//# sourceMappingURL=AgentOrchestrator.js.map