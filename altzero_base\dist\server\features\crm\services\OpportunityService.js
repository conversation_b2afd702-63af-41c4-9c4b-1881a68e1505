"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OpportunityService = void 0;
const supabase_1 = require("../../../base/common/apps/supabase");
class OpportunityService {
    /**
     * Get user's organization IDs from organisation_members table
     */
    async getUserOrganisationIds(userId) {
        const { data, error } = await supabase_1.supabase
            .from('organisation_members')
            .select('organisation_id')
            .eq('user_id', userId);
        if (error) {
            console.error('Error fetching user organizations:', error);
            return [];
        }
        return data.map(row => row.organisation_id);
    }
    /**
     * Get opportunities with pagination and filtering
     */
    async getOpportunities(filters) {
        try {
            if (!filters.userId) {
                throw new Error('User ID is required');
            }
            const organisationIds = await this.getUserOrganisationIds(filters.userId);
            if (organisationIds.length === 0) {
                return { data: [], total: 0, page: filters.page, limit: filters.limit };
            }
            let query = supabase_1.supabase
                .from('crm_opportunities')
                .select('*', { count: 'exact' })
                .in('organisation_id', organisationIds);
            // Apply stage filter
            if (filters.stage) {
                query = query.eq('stage', filters.stage);
            }
            // Apply assigned_to filter
            if (filters.assignedTo) {
                query = query.eq('assigned_to', filters.assignedTo);
            }
            // Apply pagination
            const offset = (filters.page - 1) * filters.limit;
            query = query
                .order('created_at', { ascending: false })
                .range(offset, offset + filters.limit - 1);
            const { data, error, count } = await query;
            if (error) {
                console.error('Error fetching opportunities:', error);
                throw error;
            }
            return {
                data: data || [],
                total: count || 0,
                page: filters.page,
                limit: filters.limit
            };
        }
        catch (error) {
            console.error('OpportunityService.getOpportunities error:', error);
            throw error;
        }
    }
    /**
     * Get a single opportunity by ID
     */
    async getOpportunityById(opportunityId, userId) {
        try {
            if (!userId) {
                throw new Error('User ID is required');
            }
            const organisationIds = await this.getUserOrganisationIds(userId);
            if (organisationIds.length === 0) {
                return null;
            }
            const { data, error } = await supabase_1.supabase
                .from('crm_opportunities')
                .select('*')
                .eq('id', opportunityId)
                .in('organisation_id', organisationIds)
                .single();
            if (error) {
                if (error.code === 'PGRST116') {
                    return null; // Not found
                }
                console.error('Error fetching opportunity:', error);
                throw error;
            }
            return data;
        }
        catch (error) {
            console.error('OpportunityService.getOpportunityById error:', error);
            throw error;
        }
    }
    /**
     * Create a new opportunity
     */
    async createOpportunity(opportunityData, userId) {
        try {
            if (!userId) {
                throw new Error('User ID is required');
            }
            const organisationIds = await this.getUserOrganisationIds(userId);
            if (organisationIds.length === 0) {
                throw new Error('User is not a member of any organization');
            }
            // Use the first organization if not specified
            if (!opportunityData.organisation_id) {
                opportunityData.organisation_id = organisationIds[0];
            }
            // Verify user has access to the specified organization
            if (!organisationIds.includes(opportunityData.organisation_id)) {
                throw new Error('User does not have access to the specified organization');
            }
            // Set assigned_to to current user if not specified
            if (!opportunityData.assigned_to) {
                opportunityData.assigned_to = userId;
            }
            // Set default currency if not specified
            if (!opportunityData.currency) {
                opportunityData.currency = 'USD';
            }
            const { data, error } = await supabase_1.supabase
                .from('crm_opportunities')
                .insert([opportunityData])
                .select()
                .single();
            if (error) {
                console.error('Error creating opportunity:', error);
                throw error;
            }
            return data;
        }
        catch (error) {
            console.error('OpportunityService.createOpportunity error:', error);
            throw error;
        }
    }
    /**
     * Update an existing opportunity
     */
    async updateOpportunity(opportunityId, opportunityData, userId) {
        try {
            if (!userId) {
                throw new Error('User ID is required');
            }
            const organisationIds = await this.getUserOrganisationIds(userId);
            if (organisationIds.length === 0) {
                return null;
            }
            // Remove fields that shouldn't be updated
            const { id, created_at, ...updateData } = opportunityData;
            updateData.updated_at = new Date().toISOString();
            const { data, error } = await supabase_1.supabase
                .from('crm_opportunities')
                .update(updateData)
                .eq('id', opportunityId)
                .in('organisation_id', organisationIds)
                .select()
                .single();
            if (error) {
                if (error.code === 'PGRST116') {
                    return null; // Not found
                }
                console.error('Error updating opportunity:', error);
                throw error;
            }
            return data;
        }
        catch (error) {
            console.error('OpportunityService.updateOpportunity error:', error);
            throw error;
        }
    }
    /**
     * Delete an opportunity
     */
    async deleteOpportunity(opportunityId, userId) {
        try {
            if (!userId) {
                throw new Error('User ID is required');
            }
            const organisationIds = await this.getUserOrganisationIds(userId);
            if (organisationIds.length === 0) {
                return false;
            }
            const { error } = await supabase_1.supabase
                .from('crm_opportunities')
                .delete()
                .eq('id', opportunityId)
                .in('organisation_id', organisationIds);
            if (error) {
                console.error('Error deleting opportunity:', error);
                throw error;
            }
            return true;
        }
        catch (error) {
            console.error('OpportunityService.deleteOpportunity error:', error);
            throw error;
        }
    }
    /**
     * Get opportunities by stage for pipeline view
     */
    async getOpportunitiesByStage(userId) {
        try {
            if (!userId) {
                throw new Error('User ID is required');
            }
            const organisationIds = await this.getUserOrganisationIds(userId);
            if (organisationIds.length === 0) {
                return {};
            }
            const { data, error } = await supabase_1.supabase
                .from('crm_opportunities')
                .select('*')
                .in('organisation_id', organisationIds)
                .order('created_at', { ascending: false });
            if (error) {
                console.error('Error fetching opportunities by stage:', error);
                throw error;
            }
            // Group by stage
            const groupedByStage = (data || []).reduce((acc, opportunity) => {
                const stage = opportunity.stage || 'unassigned';
                if (!acc[stage]) {
                    acc[stage] = [];
                }
                acc[stage].push(opportunity);
                return acc;
            }, {});
            return groupedByStage;
        }
        catch (error) {
            console.error('OpportunityService.getOpportunitiesByStage error:', error);
            throw error;
        }
    }
    /**
     * Get opportunity statistics
     */
    async getOpportunityStats(userId) {
        try {
            if (!userId) {
                throw new Error('User ID is required');
            }
            const organisationIds = await this.getUserOrganisationIds(userId);
            if (organisationIds.length === 0) {
                return {
                    total: 0,
                    totalValue: 0,
                    byStage: {},
                    avgValue: 0
                };
            }
            const { data, error } = await supabase_1.supabase
                .from('crm_opportunities')
                .select('stage, value, currency')
                .in('organisation_id', organisationIds);
            if (error) {
                console.error('Error fetching opportunity stats:', error);
                throw error;
            }
            const opportunities = data || [];
            const total = opportunities.length;
            const totalValue = opportunities.reduce((sum, opp) => sum + (opp.value || 0), 0);
            const avgValue = total > 0 ? totalValue / total : 0;
            // Group by stage
            const byStage = opportunities.reduce((acc, opp) => {
                const stage = opp.stage || 'unassigned';
                if (!acc[stage]) {
                    acc[stage] = { count: 0, value: 0 };
                }
                acc[stage].count++;
                acc[stage].value += opp.value || 0;
                return acc;
            }, {});
            return {
                total,
                totalValue,
                byStage,
                avgValue
            };
        }
        catch (error) {
            console.error('OpportunityService.getOpportunityStats error:', error);
            throw error;
        }
    }
}
exports.OpportunityService = OpportunityService;
//# sourceMappingURL=OpportunityService.js.map