"use strict";
// =====================================================
// UBERSUGGEST API INTEGRATION TOOL FOR LANGGRAPH
// =====================================================
Object.defineProperty(exports, "__esModule", { value: true });
exports.UbersuggestTool = void 0;
class UbersuggestTool {
    constructor(config) {
        this.name = 'ubersuggest_keyword_research';
        this.description = 'Research keywords using Ubersuggest API for search volume and keyword suggestions';
        this.lastRequestTime = 0;
        this.config = config;
    }
    async _call(input) {
        try {
            const { keywords, options } = JSON.parse(input);
            if (!Array.isArray(keywords)) {
                throw new Error('Keywords must be an array');
            }
            const results = await this.getKeywordData(keywords, options);
            return JSON.stringify(results);
        }
        catch (error) {
            console.error('Ubersuggest tool error:', error);
            throw error;
        }
    }
    // Get keyword data from Ubersuggest API
    async getKeywordData(keywords, options = {}) {
        if (!this.config.enabled || !this.config.apiKey) {
            throw new Error('Ubersuggest API not configured');
        }
        const results = [];
        for (const keyword of keywords.slice(0, 8)) { // Limit to prevent API overuse
            try {
                await this.enforceRateLimit();
                // Get keyword overview
                const overviewData = await this.getKeywordOverview(keyword);
                // Get keyword suggestions
                const suggestionsData = await this.getKeywordSuggestions(keyword, 5);
                results.push({
                    keyword,
                    search_volume: overviewData.search_volume || 0,
                    keyword_difficulty: overviewData.keyword_difficulty || 50,
                    cpc: overviewData.cpc || 0,
                    competition: this.mapCompetitionLevel(overviewData.competition_score),
                    intent: this.inferKeywordIntent(keyword),
                    trend: overviewData.trend || 'stable',
                    related_keywords: suggestionsData
                });
            }
            catch (error) {
                console.warn(`Failed to get Ubersuggest data for keyword: ${keyword}`, error);
                // Return fallback data to prevent workflow failure
                results.push({
                    keyword,
                    search_volume: 0,
                    keyword_difficulty: 50,
                    cpc: 0,
                    competition: 'medium',
                    intent: this.inferKeywordIntent(keyword),
                    trend: 'stable',
                    related_keywords: []
                });
            }
        }
        return results;
    }
    // Get keyword overview from Ubersuggest
    async getKeywordOverview(keyword) {
        const url = 'https://app.neilpatel.com/api/keywords/overview';
        const requestBody = {
            keyword: keyword,
            locId: 2840, // United States
            language: 'en'
        };
        const response = await this.makeRequest(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-API-Key': this.config.apiKey
            },
            body: JSON.stringify(requestBody)
        });
        if (!response.ok) {
            throw new Error(`Ubersuggest API error: ${response.status} ${response.statusText}`);
        }
        const data = await response.json();
        if (!data.success) {
            throw new Error('Ubersuggest API returned error');
        }
        return {
            search_volume: data.data?.search_volume || 0,
            cpc: data.data?.cpc || 0,
            competition_score: data.data?.competition || 0,
            keyword_difficulty: data.data?.difficulty || 50,
            trend: this.analyzeTrend(data.data?.trend_data)
        };
    }
    // Get keyword suggestions from Ubersuggest
    async getKeywordSuggestions(keyword, limit = 5) {
        const url = 'https://app.neilpatel.com/api/keywords/suggestions';
        const requestBody = {
            keyword: keyword,
            locId: 2840, // United States
            language: 'en',
            limit: limit
        };
        try {
            const response = await this.makeRequest(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-API-Key': this.config.apiKey
                },
                body: JSON.stringify(requestBody)
            });
            if (!response.ok) {
                return [];
            }
            const data = await response.json();
            if (!data.success || !data.data?.suggestions) {
                return [];
            }
            return data.data.suggestions
                .map((suggestion) => suggestion.keyword)
                .filter((kw) => kw && kw !== keyword)
                .slice(0, limit);
        }
        catch (error) {
            console.warn('Failed to get keyword suggestions from Ubersuggest:', error);
            return [];
        }
    }
    // Make HTTP request with retry logic
    async makeRequest(url, options) {
        let lastError = new Error('Unknown error');
        for (let attempt = 0; attempt <= this.config.retries; attempt++) {
            try {
                const controller = new AbortController();
                const timeout = setTimeout(() => controller.abort(), this.config.timeout);
                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'User-Agent': 'AltZero-pSEO-Bot/1.0',
                        ...options.headers
                    },
                    signal: controller.signal
                });
                clearTimeout(timeout);
                return response;
            }
            catch (error) {
                lastError = error instanceof Error ? error : new Error('Unknown error');
                if (attempt < this.config.retries) {
                    // Exponential backoff
                    const delay = Math.pow(2, attempt) * 1000;
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }
        throw lastError;
    }
    // Enforce rate limiting
    async enforceRateLimit() {
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;
        const minInterval = 1000 / this.config.rateLimit; // Convert rate limit to interval
        if (timeSinceLastRequest < minInterval) {
            const delay = minInterval - timeSinceLastRequest;
            await new Promise(resolve => setTimeout(resolve, delay));
        }
        this.lastRequestTime = Date.now();
    }
    // Map Ubersuggest competition score to our format
    mapCompetitionLevel(competitionScore) {
        if (competitionScore < 0.33)
            return 'low';
        if (competitionScore < 0.67)
            return 'medium';
        return 'high';
    }
    // Analyze trend data
    analyzeTrend(trendData) {
        if (!trendData || trendData.length < 2) {
            return 'stable';
        }
        const recent = trendData.slice(-3);
        const older = trendData.slice(0, 3);
        const recentAvg = recent.reduce((sum, item) => sum + (item.search_volume || 0), 0) / recent.length;
        const olderAvg = older.reduce((sum, item) => sum + (item.search_volume || 0), 0) / older.length;
        const changePercent = ((recentAvg - olderAvg) / olderAvg) * 100;
        if (changePercent > 10)
            return 'rising';
        if (changePercent < -10)
            return 'declining';
        return 'stable';
    }
    // Infer keyword intent
    inferKeywordIntent(keyword) {
        const keywordLower = keyword.toLowerCase();
        if (keywordLower.includes('buy') || keywordLower.includes('price') || keywordLower.includes('cost') || keywordLower.includes('purchase')) {
            return 'transactional';
        }
        if (keywordLower.includes('review') || keywordLower.includes('vs') || keywordLower.includes('best') || keywordLower.includes('compare')) {
            return 'commercial';
        }
        if (keywordLower.includes('how') || keywordLower.includes('what') || keywordLower.includes('guide') || keywordLower.includes('tutorial')) {
            return 'informational';
        }
        return 'informational';
    }
    // Check if Ubersuggest API is available
    async healthCheck() {
        try {
            if (!this.config.enabled || !this.config.apiKey) {
                return false;
            }
            // Test with a simple query
            const url = 'https://app.neilpatel.com/api/keywords/overview';
            const requestBody = {
                keyword: 'test',
                locId: 2840,
                language: 'en'
            };
            const response = await this.makeRequest(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-API-Key': this.config.apiKey
                },
                body: JSON.stringify(requestBody)
            });
            return response.ok;
        }
        catch (error) {
            console.warn('Ubersuggest health check failed:', error);
            return false;
        }
    }
    // Get API usage statistics (if available)
    async getUsageStats() {
        // Ubersuggest doesn't provide usage stats in their API
        // Return placeholder data
        return {
            requests_left: 'unknown',
            requests_limit: 'unknown',
            usage_percentage: 'unknown'
        };
    }
}
exports.UbersuggestTool = UbersuggestTool;
//# sourceMappingURL=UbersuggestTool.js.map