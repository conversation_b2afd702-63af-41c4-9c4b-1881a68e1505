import express from "express";
import {
  Co<PERSON>lotRuntime,
  OpenAIAdapter,
  copilotRuntimeNodeHttpEndpoint,
} from "@copilotkit/runtime";
import { validateApiKey } from "../../../base/common/routes/auth";
import OpenAI from "openai";

const router = express.Router();

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Create OpenAI adapter for CopilotKit
const serviceAdapter = new OpenAIAdapter({ openai });

// Define backend actions for CopilotKit
const actions = [
  {
    name: "queryDatabase",
    description:
      "Query the AltZero database using natural language. Use @entity syntax like '@customers John' or '@orders today'",
    parameters: [
      {
        name: "query",
        type: "string" as const,
        description: "Natural language database query using @entity syntax",
        required: true,
      },
    ],
    handler: async ({ query }: { query: string }) => {
      try {
        console.log("🔍 Database query action:", query);

        // TODO: Implement actual database querying logic
        // For now, return a structured response
        return {
          success: true,
          message: `Database query executed: ${query}`,
          results: [],
          note: "Database integration coming soon! This action is working correctly.",
        };
      } catch (error) {
        throw new Error(
          `Database query failed: ${
            error instanceof Error ? error.message : "Unknown error"
          }`
        );
      }
    },
  },
  {
    name: "navigateToPage",
    description: "Navigate to different pages in the AltZero platform",
    parameters: [
      {
        name: "page",
        type: "string" as const,
        description:
          "Page to navigate to (dashboard, knowledge, teams, settings, pseo, scopingai, crm)",
        required: true,
      },
    ],
    handler: async ({ page }: { page: string }) => {
      try {
        console.log("🧭 Navigation action:", page);

        // Simple navigation mapping
        const pageMap: Record<string, string> = {
          dashboard: "/",
          knowledge: "/knowledge",
          "knowledge base": "/knowledge",
          teams: "/teams",
          settings: "/settings",
          profile: "/profile",
          pseo: "/pseo",
          scopingai: "/scopingai",
          crm: "/crm",
          chat: "/ai-chat",
        };

        const targetPage =
          pageMap[page.toLowerCase()] || `/${page.toLowerCase()}`;

        return {
          success: true,
          message: `Navigation to ${page} prepared`,
          targetPage,
          action: "navigate",
        };
      } catch (error) {
        throw new Error(
          `Navigation failed: ${
            error instanceof Error ? error.message : "Unknown error"
          }`
        );
      }
    },
  },
  {
    name: "searchKnowledgeBase",
    description:
      "Search through the knowledge base documents for relevant information",
    parameters: [
      {
        name: "searchQuery",
        type: "string" as const,
        description: "Search query to find relevant documents",
        required: true,
      },
      {
        name: "maxResults",
        type: "number" as const,
        description: "Maximum number of results to return (default: 5)",
        required: false,
      },
    ],
    handler: async ({
      searchQuery,
      maxResults = 5,
    }: {
      searchQuery: string;
      maxResults?: number;
    }) => {
      try {
        console.log("📚 Knowledge base search action:", searchQuery);

        // TODO: Implement actual knowledge base search
        // For now, return a structured response
        return {
          success: true,
          message: `Knowledge base search completed for: ${searchQuery}`,
          query: searchQuery,
          results: [],
          totalFound: 0,
          note: "Knowledge base integration coming soon! This action is working correctly.",
        };
      } catch (error) {
        throw new Error(
          `Knowledge base search failed: ${
            error instanceof Error ? error.message : "Unknown error"
          }`
        );
      }
    },
  },
];

// Create CopilotKit runtime with actions
const runtime = new CopilotRuntime({
  actions: () => actions,
});

// CopilotKit runtime endpoint - proper implementation with backend actions
router.all("/", validateApiKey, async (req, res) => {
  try {
    console.log("🤖 CopilotKit runtime request received with backend actions");
    console.log("Method:", req.method);
    console.log("Headers:", req.headers);

    // Handle the CopilotKit request using the runtime
    const { handleRequest } = copilotRuntimeNodeHttpEndpoint({
      runtime,
      serviceAdapter,
      endpoint: "/api/aichat",
    });

    // Create a proper Request object for CopilotKit
    const url = `${req.protocol}://${req.get("host")}${req.originalUrl}`;
    const headers = new Headers();

    // Copy headers from Express request
    Object.entries(req.headers).forEach(([key, value]) => {
      if (typeof value === "string") {
        headers.set(key, value);
      } else if (Array.isArray(value)) {
        headers.set(key, value.join(", "));
      }
    });

    // Create the request body
    let body: string | undefined;
    if (req.method !== "GET" && req.method !== "HEAD" && req.body) {
      body = typeof req.body === "string" ? req.body : JSON.stringify(req.body);
    }

    const copilotRequest = new Request(url, {
      method: req.method,
      headers,
      body,
    });

    return await handleRequest(copilotRequest, res as any);
  } catch (error) {
    console.error("CopilotKit runtime error:", error);
    res.status(500).json({
      error: "CopilotKit runtime error",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
});

// Health check endpoint
router.get("/health", async (req, res) => {
  try {
    // Check if OpenAI API key is configured
    const hasApiKey = !!process.env.OPENAI_API_KEY;

    // Basic health check
    const status = {
      status: "healthy",
      services: {
        openai: hasApiKey ? "configured" : "not_configured",
        copilotkit: "active",
      },
      timestamp: new Date().toISOString(),
    };

    res.status(200).json(status);
  } catch (error) {
    console.error("AI Chat health check error:", error);
    res.status(503).json({
      status: "unhealthy",
      error: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
    });
  }
});

export default router;
