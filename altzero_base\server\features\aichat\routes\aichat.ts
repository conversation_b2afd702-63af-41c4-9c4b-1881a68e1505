import express from "express";
import { validate<PERSON><PERSON><PERSON><PERSON> } from "../../../base/common/routes/auth";
import OpenAI from "openai";

const router = express.Router();

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Simple OpenAI-compatible chat endpoint for CopilotKit
router.post("/", validateApiKey, async (req, res) => {
  try {
    console.log("🤖 CopilotKit chat request received");

    const {
      messages,
      model = "gpt-4",
      temperature = 0.7,
      max_tokens = 2048,
    } = req.body;

    if (!messages || !Array.isArray(messages)) {
      return res.status(400).json({ error: "Messages array is required" });
    }

    // Call OpenAI API
    const completion = await openai.chat.completions.create({
      model,
      messages,
      temperature,
      max_tokens,
      stream: false,
    });

    // Return OpenAI-compatible response
    res.json(completion);
  } catch (error) {
    console.error("CopilotKit runtime error:", error);
    res.status(500).json({
      error: "CopilotKit runtime error",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
});

// Health check endpoint
router.get("/health", async (req, res) => {
  try {
    // Check if OpenAI API key is configured
    const hasApiKey = !!process.env.OPENAI_API_KEY;

    // Basic health check
    const status = {
      status: "healthy",
      services: {
        openai: hasApiKey ? "configured" : "not_configured",
        copilotkit: "active",
      },
      timestamp: new Date().toISOString(),
    };

    res.status(200).json(status);
  } catch (error) {
    console.error("AI Chat health check error:", error);
    res.status(503).json({
      status: "unhealthy",
      error: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
    });
  }
});

export default router;
