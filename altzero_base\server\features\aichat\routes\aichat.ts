import express from "express";
import { validate<PERSON><PERSON><PERSON><PERSON> } from "../../../base/common/routes/auth";
import OpenAI from "openai";

const router = express.Router();

// Additional CORS middleware specifically for CopilotKit
router.use((req, res, next) => {
  res.header("Access-Control-Allow-Origin", "http://localhost:5173");
  res.header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
  res.header(
    "Access-Control-Allow-Headers",
    "Content-Type, Authorization, x-api-key, x-user-id, Accept, Origin, X-Requested-With"
  );
  res.header("Access-Control-Allow-Credentials", "true");

  // Handle preflight requests
  if (req.method === "OPTIONS") {
    res.status(200).end();
    return;
  }

  next();
});

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Simple CopilotKit-compatible endpoint
router.post("/", validateApiKey, async (req, res) => {
  try {
    console.log("🤖 CopilotKit request received");
    console.log("Method:", req.method);
    console.log("Body:", JSON.stringify(req.body, null, 2));

    // Handle different CopilotKit operations
    const { operationName, query, variables } = req.body;

    if (operationName === "generateCopilotResponse") {
      const messages = variables?.data?.messages || [];
      console.log(
        "💬 Processing generateCopilotResponse with messages:",
        messages.length
      );

      // Extract the actual message content
      const chatMessages = messages
        .map((msg: any) => ({
          role: msg.textMessage?.role || msg.role || "user",
          content: msg.textMessage?.content || msg.content || "",
        }))
        .filter((msg: any) => msg.content.trim() !== "");

      const userMessage =
        chatMessages[chatMessages.length - 1]?.content || "Hello";
      console.log("💬 User message:", userMessage);

      // Check if user wants navigation
      if (
        userMessage.toLowerCase().includes("navigate") ||
        userMessage.toLowerCase().includes("go to") ||
        userMessage.toLowerCase().includes("switch to")
      ) {
        // Extract page name from message
        const pageKeywords = [
          "dashboard",
          "knowledge",
          "teams",
          "settings",
          "pseo",
          "scopingai",
          "crm",
        ];
        const foundPage = pageKeywords.find((page) =>
          userMessage.toLowerCase().includes(page)
        );

        if (foundPage) {
          console.log("🧭 Navigation detected for:", foundPage);

          const response = {
            data: {
              generateCopilotResponse: {
                threadId: variables?.data?.threadId || `thread-${Date.now()}`,
                runId: variables?.data?.runId || `run-${Date.now()}`,
                extensions: {},
                status: {
                  code: "SUCCESS",
                  __typename: "BaseResponseStatus",
                },
                messages: [
                  {
                    __typename: "TextMessageOutput",
                    id: `msg-${Date.now()}`,
                    createdAt: new Date().toISOString(),
                    content: [
                      `I'll help you navigate to ${foundPage}. Please use the navigation menu or click on the ${foundPage} section.`,
                    ],
                    role: "assistant",
                    parentMessageId: messages[messages.length - 1]?.id || null,
                    status: {
                      code: "SUCCESS",
                      __typename: "SuccessMessageStatus",
                    },
                  },
                ],
                metaEvents: [],
                __typename: "CopilotResponse",
              },
            },
          };

          return res.json(response);
        }
      }

      // Default AI response using OpenAI
      try {
        const completion = await openai.chat.completions.create({
          model: "gpt-4",
          messages: [
            {
              role: "system",
              content:
                "You are AltZero AI, an intelligent assistant for the AltZero platform. You can help with navigation, database queries, and knowledge base searches. Be helpful and concise.",
            },
            ...chatMessages,
          ],
          temperature: 0.7,
          max_tokens: 2048,
        });

        const assistantMessage =
          completion.choices[0]?.message?.content ||
          "Hello! I'm your AltZero AI assistant.";

        const response = {
          data: {
            generateCopilotResponse: {
              threadId: variables?.data?.threadId || `thread-${Date.now()}`,
              runId: variables?.data?.runId || `run-${Date.now()}`,
              extensions: {},
              status: {
                code: "SUCCESS",
                __typename: "BaseResponseStatus",
              },
              messages: [
                {
                  __typename: "TextMessageOutput",
                  id: `msg-${Date.now()}`,
                  createdAt: new Date().toISOString(),
                  content: [assistantMessage],
                  role: "assistant",
                  parentMessageId: messages[messages.length - 1]?.id || null,
                  status: {
                    code: "SUCCESS",
                    __typename: "SuccessMessageStatus",
                  },
                },
              ],
              metaEvents: [],
              __typename: "CopilotResponse",
            },
          },
        };

        console.log("✅ Sending CopilotKit response:", assistantMessage);
        return res.json(response);
      } catch (error) {
        console.error("OpenAI API error:", error);

        // Fallback response
        const response = {
          data: {
            generateCopilotResponse: {
              threadId: variables?.data?.threadId || `thread-${Date.now()}`,
              runId: variables?.data?.runId || `run-${Date.now()}`,
              extensions: {},
              status: {
                code: "SUCCESS",
                __typename: "BaseResponseStatus",
              },
              messages: [
                {
                  __typename: "TextMessageOutput",
                  id: `msg-${Date.now()}`,
                  createdAt: new Date().toISOString(),
                  content: [
                    "Hello! I'm your AltZero AI assistant. I can help you navigate the platform, query databases, and search the knowledge base. What would you like to do?",
                  ],
                  role: "assistant",
                  parentMessageId: messages[messages.length - 1]?.id || null,
                  status: {
                    code: "SUCCESS",
                    __typename: "SuccessMessageStatus",
                  },
                },
              ],
              metaEvents: [],
              __typename: "CopilotResponse",
            },
          },
        };

        return res.json(response);
      }
    }

    // Handle availableAgents query
    if (operationName === "availableAgents") {
      console.log("🤖 Handling availableAgents query");

      const response = {
        data: {
          availableAgents: {
            agents: [
              {
                name: "AltZero AI",
                id: "altzero-ai",
                description:
                  "Intelligent assistant for the AltZero platform with navigation, database, and knowledge base capabilities",
                __typename: "Agent",
              },
            ],
            __typename: "AvailableAgents",
          },
        },
      };

      return res.json(response);
    }

    // Default response for other operations
    return res.json({
      data: {
        message: "CopilotKit endpoint is active",
        operationName,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error("CopilotKit error:", error);
    res.status(500).json({
      error: "CopilotKit error",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
});

// Health check endpoint (no API key required)
router.get("/health", async (req, res) => {
  try {
    // Check if OpenAI API key is configured
    const hasApiKey = !!process.env.OPENAI_API_KEY;

    // Basic health check
    const status = {
      status: "healthy",
      services: {
        openai: hasApiKey ? "configured" : "not_configured",
        copilotkit: "active",
      },
      timestamp: new Date().toISOString(),
    };

    res.status(200).json(status);
  } catch (error) {
    console.error("AI Chat health check error:", error);
    res.status(503).json({
      status: "unhealthy",
      error: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
    });
  }
});

export default router;
