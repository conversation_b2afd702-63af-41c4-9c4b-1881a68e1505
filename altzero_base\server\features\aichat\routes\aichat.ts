import express from "express";
import { validate<PERSON><PERSON><PERSON><PERSON> } from "../../../base/common/routes/auth";
import OpenAI from "openai";

const router = express.Router();

// Additional CORS middleware specifically for CopilotKit
router.use((req, res, next) => {
  res.header("Access-Control-Allow-Origin", "http://localhost:5173");
  res.header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
  res.header(
    "Access-Control-Allow-Headers",
    "Content-Type, Authorization, x-api-key, x-user-id, Accept, Origin, X-Requested-With"
  );
  res.header("Access-Control-Allow-Credentials", "true");

  // <PERSON>le preflight requests
  if (req.method === "OPTIONS") {
    res.status(200).end();
    return;
  }

  next();
});

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Available actions for the AI assistant
const availableActions = [
  {
    name: "queryDatabase",
    description:
      "Query the AltZero database using natural language. Use @entity syntax like '@customers John' or '@orders today'",
    parameters: {
      query: "Natural language database query using @entity syntax",
    },
  },
  {
    name: "navigateToPage",
    description: "Navigate to different pages in the AltZero platform",
    parameters: {
      page: "Page to navigate to (dashboard, knowledge, teams, settings, pseo, scopingai, crm)",
    },
  },
  {
    name: "searchKnowledgeBase",
    description:
      "Search through the knowledge base documents for relevant information",
    parameters: {
      searchQuery: "Search query to find relevant documents",
      maxResults: "Maximum number of results to return (default: 5)",
    },
  },
];

// Function to execute actions
async function executeAction(actionName: string, parameters: any) {
  console.log(`🔧 Executing action: ${actionName}`, parameters);

  switch (actionName) {
    case "queryDatabase":
      const { query } = parameters;
      console.log("🔍 Database query action:", query);
      return {
        success: true,
        message: `Database query executed: ${query}`,
        results: [],
        note: "Database integration coming soon! This action is working correctly.",
      };

    case "navigateToPage":
      const { page } = parameters;
      console.log("🧭 Navigation action:", page);

      const pageMap: Record<string, string> = {
        dashboard: "/",
        knowledge: "/knowledge",
        "knowledge base": "/knowledge",
        teams: "/teams",
        settings: "/settings",
        profile: "/profile",
        pseo: "/pseo",
        scopingai: "/scopingai",
        crm: "/crm",
        chat: "/ai-chat",
      };

      const targetPage =
        pageMap[page.toLowerCase()] || `/${page.toLowerCase()}`;

      return {
        success: true,
        message: `Navigation to ${page} prepared`,
        targetPage,
        action: "navigate",
      };

    case "searchKnowledgeBase":
      const { searchQuery, maxResults = 5 } = parameters;
      console.log("📚 Knowledge base search action:", searchQuery);

      return {
        success: true,
        message: `Knowledge base search completed for: ${searchQuery}`,
        query: searchQuery,
        results: [],
        totalFound: 0,
        note: "Knowledge base integration coming soon! This action is working correctly.",
      };

    default:
      throw new Error(`Unknown action: ${actionName}`);
  }
}

// Handle OPTIONS requests for CORS preflight
router.options("/", (req, res) => {
  res.status(200).end();
});

// OpenAI-compatible chat endpoint for CopilotKit
router.post("/", validateApiKey, async (req, res) => {
  try {
    console.log("🤖 CopilotKit chat request received");

    const {
      messages,
      model = "gpt-4",
      temperature = 0.7,
      max_tokens = 2048,
      tools,
    } = req.body;

    if (!messages || !Array.isArray(messages)) {
      return res.status(400).json({ error: "Messages array is required" });
    }

    // Add system message about available actions
    const systemMessage = {
      role: "system",
      content: `You are AltZero AI, an intelligent assistant for the AltZero platform. You have access to these powerful actions:

${availableActions
  .map(
    (action) =>
      `- **${action.name}**: ${action.description}
   Parameters: ${Object.entries(action.parameters)
     .map(([key, desc]) => `${key} (${desc})`)
     .join(", ")}`
  )
  .join("\n\n")}

When users ask for database information, navigation, or document searches, use the appropriate action. Always be helpful and leverage these capabilities!`,
    };

    const messagesWithSystem = [systemMessage, ...messages];

    // Check if there are any tool calls to execute
    if (tools && tools.length > 0) {
      console.log(
        "🔧 Tools available:",
        tools.map((t: any) => t.function?.name)
      );
    }

    // Call OpenAI API
    const completion = await openai.chat.completions.create({
      model,
      messages: messagesWithSystem,
      temperature,
      max_tokens,
      stream: false,
      tools: tools || undefined,
    });

    // Handle tool calls if present
    if (completion.choices[0]?.message?.tool_calls) {
      const toolCalls = completion.choices[0].message.tool_calls;
      const toolResults = [];

      for (const toolCall of toolCalls) {
        try {
          const actionName = toolCall.function.name;
          const parameters = JSON.parse(toolCall.function.arguments);
          const result = await executeAction(actionName, parameters);

          toolResults.push({
            tool_call_id: toolCall.id,
            role: "tool",
            content: JSON.stringify(result),
          });
        } catch (error) {
          toolResults.push({
            tool_call_id: toolCall.id,
            role: "tool",
            content: JSON.stringify({
              error: error instanceof Error ? error.message : "Unknown error",
            }),
          });
        }
      }

      // If there were tool calls, we need to continue the conversation
      if (toolResults.length > 0) {
        const followUpMessages = [
          ...messagesWithSystem,
          completion.choices[0].message,
          ...toolResults,
        ];

        const followUpCompletion = await openai.chat.completions.create({
          model,
          messages: followUpMessages,
          temperature,
          max_tokens,
          stream: false,
        });

        return res.json(followUpCompletion);
      }
    }

    // Return OpenAI-compatible response
    res.json(completion);
  } catch (error) {
    console.error("CopilotKit runtime error:", error);
    res.status(500).json({
      error: "CopilotKit runtime error",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
});

// Health check endpoint
router.get("/health", async (req, res) => {
  try {
    // Check if OpenAI API key is configured
    const hasApiKey = !!process.env.OPENAI_API_KEY;

    // Basic health check
    const status = {
      status: "healthy",
      services: {
        openai: hasApiKey ? "configured" : "not_configured",
        copilotkit: "active",
      },
      timestamp: new Date().toISOString(),
    };

    res.status(200).json(status);
  } catch (error) {
    console.error("AI Chat health check error:", error);
    res.status(503).json({
      status: "unhealthy",
      error: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
    });
  }
});

export default router;
