import express from "express";
import { validate<PERSON><PERSON><PERSON><PERSON> } from "../../../base/common/routes/auth";
import {
  CopilotRuntime,
  copilotRuntimeNodeHttpEndpoint,
} from "@copilotkit/runtime";
import { OpenAIAdapter } from "@copilotkit/backend";
import OpenAI from "openai";

const router = express.Router();

// Additional CORS middleware specifically for CopilotKit
router.use((req, res, next) => {
  res.header("Access-Control-Allow-Origin", "http://localhost:5173");
  res.header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
  res.header(
    "Access-Control-Allow-Headers",
    "Content-Type, Authorization, x-api-key, x-user-id, Accept, Origin, X-Requested-With"
  );
  res.header("Access-Control-Allow-Credentials", "true");

  // <PERSON>le preflight requests
  if (req.method === "OPTIONS") {
    res.status(200).end();
    return;
  }

  next();
});

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Create OpenAI adapter for CopilotKit
const serviceAdapter = new OpenAIAdapter({ openai });

// Define backend actions for CopilotKit
const actions = [
  {
    name: "navigateToPage",
    description: "Navigate to different pages in the AltZero platform. Use this when users want to go to dashboard, settings, knowledge base, etc.",
    parameters: [
      {
        name: "page",
        type: "string" as const,
        description: "Page to navigate to (dashboard, knowledge, teams, settings, pseo, scopingai, crm)",
        required: true,
      },
    ],
    handler: async ({ page }: { page: string }) => {
      console.log("🧭 Navigation action triggered:", page);
      
      const pageMap: Record<string, string> = {
        dashboard: "/",
        knowledge: "/knowledge",
        "knowledge base": "/knowledge",
        teams: "/teams",
        settings: "/settings",
        profile: "/profile",
        pseo: "/pseo",
        scopingai: "/scopingai",
        crm: "/crm",
        chat: "/ai-chat",
      };

      const targetPage = pageMap[page.toLowerCase()] || `/${page.toLowerCase()}`;
      
      // Return navigation instruction that frontend can handle
      return {
        success: true,
        message: `Navigating to ${page}...`,
        targetPage,
        action: "navigate",
        instruction: `Please navigate to ${targetPage}`,
      };
    },
  },
  {
    name: "queryDatabase",
    description: "Query the AltZero database using natural language. Use this for finding customers, orders, users, etc.",
    parameters: [
      {
        name: "query",
        type: "string" as const,
        description: "Natural language database query (e.g., 'Find all customers from this month', 'Show pending orders')",
        required: true,
      },
    ],
    handler: async ({ query }: { query: string }) => {
      console.log("🔍 Database query action triggered:", query);
      
      // TODO: Implement actual database querying logic
      return {
        success: true,
        message: `Database query executed: ${query}`,
        results: [
          { id: 1, name: "Sample Customer", status: "active" },
          { id: 2, name: "Another Customer", status: "pending" },
        ],
        note: "This is a demo response. Real database integration coming soon!",
      };
    },
  },
  {
    name: "searchKnowledgeBase",
    description: "Search through the knowledge base documents for relevant information",
    parameters: [
      {
        name: "searchQuery",
        type: "string" as const,
        description: "Search query to find relevant documents",
        required: true,
      },
      {
        name: "maxResults",
        type: "number" as const,
        description: "Maximum number of results to return (default: 5)",
        required: false,
      },
    ],
    handler: async ({ searchQuery, maxResults = 5 }: { searchQuery: string; maxResults?: number }) => {
      console.log("📚 Knowledge base search action triggered:", searchQuery);
      
      // TODO: Implement actual knowledge base search
      return {
        success: true,
        message: `Knowledge base search completed for: ${searchQuery}`,
        query: searchQuery,
        results: [
          { id: 1, title: "AI Documentation", snippet: "Information about AI features..." },
          { id: 2, title: "User Guide", snippet: "How to use the platform..." },
        ],
        totalFound: 2,
        note: "This is a demo response. Real knowledge base integration coming soon!",
      };
    },
  },
];

// Create CopilotKit runtime with actions
const runtime = new CopilotRuntime({
  actions: () => actions,
});

// CopilotKit runtime endpoint - proper implementation with backend actions
router.all("/", validateApiKey, async (req, res) => {
  try {
    console.log("🤖 CopilotKit runtime request received with backend actions");
    console.log("Method:", req.method);
    console.log("Headers:", req.headers);

    // Handle the CopilotKit request using the runtime
    const { handleRequest } = copilotRuntimeNodeHttpEndpoint({
      runtime,
      serviceAdapter,
      endpoint: "/api/aichat",
    });

    // Create a proper Request object for CopilotKit
    const url = `${req.protocol}://${req.get("host")}${req.originalUrl}`;
    const headers = new Headers();
    
    // Copy headers from Express request
    Object.entries(req.headers).forEach(([key, value]) => {
      if (typeof value === "string") {
        headers.set(key, value);
      } else if (Array.isArray(value)) {
        headers.set(key, value.join(", "));
      }
    });

    // Create the request body
    let body: string | undefined;
    if (req.method !== "GET" && req.method !== "HEAD" && req.body) {
      body = typeof req.body === "string" ? req.body : JSON.stringify(req.body);
    }

    const copilotRequest = new Request(url, {
      method: req.method,
      headers,
      body,
    });

    return await handleRequest(copilotRequest, res as any);
  } catch (error) {
    console.error("CopilotKit runtime error:", error);
    res.status(500).json({
      error: "CopilotKit runtime error",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
});

// Health check endpoint
router.get("/health", async (req, res) => {
  try {
    // Check if OpenAI API key is configured
    const hasApiKey = !!process.env.OPENAI_API_KEY;
    
    // Basic health check
    const status = {
      status: "healthy",
      services: {
        openai: hasApiKey ? "configured" : "not_configured",
        copilotkit: "active",
        actions: actions.length,
      },
      timestamp: new Date().toISOString(),
    };

    res.status(200).json(status);
  } catch (error) {
    console.error("AI Chat health check error:", error);
    res.status(503).json({
      status: "unhealthy",
      error: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
    });
  }
});

export default router;
