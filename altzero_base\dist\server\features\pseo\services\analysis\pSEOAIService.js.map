{"version": 3, "file": "pSEOAIService.js", "sourceRoot": "", "sources": ["../../../../../../server/features/pseo/services/analysis/pSEOAIService.ts"], "names": [], "mappings": ";;;;;;AAAA,oDAA4B;AAsE5B,MAAM,aAAa;IAKjB;QAJQ,WAAM,GAAkB,IAAI,CAAC;QAC7B,cAAS,GAAY,KAAK,CAAC;QAC3B,iBAAY,GAAW,aAAa,CAAC;QAG3C,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAC;YAC3E,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACzB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC;gBACH,IAAI,CAAC,MAAM,GAAG,IAAI,gBAAM,CAAC;oBACvB,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc;iBACnC,CAAC,CAAC;gBACH,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;gBACtB,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;YAC5D,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;gBAC9D,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YACzB,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,QAAgB;QACxC,yCAAyC;QACzC,MAAM,cAAc,GAAG,iCAAiC,CAAC;QACzD,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QAE7C,IAAI,KAAK,EAAE,CAAC;YACV,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACzB,CAAC;QAED,gEAAgE;QAChE,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAI,QAAgB,EAAE,OAAe;QAC5D,IAAI,CAAC;YACH,MAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YACzD,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;QACrC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iBAAiB,OAAO,QAAQ,EAAE,KAAK,CAAC,CAAC;YACvD,OAAO,CAAC,KAAK,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;YACzC,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC;YACrE,MAAM,IAAI,KAAK,CAAC,mBAAmB,OAAO,WAAW,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,OAAsB;QAC3C,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;QAC3E,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,wCAAwC,OAAO,CAAC,MAAM,IAAI,WAAW,EAAE,CAAC,CAAC;YAErF,MAAM,QAAQ,GAAyD,EAAE,CAAC;YAE1E,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;gBAC1B,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,OAAO,CAAC,aAAa;iBAC/B,CAAC,CAAC;YACL,CAAC;YAED,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,OAAO,CAAC,MAAM;aACxB,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBAC3D,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,YAAY;gBACzC,QAAQ;gBACR,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,GAAG;gBACvC,UAAU,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI;aACtC,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE9C,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;gBAC7C,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;YAC9D,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO;gBAC9C,KAAK,EAAE;oBACL,YAAY,EAAE,UAAU,CAAC,KAAK,EAAE,aAAa,IAAI,CAAC;oBAClD,gBAAgB,EAAE,UAAU,CAAC,KAAK,EAAE,iBAAiB,IAAI,CAAC;oBAC1D,WAAW,EAAE,UAAU,CAAC,KAAK,EAAE,YAAY,IAAI,CAAC;iBACjD;gBACD,KAAK,EAAE,UAAU,CAAC,KAAK;gBACvB,cAAc;aACf,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAC1D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,OAA4B;QACpD,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;QAC3E,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,eAAe,GAAG,CAAC,CAAC;QAExB,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,qCAAqC,OAAO,CAAC,GAAG,WAAW,OAAO,CAAC,MAAM,IAAI,WAAW,EAAE,CAAC,CAAC;YAExG,yBAAyB;YACzB,MAAM,eAAe,GAAG;;eAEf,OAAO,CAAC,GAAG;WACf,OAAO,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAgD/B,CAAC;YAEG,uBAAuB;YACvB,MAAM,aAAa,GAAG;;eAEb,OAAO,CAAC,GAAG;WACf,OAAO,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAkC/B,CAAC;YAEG,gCAAgC;YAChC,MAAM,CAAC,iBAAiB,EAAE,eAAe,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAC7D,IAAI,CAAC,gBAAgB,CAAC;oBACpB,MAAM,EAAE,eAAe;oBACvB,KAAK,EAAE,IAAI,CAAC,YAAY;oBACxB,WAAW,EAAE,GAAG;oBAChB,SAAS,EAAE,IAAI;oBACf,aAAa,EAAE,2GAA2G;oBAC1H,MAAM,EAAE,OAAO,CAAC,MAAM;iBACvB,CAAC;gBACF,IAAI,CAAC,gBAAgB,CAAC;oBACpB,MAAM,EAAE,aAAa;oBACrB,KAAK,EAAE,IAAI,CAAC,YAAY;oBACxB,WAAW,EAAE,GAAG;oBAChB,SAAS,EAAE,IAAI;oBACf,aAAa,EAAE,yGAAyG;oBACxH,MAAM,EAAE,OAAO,CAAC,MAAM;iBACvB,CAAC;aACH,CAAC,CAAC;YAEH,eAAe,GAAG,iBAAiB,CAAC,KAAK,CAAC,WAAW,GAAG,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC;YAE1F,2BAA2B;YAC3B,IAAI,iBAAwC,CAAC;YAC7C,IAAI,eAAoC,CAAC;YAEzC,IAAI,CAAC;gBACH,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CACxC,iBAAiB,CAAC,OAAO,EACzB,wBAAwB,CACzB,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;gBAC/D,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;YACrE,CAAC;YAED,IAAI,CAAC;gBACH,eAAe,GAAG,IAAI,CAAC,iBAAiB,CACtC,eAAe,CAAC,OAAO,EACvB,sBAAsB,CACvB,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;gBAC7D,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;YACnE,CAAC;YAED,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE9C,OAAO;gBACL,SAAS,EAAE,iBAAiB;gBAC5B,OAAO,EAAE,eAAe;gBACxB,cAAc;gBACd,UAAU,EAAE,eAAe;aAC5B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACxD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,sBAAsB,CAC1B,iBAAwC,EACxC,eAAoC,EACpC,GAAW,EACX,MAAc;QAEd,MAAM,YAAY,GAAG;;OAElB,GAAG;UACA,MAAM;;;EAGd,IAAI,CAAC,SAAS,CAAC,iBAAiB,EAAE,IAAI,EAAE,CAAC,CAAC;;;EAG1C,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC;;;;;;;;;6FASmD,CAAC;QAE1F,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC;gBAC3C,MAAM,EAAE,YAAY;gBACpB,KAAK,EAAE,IAAI,CAAC,YAAY;gBACxB,WAAW,EAAE,GAAG;gBAChB,SAAS,EAAE,IAAI;gBACf,aAAa,EAAE,mHAAmH;aACnI,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC,OAAO,CAAC;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAC1D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW;QACf,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACpC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC;YACH,gCAAgC;YAChC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACzD,KAAK,EAAE,IAAI,CAAC,YAAY;gBACxB,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;gBAC9C,UAAU,EAAE,CAAC;aACd,CAAC,CAAC;YAEH,OAAO,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC;QACjD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC5D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,+CAA+C;IAC/C,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED,uBAAuB;IACvB,KAAK,CAAC,kBAAkB;QACtB,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACpC,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YAC/C,OAAO,MAAM,CAAC,IAAI;iBACf,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;iBACzC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC;iBACtB,IAAI,EAAE,CAAC;QACZ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC;IAED,uBAAuB;IACvB,eAAe,CAAC,KAAa;QAC3B,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,OAAO,CAAC,GAAG,CAAC,oCAAoC,KAAK,EAAE,CAAC,CAAC;IAC3D,CAAC;CACF;AAEY,QAAA,aAAa,GAAG,IAAI,aAAa,EAAE,CAAC"}