// =====================================================
// UI CONTROL NODE - REAL FRONTEND CONTROL
// =====================================================

import { BaseNode } from '../types/NodeTypes';
import { WorkflowContext } from '../types/WorkflowState';

export class UIControlNode implements BaseNode {
  name = 'ui_control';
  description = 'Controls the frontend UI, triggers navigation, opens modals, and performs UI actions';

  async execute(context: WorkflowContext): Promise<any> {
    const { state, tools, logger } = context;
    const startTime = Date.now();

    try {
      logger.info('Starting UI control processing', {
        workflow_id: state.workflow_id,
        current_intent: state.current_intent
      });

      // Check if there are any UI actions to perform
      const uiActions = this.extractUIActions(state);
      
      if (uiActions.length === 0) {
        logger.debug('No UI actions detected');
        return {
          processing_time: state.processing_time,
          last_updated: new Date().toISOString()
        };
      }

      const results = [];
      
      for (const action of uiActions) {
        const result = await this.executeUIAction(action, state, logger);
        results.push(result);
      }

      const executionTime = Date.now() - startTime;

      return {
        ui_actions: results,
        processing_time: (state.processing_time || 0) + executionTime,
        last_updated: new Date().toISOString(),
        node_data: {
          ...state.node_data,
          ui_control: {
            actions_executed: results.length,
            execution_time: executionTime,
            results: results
          }
        }
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      logger.error('UI control processing failed', error);

      return {
        errors: [
          ...state.errors,
          {
            node_name: this.name,
            error_message: error instanceof Error ? error.message : 'Unknown UI control error',
            error_code: 'UI_CONTROL_FAILED',
            timestamp: new Date().toISOString(),
            recoverable: true
          }
        ],
        processing_time: (state.processing_time || 0) + executionTime,
        last_updated: new Date().toISOString()
      };
    }
  }

  // Extract UI actions from workflow state
  private extractUIActions(state: any): any[] {
    const actions = [];

    // Check for navigation actions from other nodes
    if (state.node_data?.crm_action?.data?.navigation_action) {
      actions.push(state.node_data.crm_action.data.navigation_action);
    }

    if (state.node_data?.navigation?.navigation_triggered) {
      actions.push({
        type: 'navigate',
        target: state.node_data.navigation.target_url
      });
    }

    // Check for specific UI commands in the message
    const message = state.messages[state.messages.length - 1]?.content.toLowerCase() || '';
    
    // Modal actions
    if (message.includes('open contact form') || message.includes('create new contact')) {
      actions.push({
        type: 'open_modal',
        modal: 'contact_form',
        data: this.extractFormData(message)
      });
    }

    if (message.includes('open company form') || message.includes('create new company')) {
      actions.push({
        type: 'open_modal',
        modal: 'company_form',
        data: this.extractFormData(message)
      });
    }

    // Search actions
    if (message.includes('search for') || message.includes('find')) {
      const searchTerm = this.extractSearchTerm(message);
      if (searchTerm) {
        actions.push({
          type: 'search',
          term: searchTerm,
          context: this.getSearchContext(message)
        });
      }
    }

    // Filter actions
    if (message.includes('filter by') || message.includes('show only')) {
      actions.push({
        type: 'filter',
        filters: this.extractFilters(message)
      });
    }

    return actions;
  }

  // Execute a specific UI action
  private async executeUIAction(action: any, state: any, logger: any): Promise<any> {
    logger.info('Executing UI action:', action);

    switch (action.type) {
      case 'navigate':
        return this.executeNavigation(action, state, logger);
      
      case 'open_modal':
        return this.executeOpenModal(action, state, logger);
      
      case 'search':
        return this.executeSearch(action, state, logger);
      
      case 'filter':
        return this.executeFilter(action, state, logger);
      
      case 'highlight':
        return this.executeHighlight(action, state, logger);
      
      default:
        logger.warn('Unknown UI action type:', action.type);
        return {
          success: false,
          action_type: action.type,
          error: 'Unknown action type'
        };
    }
  }

  // Execute navigation action
  private async executeNavigation(action: any, state: any, logger: any): Promise<any> {
    try {
      logger.info('Executing navigation to:', action.target);

      // Validate the target URL
      const validRoutes = [
        '/', '/dashboard', '/knowledge', '/teams', '/settings', '/profile',
        '/crm', '/crm/contacts', '/crm/companies', '/crm/opportunities', 
        '/crm/activities', '/crm/events', '/crm/contact-groups', '/crm/regions',
        '/pseo', '/scopingai', '/ai-chat'
      ];

      if (!validRoutes.includes(action.target)) {
        throw new Error(`Invalid navigation target: ${action.target}`);
      }

      return {
        success: true,
        action_type: 'navigate',
        target: action.target,
        frontend_action: {
          type: 'NAVIGATE',
          payload: {
            url: action.target,
            highlight_id: action.highlight_id,
            search_term: action.search_term
          }
        }
      };

    } catch (error) {
      logger.error('Navigation execution failed:', error);
      return {
        success: false,
        action_type: 'navigate',
        error: error instanceof Error ? error.message : 'Navigation failed'
      };
    }
  }

  // Execute modal opening
  private async executeOpenModal(action: any, state: any, logger: any): Promise<any> {
    try {
      logger.info('Opening modal:', action.modal);

      const modalConfigs = {
        contact_form: {
          component: 'ContactForm',
          title: 'Create New Contact',
          size: 'large'
        },
        company_form: {
          component: 'CompanyForm',
          title: 'Create New Company',
          size: 'medium'
        },
        opportunity_form: {
          component: 'OpportunityForm',
          title: 'Create New Opportunity',
          size: 'large'
        }
      };

      const config = modalConfigs[action.modal as keyof typeof modalConfigs];
      if (!config) {
        throw new Error(`Unknown modal: ${action.modal}`);
      }

      return {
        success: true,
        action_type: 'open_modal',
        modal: action.modal,
        frontend_action: {
          type: 'OPEN_MODAL',
          payload: {
            modal: action.modal,
            config: config,
            data: action.data || {}
          }
        }
      };

    } catch (error) {
      logger.error('Modal opening failed:', error);
      return {
        success: false,
        action_type: 'open_modal',
        error: error instanceof Error ? error.message : 'Modal opening failed'
      };
    }
  }

  // Execute search action
  private async executeSearch(action: any, state: any, logger: any): Promise<any> {
    try {
      logger.info('Executing search:', action.term);

      return {
        success: true,
        action_type: 'search',
        search_term: action.term,
        context: action.context,
        frontend_action: {
          type: 'SEARCH',
          payload: {
            term: action.term,
            context: action.context,
            auto_execute: true
          }
        }
      };

    } catch (error) {
      logger.error('Search execution failed:', error);
      return {
        success: false,
        action_type: 'search',
        error: error instanceof Error ? error.message : 'Search failed'
      };
    }
  }

  // Execute filter action
  private async executeFilter(action: any, state: any, logger: any): Promise<any> {
    try {
      logger.info('Executing filter:', action.filters);

      return {
        success: true,
        action_type: 'filter',
        filters: action.filters,
        frontend_action: {
          type: 'FILTER',
          payload: {
            filters: action.filters,
            auto_apply: true
          }
        }
      };

    } catch (error) {
      logger.error('Filter execution failed:', error);
      return {
        success: false,
        action_type: 'filter',
        error: error instanceof Error ? error.message : 'Filter failed'
      };
    }
  }

  // Execute highlight action
  private async executeHighlight(action: any, state: any, logger: any): Promise<any> {
    try {
      logger.info('Executing highlight:', action.target_id);

      return {
        success: true,
        action_type: 'highlight',
        target_id: action.target_id,
        frontend_action: {
          type: 'HIGHLIGHT',
          payload: {
            target_id: action.target_id,
            duration: action.duration || 3000,
            style: action.style || 'pulse'
          }
        }
      };

    } catch (error) {
      logger.error('Highlight execution failed:', error);
      return {
        success: false,
        action_type: 'highlight',
        error: error instanceof Error ? error.message : 'Highlight failed'
      };
    }
  }

  // Extract form data from message
  private extractFormData(message: string): any {
    const data: any = {};

    // Extract name
    const nameMatch = message.match(/(?:named|called)\s+([a-zA-Z\s]+)/i);
    if (nameMatch) {
      data.name = nameMatch[1].trim();
    }

    // Extract email
    const emailMatch = message.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
    if (emailMatch) {
      data.email = emailMatch[1];
    }

    // Extract phone
    const phoneMatch = message.match(/(?:phone|number)\s*:?\s*([+]?[\d\s\-\(\)]+)/i);
    if (phoneMatch) {
      data.phone = phoneMatch[1].replace(/\s+/g, '');
    }

    return data;
  }

  // Extract search term from message
  private extractSearchTerm(message: string): string | null {
    const searchMatch = message.match(/(?:search for|find|look for)\s+([a-zA-Z\s@.]+)/i);
    return searchMatch ? searchMatch[1].trim() : null;
  }

  // Get search context (which section to search in)
  private getSearchContext(message: string): string {
    if (message.includes('contact')) return 'contacts';
    if (message.includes('company')) return 'companies';
    if (message.includes('opportunity')) return 'opportunities';
    if (message.includes('activity')) return 'activities';
    if (message.includes('event')) return 'events';
    
    return 'all';
  }

  // Extract filters from message
  private extractFilters(message: string): any {
    const filters: any = {};

    // Extract tag filters
    const tagMatch = message.match(/(?:tagged|tag)\s+([a-zA-Z\s,]+)/i);
    if (tagMatch) {
      filters.tags = tagMatch[1].split(',').map(tag => tag.trim());
    }

    // Extract date filters
    if (message.includes('today')) {
      filters.date_range = 'today';
    } else if (message.includes('this week')) {
      filters.date_range = 'this_week';
    } else if (message.includes('this month')) {
      filters.date_range = 'this_month';
    }

    // Extract status filters
    if (message.includes('active')) {
      filters.status = 'active';
    } else if (message.includes('inactive')) {
      filters.status = 'inactive';
    }

    return filters;
  }
}
