{"version": 3, "file": "DocumentProcessorService.js", "sourceRoot": "", "sources": ["../../../../../../server/features/scopingAi/services/document/DocumentProcessorService.ts"], "names": [], "mappings": ";;;;;;AAAA,4CAAoB;AACpB,gDAAwB;AAkBxB,MAAa,wBAAwB;IAQnC,YAAoB,UAAqC,EAAE;QAAvC,YAAO,GAAP,OAAO,CAAgC;QAP1C,mBAAc,GAA8B;YAC3D,WAAW,EAAE,IAAI;YACjB,kBAAkB,EAAE,KAAK;YACzB,OAAO,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE,OAAO;YAClC,YAAY,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC;SAClD,CAAC;QAGA,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,GAAG,OAAO,EAAE,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CACnB,QAAgB,EAChB,YAAoB,EACpB,OAAmC;QAEnC,IAAI,CAAC;YACH,MAAM,iBAAiB,GAAG,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,OAAO,EAAE,CAAC;YAE1D,OAAO,CAAC,GAAG,CAAC,2BAA2B,YAAY,EAAE,CAAC,CAAC;YAEvD,kBAAkB;YAClB,MAAM,KAAK,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACpC,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,iBAAiB,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,OAAQ,CAAC,EAAE,CAAC;gBAC7E,MAAM,IAAI,KAAK,CAAC,6CAA6C,iBAAiB,CAAC,OAAO,QAAQ,CAAC,CAAC;YAClG,CAAC;YAED,qBAAqB;YACrB,MAAM,GAAG,GAAG,cAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;YAEtE,sBAAsB;YACtB,IAAI,iBAAiB,CAAC,YAAY,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBACpF,MAAM,IAAI,KAAK,CAAC,cAAc,GAAG,kBAAkB,CAAC,CAAC;YACvD,CAAC;YAED,IAAI,OAAO,GAAG,EAAE,CAAC;YACjB,IAAI,QAAQ,GAA4B;gBACtC,YAAY;gBACZ,QAAQ,EAAE,GAAG;gBACb,QAAQ,EAAE,KAAK,CAAC,IAAI;gBACpB,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACrC,gBAAgB,EAAE,OAAO;aAC1B,CAAC;YAEF,6BAA6B;YAC7B,QAAQ,GAAG,EAAE,CAAC;gBACZ,KAAK,KAAK,CAAC;gBACX,KAAK,IAAI;oBACP,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;oBAC/C,MAAM;gBACR,KAAK,KAAK;oBACR,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;oBAC9C,QAAQ,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;oBAC7C,MAAM;gBACR,KAAK,KAAK,CAAC;gBACX,KAAK,MAAM;oBACT,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;oBAC/C,QAAQ,CAAC,gBAAgB,GAAG,iBAAiB,CAAC;oBAC9C,MAAM;gBACR;oBACE,sBAAsB;oBACtB,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;oBAC/C,QAAQ,CAAC,gBAAgB,GAAG,eAAe,CAAC;YAChD,CAAC;YAED,6BAA6B;YAC7B,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YAErC,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC5C,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;YAC3D,CAAC;YAED,MAAM,YAAY,GAAsB;gBACtC,EAAE,EAAE,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;gBAClE,KAAK,EAAE,YAAY;gBACnB,OAAO;gBACP,IAAI,EAAE,GAAG;gBACT,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,QAAQ,EAAE;oBACR,GAAG,QAAQ;oBACX,aAAa,EAAE,OAAO,CAAC,MAAM;oBAC7B,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;iBACpC;aACF,CAAC;YAEF,OAAO,CAAC,GAAG,CAAC,sCAAsC,YAAY,CAAC,EAAE,EAAE,CAAC,CAAC;YACrE,OAAO,CAAC,GAAG,CAAC,sBAAsB,OAAO,CAAC,MAAM,4BAA4B,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC;YAElG,OAAO,YAAY,CAAC;QACtB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO,CAAC,KAAK,CAAC,+BAA+B,YAAY,GAAG,EAAE,KAAK,CAAC,CAAC;YACrE,MAAM,IAAI,KAAK,CAAC,+BAA+B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAAC,QAAgB;QAC5C,IAAI,CAAC;YACH,OAAO,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAC5C,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,6BAA6B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc,CAAC,QAAgB;QAC3C,IAAI,CAAC;YACH,iFAAiF;YACjF,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAC;YACrE,OAAO,qBAAqB,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,gJAAgJ,CAAC;QACtM,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,+BAA+B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAAC,QAAgB;QAC5C,IAAI,CAAC;YACH,kFAAkF;YAClF,OAAO,CAAC,GAAG,CAAC,iEAAiE,CAAC,CAAC;YAC/E,OAAO,+BAA+B,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,2JAA2J,CAAC;QAC3N,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,gCAAgC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,OAAe;QAClC,OAAO,OAAO;YACZ,8BAA8B;aAC7B,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;YACrB,4BAA4B;aAC3B,OAAO,CAAC,mCAAmC,EAAE,EAAE,CAAC;YACjD,wBAAwB;aACvB,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC;aACtB,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC;YACrB,+BAA+B;aAC9B,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC;YAC3B,OAAO;aACN,IAAI,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACK,UAAU,CAAC,OAAe;QAChC,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC;IAC5E,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,QAAgB;QAC9B,MAAM,GAAG,GAAG,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;QAClE,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,QAAQ,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC;IAC3D,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,IAAI,EAAE,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,IAAY;QAC1B,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,OAAQ,CAAC,CAAC;IACxE,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,OAAQ,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,KAAa;QAC1B,MAAM,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC1C,IAAI,KAAK,KAAK,CAAC;YAAE,OAAO,SAAS,CAAC;QAClC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;QACvD,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,GAAsB;QACpC,OAAO;YACL,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,KAAK,EAAE,GAAG,CAAC,KAAK;YAChB,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,aAAa,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC;YAC5C,aAAa,EAAE,GAAG,CAAC,OAAO,CAAC,MAAM;YACjC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC;YACvC,GAAG,GAAG,CAAC,QAAQ;SAChB,CAAC;IACJ,CAAC;CACF;AAvND,4DAuNC;AAEY,QAAA,wBAAwB,GAAG,IAAI,wBAAwB,EAAE,CAAC"}