"use strict";
// =====================================================
// RAPIDAPI GATEWAY FOR SEO TOOLS
// =====================================================
Object.defineProperty(exports, "__esModule", { value: true });
exports.RapidAPIGateway = void 0;
class RapidAPIGateway {
    constructor(config) {
        this.lastRequestTime = 0;
        this.config = config;
        this.providers = new Map();
        this.initializeProviders();
    }
    // Initialize available providers from environment
    initializeProviders() {
        const providers = [
            {
                name: 'ubersuggest',
                host: process.env.RAPIDAPI_UBERSUGGEST_HOST || 'ubersuggest-keyword-ideas.p.rapidapi.com',
                enabled: process.env.RAPIDAPI_UBERSUGGEST_ENABLED === 'true',
                endpoints: {
                    keyword_research: '/keyword-research'
                }
            },
            {
                name: 'similarweb',
                host: process.env.RAPIDAPI_SIMILARWEB_HOST || 'similarweb-traffic.p.rapidapi.com',
                enabled: process.env.RAPIDAPI_SIMILARWEB_ENABLED === 'true',
                endpoints: {
                    domain_traffic: '/traffic'
                }
            },
            {
                name: 'semrush',
                host: process.env.RAPIDAPI_SEMRUSH_HOST || 'semrush-keyword-research.p.rapidapi.com',
                enabled: process.env.RAPIDAPI_SEMRUSH_ENABLED === 'true',
                endpoints: {
                    keyword_overview: '/keyword/overview',
                    related_keywords: '/keyword/related',
                    keyword_difficulty: '/keyword/difficulty'
                }
            },
            {
                name: 'ahrefs',
                host: process.env.RAPIDAPI_AHREFS_HOST || 'ahrefs-keyword-research.p.rapidapi.com',
                enabled: process.env.RAPIDAPI_AHREFS_ENABLED === 'true',
                endpoints: {
                    keyword_overview: '/keyword/overview',
                    keyword_ideas: '/keyword/ideas',
                    keyword_difficulty: '/keyword/difficulty'
                }
            },
            {
                name: 'moz',
                host: process.env.RAPIDAPI_MOZ_HOST || 'moz-keyword-research.p.rapidapi.com',
                enabled: process.env.RAPIDAPI_MOZ_ENABLED === 'true',
                endpoints: {
                    keyword_overview: '/keyword/overview',
                    keyword_analysis: '/keyword/analysis',
                    keyword_suggestions: '/keyword/suggestions'
                }
            }
        ];
        providers.forEach(provider => {
            this.providers.set(provider.name, provider);
        });
    }
    // Get keyword data from the enabled provider
    async getKeywordData(keywords, options = {}) {
        if (!this.config.enabled || !this.config.apiKey) {
            throw new Error('RapidAPI not configured');
        }
        const ubersuggestProvider = this.providers.get('ubersuggest');
        if (!ubersuggestProvider || !ubersuggestProvider.enabled) {
            throw new Error('Ubersuggest provider not enabled. Please enable Ubersuggest in your .env file.');
        }
        console.log(`🔍 Using Ubersuggest via RapidAPI for keyword research`);
        try {
            const results = await this.getKeywordDataFromProvider(ubersuggestProvider, keywords, options);
            return this.deduplicateResults(results);
        }
        catch (error) {
            console.warn(`Failed to get keyword data from Ubersuggest:`, error);
            throw new Error(`RapidAPI Ubersuggest failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    // Get domain analysis data from SimilarWeb
    async getDomainData(domain, options = {}) {
        if (!this.config.enabled || !this.config.apiKey) {
            throw new Error('RapidAPI not configured');
        }
        const similarwebProvider = this.providers.get('similarweb');
        if (!similarwebProvider || !similarwebProvider.enabled) {
            throw new Error('SimilarWeb provider not enabled. Please enable SimilarWeb in your .env file.');
        }
        console.log(`🔍 Using SimilarWeb via RapidAPI for domain analysis`);
        try {
            const result = await this.makeRapidAPIRequest(similarwebProvider, 'domain_traffic', { domain });
            return result;
        }
        catch (error) {
            console.warn(`Failed to get domain data from SimilarWeb:`, error);
            throw new Error(`RapidAPI SimilarWeb failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    // Get the single enabled provider (priority order: Ubersuggest > Semrush > Ahrefs > Moz)
    getEnabledProvider() {
        const priorityOrder = ['ubersuggest', 'semrush', 'ahrefs', 'moz'];
        for (const providerName of priorityOrder) {
            const provider = this.providers.get(providerName);
            if (provider && provider.enabled) {
                return provider;
            }
        }
        return null;
    }
    // Get the appropriate related keywords endpoint for a provider
    getRelatedKeywordsEndpoint(provider) {
        if (provider.endpoints.keyword_suggestions) {
            return 'keyword_suggestions';
        }
        if (provider.endpoints.related_keywords) {
            return 'related_keywords';
        }
        if (provider.endpoints.keyword_ideas) {
            return 'keyword_ideas';
        }
        return null;
    }
    // Get keyword data from a specific provider
    async getKeywordDataFromProvider(provider, keywords, options) {
        const results = [];
        for (const keyword of keywords) {
            try {
                await this.enforceRateLimit();
                // Get keyword data from Ubersuggest
                const overview = await this.makeRapidAPIRequest(provider, 'keyword_research', { keyword, country: options.country || 'au' });
                // Get related keywords if available
                let relatedKeywords = [];
                const relatedEndpoint = this.getRelatedKeywordsEndpoint(provider);
                if (relatedEndpoint) {
                    try {
                        const related = await this.makeRapidAPIRequest(provider, relatedEndpoint, { keyword, limit: 5 });
                        relatedKeywords = this.extractRelatedKeywords(related, provider.name);
                    }
                    catch (error) {
                        // Related keywords are optional
                    }
                }
                // Convert provider-specific data to our format
                const keywordData = this.normalizeKeywordData(overview, keyword, provider.name);
                results.push(keywordData);
                // Add related keywords
                relatedKeywords.forEach(relatedKeyword => {
                    results.push({
                        keyword: relatedKeyword,
                        search_volume: Math.floor(keywordData.search_volume * 0.3), // Estimate
                        keyword_difficulty: keywordData.keyword_difficulty + Math.floor(Math.random() * 20) - 10,
                        cpc: keywordData.cpc * (0.5 + Math.random() * 0.5),
                        competition: keywordData.competition,
                        intent: this.inferKeywordIntent(relatedKeyword),
                        trend: 'stable',
                        data_source: `${provider.name}_related`
                    });
                });
            }
            catch (error) {
                console.warn(`Failed to get data for keyword ${keyword} from ${provider.name}:`, error);
                // Check if it's a rate limit error - if so, stop immediately
                if (error instanceof Error && (error.message.includes('429') ||
                    error.message.includes('Too Many Requests') ||
                    error.message.includes('rate limit'))) {
                    console.warn(`🚫 Rate limit hit for ${provider.name}. Stopping further requests to avoid waste.`);
                    break; // Stop processing more keywords
                }
                // Continue with next keyword for other types of errors
            }
        }
        return results;
    }
    // Make RapidAPI request
    async makeRapidAPIRequest(provider, endpoint, params) {
        const endpointPath = provider.endpoints[endpoint];
        if (!endpointPath) {
            throw new Error(`Endpoint ${endpoint} not available for ${provider.name}`);
        }
        const url = `https://${provider.host}${endpointPath}`;
        const queryParams = new URLSearchParams(params).toString();
        const fullUrl = `${url}?${queryParams}`;
        console.log(`🔍 Making RapidAPI request to: ${fullUrl}`);
        console.log(`🔑 Using API key: ${this.config.apiKey.substring(0, 10)}...`);
        console.log(`🏠 Host header: ${provider.host}`);
        let lastError = new Error('Unknown error');
        for (let attempt = 0; attempt <= this.config.retries; attempt++) {
            try {
                const controller = new AbortController();
                const timeout = setTimeout(() => controller.abort(), this.config.timeout);
                const response = await fetch(fullUrl, {
                    method: 'GET',
                    headers: {
                        'X-RapidAPI-Key': this.config.apiKey,
                        'X-RapidAPI-Host': provider.host,
                        'User-Agent': 'AltZero-pSEO-Bot/1.0'
                    },
                    signal: controller.signal
                });
                clearTimeout(timeout);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return await response.json();
            }
            catch (error) {
                lastError = error instanceof Error ? error : new Error('Unknown error');
                if (attempt < this.config.retries) {
                    // Exponential backoff
                    const delay = Math.pow(2, attempt) * 1000;
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }
        throw lastError;
    }
    // Normalize data from different providers to our format
    normalizeKeywordData(data, keyword, providerName) {
        switch (providerName) {
            case 'ubersuggest':
                return {
                    keyword,
                    search_volume: data.search_volume || data.volume || 0,
                    keyword_difficulty: data.difficulty || data.seo_difficulty || 50,
                    cpc: data.cpc || data.paid_difficulty || 0,
                    competition: this.mapCompetition(data.competition || data.paid_difficulty || 0.5),
                    intent: this.inferKeywordIntent(keyword),
                    trend: data.trend || 'stable',
                    data_source: 'ubersuggest_rapidapi'
                };
            case 'semrush':
                return {
                    keyword,
                    search_volume: data.search_volume || data.volume || 0,
                    keyword_difficulty: data.keyword_difficulty || data.kd || 50,
                    cpc: data.cpc || data.cost_per_click || 0,
                    competition: this.mapCompetition(data.competition || 0.5),
                    intent: this.inferKeywordIntent(keyword),
                    trend: data.trend || 'stable',
                    data_source: 'semrush_rapidapi'
                };
            case 'ahrefs':
                return {
                    keyword,
                    search_volume: data.search_volume || data.volume || 0,
                    keyword_difficulty: data.keyword_difficulty || data.kd || 50,
                    cpc: data.cpc || 0,
                    competition: this.mapCompetition(data.competition || 0.5),
                    intent: this.inferKeywordIntent(keyword),
                    trend: data.trend || 'stable',
                    data_source: 'ahrefs_rapidapi'
                };
            default:
                return {
                    keyword,
                    search_volume: data.search_volume || data.volume || 0,
                    keyword_difficulty: data.difficulty || 50,
                    cpc: data.cpc || 0,
                    competition: this.mapCompetition(data.competition || 0.5),
                    intent: this.inferKeywordIntent(keyword),
                    trend: 'stable',
                    data_source: `${providerName}_rapidapi`
                };
        }
    }
    // Extract related keywords from provider response
    extractRelatedKeywords(data, providerName) {
        if (!data)
            return [];
        switch (providerName) {
            case 'ubersuggest':
                return data.suggestions?.map((s) => s.keyword || s.term) || [];
            case 'semrush':
                return data.related_keywords?.map((k) => k.keyword || k.term) || [];
            case 'ahrefs':
                return data.keyword_ideas?.map((k) => k.keyword || k.term) || [];
            default:
                return data.keywords || data.suggestions || [];
        }
    }
    // Map competition values to our format
    mapCompetition(value) {
        if (value < 0.33)
            return 'low';
        if (value < 0.67)
            return 'medium';
        return 'high';
    }
    // Infer keyword intent
    inferKeywordIntent(keyword) {
        const keywordLower = keyword.toLowerCase();
        if (keywordLower.includes('buy') || keywordLower.includes('price') || keywordLower.includes('cost')) {
            return 'transactional';
        }
        if (keywordLower.includes('review') || keywordLower.includes('vs') || keywordLower.includes('best')) {
            return 'commercial';
        }
        if (keywordLower.includes('how') || keywordLower.includes('what') || keywordLower.includes('guide')) {
            return 'informational';
        }
        return 'informational';
    }
    // Deduplicate results
    deduplicateResults(results) {
        const seen = new Set();
        return results.filter(result => {
            const key = result.keyword.toLowerCase();
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        });
    }
    // Enforce rate limiting
    async enforceRateLimit() {
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;
        const minInterval = 1000 / this.config.rateLimit;
        if (timeSinceLastRequest < minInterval) {
            const delay = minInterval - timeSinceLastRequest;
            await new Promise(resolve => setTimeout(resolve, delay));
        }
        this.lastRequestTime = Date.now();
    }
    // Health check for RapidAPI
    async healthCheck() {
        const results = [];
        const activeProvider = this.getEnabledProvider();
        for (const [name, provider] of this.providers) {
            const isActive = activeProvider?.name === name;
            if (!provider.enabled) {
                results.push({ provider: name, status: false, active: false });
                continue;
            }
            try {
                await this.makeRapidAPIRequest(provider, 'keyword_overview', { keyword: 'test' });
                results.push({ provider: name, status: true, active: isActive });
            }
            catch (error) {
                results.push({ provider: name, status: false, active: isActive });
            }
        }
        return results;
    }
    // Get the active provider name
    getAvailableProviders() {
        const activeProvider = this.getEnabledProvider();
        return activeProvider ? [activeProvider.name] : [];
    }
    // Get all providers with their status
    getAllProvidersStatus() {
        const activeProvider = this.getEnabledProvider();
        return Array.from(this.providers.values()).map(provider => ({
            name: provider.name,
            enabled: provider.enabled,
            active: activeProvider?.name === provider.name
        }));
    }
}
exports.RapidAPIGateway = RapidAPIGateway;
//# sourceMappingURL=RapidAPIGateway.js.map