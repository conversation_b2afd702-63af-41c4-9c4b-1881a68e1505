"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.PluginLoader = void 0;
const registry_1 = require("./registry");
class PluginLoader {
    static async loadPlugins(app) {
        const enabledPlugins = (0, registry_1.getEnabledBackendPlugins)();
        console.log(`🚀 Loading ${enabledPlugins.length} backend plugins:`, enabledPlugins);
        for (const pluginName of enabledPlugins) {
            try {
                await this.mountPlugin(app, pluginName);
            }
            catch (error) {
                console.error(`❌ Failed to load backend plugin: ${pluginName}`, error);
            }
        }
        console.log(`✅ Backend plugin loading complete. Loaded: ${this.loadedPlugins.size} plugins`);
    }
    static async mountPlugin(app, pluginName) {
        try {
            console.log(`🔄 Loading backend plugin: ${pluginName}`);
            const pluginConfig = (0, registry_1.getBackendPluginConfig)(pluginName);
            if (!pluginConfig) {
                throw new Error(`No configuration found for plugin: ${pluginName}`);
            }
            // Try to import the plugin from features directory
            let pluginModule;
            try {
                // Try to import from features/{pluginName}/backend/index.ts
                const moduleImport = await Promise.resolve(`${`../features/${pluginName}/backend/index.ts`}`).then(s => __importStar(require(s)));
                pluginModule = moduleImport.default || moduleImport;
            }
            catch (importError) {
                // Fallback: try to import from features/{pluginName}/routes.ts
                try {
                    const routesImport = await Promise.resolve(`${`../features/${pluginName}/routes.ts`}`).then(s => __importStar(require(s)));
                    // Create a plugin wrapper for backward compatibility
                    pluginModule = {
                        router: routesImport.default || routesImport,
                        config: {
                            name: pluginConfig.name,
                            version: pluginConfig.version,
                            apiPrefix: pluginConfig.apiPrefix
                        }
                    };
                }
                catch (fallbackError) {
                    console.error(`Import attempts failed for ${pluginName}:`, { importError, fallbackError });
                    throw new Error(`Could not import plugin from any location: ${pluginName}`);
                }
            }
            if (!pluginModule.router) {
                throw new Error(`Plugin ${pluginName} does not export a router`);
            }
            // Run plugin initialization if available
            if (pluginModule.initialize) {
                console.log(`🔧 Initializing plugin: ${pluginName}`);
                await pluginModule.initialize();
            }
            // Apply plugin-specific middleware if available
            if (pluginModule.middleware && Array.isArray(pluginModule.middleware)) {
                pluginModule.middleware.forEach(middleware => {
                    app.use(pluginConfig.apiPrefix, middleware);
                });
            }
            // Mount the plugin router
            app.use(pluginConfig.apiPrefix, pluginModule.router);
            // Store the loaded plugin
            this.loadedPlugins.set(pluginName, pluginModule);
            console.log(`✅ Mounted backend plugin: ${pluginName} at ${pluginConfig.apiPrefix}`);
        }
        catch (error) {
            console.error(`❌ Error mounting backend plugin ${pluginName}:`, error);
            throw error;
        }
    }
    static getLoadedPlugins() {
        return this.loadedPlugins;
    }
    static getPlugin(pluginName) {
        return this.loadedPlugins.get(pluginName);
    }
    static async healthCheck() {
        const results = {};
        for (const [pluginName, plugin] of this.loadedPlugins) {
            try {
                if (plugin.healthCheck) {
                    results[pluginName] = await plugin.healthCheck();
                }
                else {
                    results[pluginName] = true; // Assume healthy if no health check
                }
            }
            catch (error) {
                console.error(`Health check failed for plugin ${pluginName}:`, error);
                results[pluginName] = false;
            }
        }
        return results;
    }
    static async unloadPlugin(pluginName) {
        const plugin = this.loadedPlugins.get(pluginName);
        if (!plugin) {
            return false;
        }
        try {
            // Run cleanup if available
            if (plugin.cleanup) {
                await plugin.cleanup();
            }
            this.loadedPlugins.delete(pluginName);
            console.log(`🗑️ Unloaded plugin: ${pluginName}`);
            return true;
        }
        catch (error) {
            console.error(`❌ Error unloading plugin ${pluginName}:`, error);
            return false;
        }
    }
}
exports.PluginLoader = PluginLoader;
PluginLoader.loadedPlugins = new Map();
//# sourceMappingURL=loader.js.map