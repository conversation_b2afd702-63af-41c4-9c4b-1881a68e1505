{"version": 3, "file": "ExecutiveSummaryNode.js", "sourceRoot": "", "sources": ["../../../../../../server/features/scopingAi/langgraph/nodes/ExecutiveSummaryNode.ts"], "names": [], "mappings": ";AAAA,wDAAwD;AACxD,+CAA+C;AAC/C,wDAAwD;;;AAKxD,MAAa,oBAAoB;IAAjC;QACE,SAAI,GAAG,mBAAmB,CAAC;QAC3B,gBAAW,GAAG,yDAAyD,CAAC;IAgU1E,CAAC;IA9TC,KAAK,CAAC,OAAO,CAAC,OAAwB;QACpC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;QAEzC,MAAM,CAAC,IAAI,CAAC,uCAAuC,EAAE;YACnD,WAAW,EAAE,KAAK,CAAC,WAAW;YAC9B,YAAY,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,iBAAiB,EAAE,OAAO,CAAC;YAClD,WAAW,EAAE,KAAK,CAAC,MAAM,EAAE,IAAI;SAChC,CAAC,CAAC;QAEH,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,6BAA6B;YAC7B,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;YAEhF,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE9C,MAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE;gBACzC,UAAU,EAAE,aAAa,CAAC,UAAU;gBACpC,UAAU,EAAE,aAAa,CAAC,UAAU,CAAC,MAAM;gBAC3C,WAAW,EAAE,aAAa,CAAC,iBAAiB;aAC7C,CAAC,CAAC;YAEH,OAAO;gBACL,iBAAiB,EAAE;oBACjB,OAAO,EAAE,aAAa,CAAC,OAAO;oBAC9B,UAAU,EAAE,aAAa,CAAC,UAAU;oBACpC,iBAAiB,EAAE,aAAa,CAAC,iBAAiB;iBACnD;gBACD,YAAY,EAAE,6BAA6B;gBAC3C,QAAQ,EAAE,EAAE;gBACZ,eAAe,EAAE,CAAC,KAAK,CAAC,eAAe,IAAI,CAAC,CAAC,GAAG,cAAc;gBAC9D,cAAc,EAAE;oBACd,GAAG,CAAC,KAAK,CAAC,cAAc,IAAI,EAAE,CAAC;oBAC/B;wBACE,QAAQ,EAAE,eAAe;wBACzB,QAAQ,EAAE,mBAAmB;wBAC7B,UAAU,EAAE,CAAC,EAAE,kCAAkC;wBACjD,YAAY,EAAE,GAAG;wBACjB,qBAAqB,EAAE,cAAc,GAAG,CAAC;wBACzC,aAAa,EAAE,IAAI;wBACnB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qBACpC;iBACF;gBACD,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACtC,SAAS,EAAE;oBACT,GAAG,KAAK,CAAC,SAAS;oBAClB,iBAAiB,EAAE,aAAa;iBACjC;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC3D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,wBAAwB,CACpC,KAA6B,EAC7B,KAAU,EACV,MAAW;QAGX,kCAAkC;QAClC,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QAEtD,iCAAiC;QACjC,MAAM,cAAc,GAAG,MAAM,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,aAAa,EAAE;YAChE,KAAK,EAAE,aAAa;YACpB,WAAW,EAAE,GAAG;YAChB,UAAU,EAAE,IAAI;SACjB,CAAC,CAAC;QAEH,qBAAqB;QACrB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAE7E,4BAA4B;QAC5B,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,cAAc,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAElG,oBAAoB;QACpB,MAAM,SAAS,GAAG,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;QACrD,MAAM,gBAAgB,GAAG,IAAI,CAAC,yBAAyB,CAAC,cAAc,CAAC,CAAC;QAExE,OAAO;YACL,OAAO,EAAE,cAAc;YACvB,UAAU,EAAE,SAAS;YACrB,iBAAiB,EAAE,gBAAgB;YACnC,UAAU,EAAE,SAAS;YACrB,iBAAiB,EAAE,gBAAgB;SACpC,CAAC;IACJ,CAAC;IAEO,mBAAmB,CAAC,KAA6B;QACvD,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC5B,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;QAC9B,MAAM,gBAAgB,GAAG,KAAK,CAAC,iBAAiB,CAAC;QACjD,MAAM,cAAc,GAAG,KAAK,CAAC,eAAe,CAAC;QAE7C,OAAO;;;aAGE,MAAM,EAAE,IAAI,IAAI,SAAS;cACxB,MAAM,EAAE,QAAQ,IAAI,SAAS;UACjC,MAAM,EAAE,IAAI,IAAI,SAAS;cACrB,MAAM,EAAE,QAAQ,IAAI,SAAS;;;WAGhC,OAAO,EAAE,KAAK,IAAI,SAAS;iBACrB,OAAO,EAAE,WAAW,IAAI,yBAAyB;cACpD,OAAO,EAAE,QAAQ,IAAI,kBAAkB;kBACnC,OAAO,EAAE,YAAY,IAAI,iBAAiB;;EAE1D,gBAAgB,CAAC,CAAC,CAAC;;EAEnB,gBAAgB,CAAC,OAAO;;;EAGxB,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,gBAAgB;;;EAG/D,gBAAgB,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,gBAAgB;CACnE,CAAC,CAAC,CAAC,EAAE;;EAEJ,cAAc,CAAC,CAAC,CAAC;;qBAEE,cAAc,CAAC,eAAe;oBAC/B,cAAc,CAAC,cAAc,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,iBAAiB;mBAC/D,cAAc,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,iBAAiB;CAC/E,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;;;;;;;;;;kBAmBY,KAAK,CAAC,UAAU,EAAE,cAAc,IAAI,8CAA8C;;6GAES,CAAC;IAC5G,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAC5B,cAAsB,EACtB,KAAU,EACV,MAAW;QAEX,IAAI,CAAC;YACH,MAAM,MAAM,GAAG;;;EAGnB,cAAc;;+FAE+E,CAAC;YAE1F,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,EAAE,CAAC,sBAAsB,CAAC,MAAM,EAAE;gBAC7D,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;aAC1B,CAAC,CAAC;YAEH,OAAO,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,IAAI,EAAE,CAAC;QACxE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,IAAI,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAC;YACnE,OAAO;gBACL,uDAAuD;gBACvD,0CAA0C;gBAC1C,0CAA0C;gBAC1C,wCAAwC;gBACxC,sCAAsC;aACvC,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,uBAAuB,CACnC,cAAsB,EACtB,KAA6B,EAC7B,KAAU,EACV,MAAW;QAEX,IAAI,CAAC;YACH,MAAM,MAAM,GAAG;;;EAGnB,cAAc;;;aAGH,KAAK,CAAC,MAAM,EAAE,IAAI;cACjB,KAAK,CAAC,MAAM,EAAE,QAAQ;aACvB,KAAK,CAAC,OAAO,EAAE,KAAK;;uHAEsF,CAAC;YAElH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE;gBACnD,WAAW,EAAE,GAAG;gBAChB,UAAU,EAAE,GAAG;aAChB,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,IAAI,CAAC,qDAAqD,EAAE,KAAK,CAAC,CAAC;YAC1E,OAAO,0EAA0E,KAAK,CAAC,MAAM,EAAE,QAAQ,IAAI,eAAe,qGAAqG,CAAC;QAClO,CAAC;IACH,CAAC;IAEO,yBAAyB,CAAC,OAAe;QAC/C,yEAAyE;QACzE,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC;QAClF,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;QAC1C,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAE/C,IAAI,SAAS,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAE7C,MAAM,mBAAmB,GAAG,KAAK,GAAG,SAAS,CAAC;QAC9C,MAAM,mBAAmB,GAAG,SAAS,GAAG,KAAK,CAAC;QAE9C,2CAA2C;QAC3C,MAAM,KAAK,GAAG,OAAO,GAAG,CAAC,KAAK,GAAG,mBAAmB,CAAC,GAAG,CAAC,IAAI,GAAG,mBAAmB,CAAC,CAAC;QAErF,gDAAgD;QAChD,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACvD,CAAC;IAEO,cAAc,CAAC,IAAY;QACjC,qCAAqC;QACrC,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACxD,IAAI,aAAa,GAAG,CAAC,CAAC;QAEtB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,qBAAqB;YACrB,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;YACnD,aAAa,IAAI,WAAW,CAAC,MAAM,CAAC;YAEpC,wBAAwB;YACxB,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACjD,aAAa,EAAE,CAAC;YAClB,CAAC;YAED,iCAAiC;YACjC,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC7B,aAAa,EAAE,CAAC;YAClB,CAAC;QACH,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;IAED,qCAAqC;IAC7B,sBAAsB,CAAC,OAAe;QAC5C,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,IAAI,KAAK,GAAG,GAAG,CAAC;QAEhB,eAAe;QACf,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;QAC9C,IAAI,SAAS,GAAG,GAAG,EAAE,CAAC;YACpB,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACpC,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;aAAM,IAAI,SAAS,GAAG,GAAG,EAAE,CAAC;YAC3B,MAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;YAC1D,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,yBAAyB;QACzB,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAC/C,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;YAChD,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC7C,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YACzC,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YACjD,MAAM,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;YACxD,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,2BAA2B;QAC3B,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC3E,MAAM,iBAAiB,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;QAEzE,IAAI,iBAAiB,GAAG,EAAE,EAAE,CAAC;YAC3B,MAAM,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;YAChE,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC;IAC/C,CAAC;IAED,iDAAiD;IACzC,KAAK,CAAC,0BAA0B,CACtC,KAA6B,EAC7B,KAAU,EACV,cAAwB;QAExB,MAAM,cAAc,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC;;;EAG3D,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC;;6IAEgH,CAAC;QAE1I,OAAO,MAAM,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,cAAc,EAAE;YACjD,KAAK,EAAE,aAAa;YACpB,WAAW,EAAE,GAAG;YAChB,UAAU,EAAE,IAAI;SACjB,CAAC,CAAC;IACL,CAAC;CACF;AAlUD,oDAkUC"}