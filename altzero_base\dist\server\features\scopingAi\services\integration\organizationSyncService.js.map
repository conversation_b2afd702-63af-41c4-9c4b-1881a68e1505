{"version": 3, "file": "organizationSyncService.js", "sourceRoot": "", "sources": ["../../../../../../server/features/scopingAi/services/integration/organizationSyncService.ts"], "names": [], "mappings": ";;;AAAA,oEAAiE;AAkBjE,MAAa,uBAAuB;IAClC;;OAEG;IACH,KAAK,CAAC,mBAAmB,CAAC,MAAc;QACtC,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,qCAAqC,MAAM,EAAE,CAAC,CAAC;YAE3D,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,MAAM,mBAAQ;iBAC/C,IAAI,CAAC,sBAAsB,CAAC;iBAC5B,MAAM,CACL;;;;;;SAMD,CACA;iBACA,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;iBACrB,KAAK,CAAC,CAAC,CAAC;iBACR,MAAM,EAAE,CAAC;YAEZ,IAAI,KAAK,IAAI,CAAC,UAAU,EAAE,CAAC;gBACzB,OAAO,CAAC,GAAG,CAAC,UAAU,MAAM,sCAAsC,CAAC,CAAC;gBACpE,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO,CAAC,GAAG,CACT,UAAU,MAAM,6BAA6B,UAAU,CAAC,eAAe,EAAE,CAC1E,CAAC;YACF,OAAO,UAAU,CAAC,eAAe,CAAC;QACpC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CAC3B,MAAc;QAEd,IAAI,CAAC;YACH,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,MAAM,mBAAQ;iBAC/C,IAAI,CAAC,sBAAsB,CAAC;iBAC5B,MAAM,CACL;;;;;;;;SAQD,CACA;iBACA,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;iBACrB,KAAK,CAAC,CAAC,CAAC;iBACR,MAAM,EAAE,CAAC;YAEZ,IAAI,KAAK,IAAI,CAAC,UAAU,EAAE,CAAC;gBACzB,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO;gBACL,MAAM;gBACN,cAAc,EAAE,UAAU,CAAC,eAAe;gBAC1C,gBAAgB,EAAG,UAAkB,CAAC,aAAa,EAAE,IAAI,IAAI,SAAS;gBACtE,IAAI,EAAE,UAAU,CAAC,IAAI,IAAI,QAAQ;gBACjC,QAAQ,EAAE,UAAU,CAAC,UAAU;aAChC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YAC9D,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACxB,cAAsB;QAEtB,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,sCAAsC,cAAc,EAAE,CAAC,CAAC;YAEpE,8BAA8B;YAC9B,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,MAAM,mBAAQ;iBAClD,IAAI,CAAC,eAAe,CAAC;iBACrB,MAAM,CAAC,UAAU,CAAC;iBAClB,EAAE,CAAC,IAAI,EAAE,cAAc,CAAC;iBACxB,MAAM,EAAE,CAAC;YAEZ,IAAI,QAAQ,IAAI,CAAC,GAAG,EAAE,CAAC;gBACrB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;YAC5C,CAAC;YAED,mBAAmB;YACnB,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,MAAM,mBAAQ;iBAC9D,IAAI,CAAC,sBAAsB,CAAC;iBAC5B,MAAM,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;iBAC3C,EAAE,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC;YAEzC,IAAI,WAAW,EAAE,CAAC;gBAChB,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,WAAW,CAAC,CAAC;YACxD,CAAC;YAED,wBAAwB;YACxB,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,MAAM,mBAAQ;iBAC/D,IAAI,CAAC,cAAc,CAAC;iBACpB,MAAM,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;iBAC3C,EAAE,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC;YAEzC,IAAI,QAAQ,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,QAAQ,CAAC,CAAC;YAC1D,CAAC;YAED,sDAAsD;YACtD,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,YAAY,EAAE,GAAG,MAAM,mBAAQ;iBAC7D,IAAI,CAAC,sBAAsB,CAAC;iBAC5B,MAAM,CAAC,SAAS,CAAC;iBACjB,EAAE,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC;YAEzC,IAAI,oBAAoB,GAAG,CAAC,CAAC;YAC7B,IAAI,CAAC,YAAY,IAAI,UAAU,EAAE,CAAC;gBAChC,MAAM,aAAa,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;gBAEvD,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,MAAM,mBAAQ;qBAC9D,IAAI,CAAC,mBAAmB,CAAC;qBACzB,MAAM,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;qBAC3C,EAAE,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;gBAEhC,IAAI,CAAC,WAAW,EAAE,CAAC;oBACjB,oBAAoB,GAAG,WAAW,IAAI,CAAC,CAAC;gBAC1C,CAAC;YACH,CAAC;YAED,MAAM,KAAK,GAAqB;gBAC9B,EAAE,EAAE,GAAG,CAAC,EAAE;gBACV,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,WAAW,EAAE,WAAW,IAAI,CAAC;gBAC7B,eAAe,EAAE,eAAe,IAAI,CAAC;gBACrC,oBAAoB;aACrB,CAAC;YAEF,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC5C,OAAO,KAAK,CAAC;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAC1D,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CACvB,cAAsB,EACtB,MAAc;QAEd,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CACT,kDAAkD,cAAc,EAAE,CACnE,CAAC;YAEF,wCAAwC;YACxC,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,eAAe,EAAE,GAAG,MAAM,mBAAQ;iBAChE,IAAI,CAAC,sBAAsB,CAAC;iBAC5B,MAAM,CAAC,IAAI,CAAC;iBACZ,EAAE,CAAC,iBAAiB,EAAE,cAAc,CAAC;iBACrC,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;iBACrB,MAAM,EAAE,CAAC;YAEZ,IAAI,eAAe,IAAI,CAAC,UAAU,EAAE,CAAC;gBACnC,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;YAC/D,CAAC;YAED,4CAA4C;YAC5C,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,MAAM,mBAAQ;iBAC/D,IAAI,CAAC,cAAc,CAAC;iBACpB,MAAM,CAAC,IAAI,CAAC;iBACZ,EAAE,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC;YAEzC,IAAI,aAAa,EAAE,CAAC;gBAClB,MAAM,aAAa,CAAC;YACtB,CAAC;YAED,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC7C,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;gBAC7C,OAAO,CAAC,CAAC;YACX,CAAC;YAED,0DAA0D;YAC1D,OAAO,CAAC,GAAG,CACT,SAAS,WAAW,CAAC,MAAM,yDAAyD,CACrF,CAAC;YAEF,OAAO,WAAW,CAAC,MAAM,CAAC;QAC5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACxB,cAAsB,EACtB,MAAc;QAMd,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,qCAAqC,cAAc,EAAE,CAAC,CAAC;YAEnE,yBAAyB;YACzB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAChD,cAAc,EACd,MAAM,CACP,CAAC;YAEF,oBAAoB;YACpB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;YAEnE,MAAM,MAAM,GAAG;gBACb,iBAAiB,EAAE,WAAW;gBAC9B,gBAAgB,EAAE,UAAU,EAAE,eAAe,IAAI,CAAC;gBAClD,qBAAqB,EAAE,UAAU,EAAE,oBAAoB,IAAI,CAAC;aAC7D,CAAC;YAEF,OAAO,CAAC,GAAG,CAAC,gCAAgC,EAAE,MAAM,CAAC,CAAC;YACtD,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB;QAC5B,IAAI,CAAC;YACH,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,GAAG,MAAM,mBAAQ;iBAClD,IAAI,CAAC,eAAe,CAAC;iBACrB,MAAM,CAAC,UAAU,CAAC;iBAClB,KAAK,CAAC,MAAM,CAAC,CAAC;YAEjB,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,KAAK,CAAC;YACd,CAAC;YAED,MAAM,KAAK,GAAuB,EAAE,CAAC;YAErC,KAAK,MAAM,GAAG,IAAI,aAAa,IAAI,EAAE,EAAE,CAAC;gBACtC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBACzD,IAAI,QAAQ,EAAE,CAAC;oBACb,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACvB,CAAC;YACH,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YAC/D,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CAAC,cAAsB;QAC/C,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CACT,+EAA+E,cAAc,EAAE,CAChG,CAAC;YACF,OAAO,CAAC,CAAC;QACX,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF;AA/RD,0DA+RC;AAEY,QAAA,uBAAuB,GAAG,IAAI,uBAAuB,EAAE,CAAC"}