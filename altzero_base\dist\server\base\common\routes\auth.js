"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateApiKey = void 0;
const environment_1 = require("../apps/config/environment");
/**
 * Middleware to validate API key in requests
 */
const validateApiKey = (req, res, next) => {
    const apiKey = req.query.apiKey || req.headers["x-api-key"];
    if (!apiKey) {
        return res.status(401).json({ error: "API key is required" });
    }
    // Check against API key from environment
    if (apiKey !== environment_1.environment.apiKey) {
        return res.status(403).json({ error: "Invalid API key" });
    }
    next();
};
exports.validateApiKey = validateApiKey;
//# sourceMappingURL=auth.js.map