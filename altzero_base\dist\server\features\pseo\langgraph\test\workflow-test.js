"use strict";
// =====================================================
// LANGGRAPH WORKFLOW TEST SCRIPT
// =====================================================
Object.defineProperty(exports, "__esModule", { value: true });
exports.testWorkflow = testWorkflow;
const PSEOWorkflow_1 = require("../workflows/PSEOWorkflow");
async function testWorkflow() {
    console.log('🧪 Testing LangGraph PSEOWorkflow...');
    try {
        // Create workflow with test configuration
        const config = {
            openai_api_key: process.env.OPENAI_API_KEY || 'test-key',
            max_concurrent_requests: 5,
            request_timeout: 30000,
            retry_attempts: 3,
            cache_ttl: 3600
        };
        console.log('📋 Configuration validation...');
        const validation = PSEOWorkflow_1.PSEOWorkflow.validateConfig(config);
        console.log('Validation result:', validation);
        if (!validation.valid) {
            console.log('⚠️ Configuration issues found:', validation.issues);
        }
        // Create workflow instance
        console.log('🏗️ Creating workflow instance...');
        const workflow = new PSEOWorkflow_1.PSEOWorkflow(config);
        // Test input
        const testInput = {
            user_id: 'test-user-123',
            website_id: 'test-website-456',
            domain: 'example.com',
            seed_keywords: ['digital marketing', 'SEO'],
            research_method: 'topic',
            topic_input: 'digital marketing strategies',
            max_keywords: 20,
            data_sources: ['ai_generated']
        };
        console.log('🚀 Starting workflow execution...');
        console.log('Input:', testInput);
        const startTime = Date.now();
        const result = await workflow.execute(testInput);
        const executionTime = Date.now() - startTime;
        console.log('✅ Workflow completed!');
        console.log('Execution time:', executionTime, 'ms');
        console.log('Status:', result.status);
        console.log('Keywords found:', result.keywords.length);
        console.log('Clusters created:', result.keyword_clusters.length);
        console.log('Processing time:', result.processing_time, 'ms');
        console.log('API calls made:', result.api_calls_made.length);
        console.log('Total cost:', result.total_cost);
        if (result.errors && result.errors.length > 0) {
            console.log('❌ Errors encountered:', result.errors);
        }
        // Sample keywords
        if (result.keywords.length > 0) {
            console.log('\n📊 Sample keywords:');
            result.keywords.slice(0, 5).forEach((keyword, index) => {
                console.log(`${index + 1}. ${keyword.keyword} (Volume: ${keyword.search_volume}, Difficulty: ${keyword.keyword_difficulty})`);
            });
        }
        // Sample clusters
        if (result.keyword_clusters.length > 0) {
            console.log('\n🔗 Sample clusters:');
            result.keyword_clusters.slice(0, 3).forEach((cluster, index) => {
                console.log(`${index + 1}. ${cluster.cluster_name} (${cluster.related_keywords.length} keywords)`);
            });
        }
        return result;
    }
    catch (error) {
        console.error('❌ Workflow test failed:', error);
        throw error;
    }
}
// Run test if this file is executed directly
if (require.main === module) {
    testWorkflow()
        .then(() => {
        console.log('🎉 Test completed successfully!');
        process.exit(0);
    })
        .catch((error) => {
        console.error('💥 Test failed:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=workflow-test.js.map