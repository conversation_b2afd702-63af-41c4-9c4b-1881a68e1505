"use strict";
// =====================================================
// CONTENT MANAGEMENT SERVICE
// Phase 5: Content & Keyword Management
// =====================================================
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContentManagementService = void 0;
const supabase_1 = require("../../../../base/common/apps/supabase");
class ContentManagementService {
    // =====================================================
    // CONTENT CRUD OPERATIONS
    // =====================================================
    async createContent(contentData) {
        const { data, error } = await supabase_1.supabase
            .from('pseo_content_items')
            .insert({
            ...contentData,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        })
            .select()
            .single();
        if (error) {
            throw new Error(`Failed to create content: ${error.message}`);
        }
        return data;
    }
    async updateContent(id, updates) {
        const { data, error } = await supabase_1.supabase
            .from('pseo_content_items')
            .update({
            ...updates,
            updated_at: new Date().toISOString()
        })
            .eq('id', id)
            .select()
            .single();
        if (error) {
            throw new Error(`Failed to update content: ${error.message}`);
        }
        return data;
    }
    async getContentById(id) {
        const { data, error } = await supabase_1.supabase
            .from('pseo_content_items')
            .select('*')
            .eq('id', id)
            .single();
        if (error) {
            if (error.code === 'PGRST116')
                return null; // Not found
            throw new Error(`Failed to get content: ${error.message}`);
        }
        return data;
    }
    async getContentByWebsite(websiteId, filters) {
        let query = supabase_1.supabase
            .from('pseo_content_items')
            .select('*', { count: 'exact' })
            .eq('website_id', websiteId);
        // Apply filters
        if (filters?.status) {
            query = query.in('status', filters.status);
        }
        if (filters?.content_type) {
            query = query.in('content_type', filters.content_type);
        }
        if (filters?.priority) {
            query = query.in('priority', filters.priority);
        }
        if (filters?.ai_generated !== undefined) {
            query = query.eq('ai_generated', filters.ai_generated);
        }
        if (filters?.generated_by_agent) {
            query = query.eq('generated_by_agent', filters.generated_by_agent);
        }
        if (filters?.search) {
            query = query.or(`title.ilike.%${filters.search}%,target_keyword.ilike.%${filters.search}%`);
        }
        // Pagination
        if (filters?.limit) {
            query = query.limit(filters.limit);
        }
        if (filters?.offset) {
            query = query.range(filters.offset, (filters.offset + (filters.limit || 20)) - 1);
        }
        // Order by priority and created date
        query = query.order('priority', { ascending: false }).order('created_at', { ascending: false });
        const { data, error, count } = await query;
        if (error) {
            throw new Error(`Failed to get content: ${error.message}`);
        }
        return { items: data || [], total: count || 0 };
    }
    async deleteContent(id) {
        const { error } = await supabase_1.supabase
            .from('pseo_content_items')
            .delete()
            .eq('id', id);
        if (error) {
            throw new Error(`Failed to delete content: ${error.message}`);
        }
    }
    // =====================================================
    // CONTENT WORKFLOW MANAGEMENT
    // =====================================================
    async createWorkflowStep(workflowData) {
        const { data, error } = await supabase_1.supabase
            .from('pseo_content_workflow')
            .insert({
            ...workflowData,
            created_at: new Date().toISOString()
        })
            .select()
            .single();
        if (error) {
            throw new Error(`Failed to create workflow step: ${error.message}`);
        }
        return data;
    }
    async updateWorkflowStep(id, updates) {
        const { data, error } = await supabase_1.supabase
            .from('pseo_content_workflow')
            .update(updates)
            .eq('id', id)
            .select()
            .single();
        if (error) {
            throw new Error(`Failed to update workflow step: ${error.message}`);
        }
        return data;
    }
    async getContentWorkflow(contentId) {
        const { data, error } = await supabase_1.supabase
            .from('pseo_content_workflow')
            .select('*')
            .eq('content_id', contentId)
            .order('created_at', { ascending: true });
        if (error) {
            throw new Error(`Failed to get content workflow: ${error.message}`);
        }
        return data || [];
    }
    async completeWorkflowStep(id, notes) {
        return this.updateWorkflowStep(id, {
            status: 'completed',
            completed_at: new Date().toISOString(),
            notes
        });
    }
    // =====================================================
    // CONTENT CALENDAR MANAGEMENT
    // =====================================================
    async createCalendarItem(calendarData) {
        const { data, error } = await supabase_1.supabase
            .from('pseo_content_calendar')
            .insert({
            ...calendarData,
            created_at: new Date().toISOString()
        })
            .select()
            .single();
        if (error) {
            throw new Error(`Failed to create calendar item: ${error.message}`);
        }
        return data;
    }
    async getContentCalendar(websiteId, startDate, endDate) {
        let query = supabase_1.supabase
            .from('pseo_content_calendar')
            .select('*')
            .eq('website_id', websiteId);
        if (startDate) {
            query = query.gte('scheduled_date', startDate);
        }
        if (endDate) {
            query = query.lte('scheduled_date', endDate);
        }
        query = query.order('scheduled_date', { ascending: true });
        const { data, error } = await query;
        if (error) {
            throw new Error(`Failed to get content calendar: ${error.message}`);
        }
        return data || [];
    }
    async updateCalendarItem(id, updates) {
        const { data, error } = await supabase_1.supabase
            .from('pseo_content_calendar')
            .update(updates)
            .eq('id', id)
            .select()
            .single();
        if (error) {
            throw new Error(`Failed to update calendar item: ${error.message}`);
        }
        return data;
    }
    // =====================================================
    // CONTENT CLUSTERS MANAGEMENT
    // =====================================================
    async createContentCluster(clusterData) {
        const { data, error } = await supabase_1.supabase
            .from('pseo_content_clusters')
            .insert({
            ...clusterData,
            created_at: new Date().toISOString()
        })
            .select()
            .single();
        if (error) {
            throw new Error(`Failed to create content cluster: ${error.message}`);
        }
        return data;
    }
    async getContentClusters(websiteId) {
        const { data, error } = await supabase_1.supabase
            .from('pseo_content_clusters')
            .select('*')
            .eq('website_id', websiteId)
            .order('created_at', { ascending: false });
        if (error) {
            throw new Error(`Failed to get content clusters: ${error.message}`);
        }
        return data || [];
    }
    async addContentToCluster(clusterId, contentId) {
        // Get current cluster
        const { data: cluster, error: getError } = await supabase_1.supabase
            .from('pseo_content_clusters')
            .select('content_items')
            .eq('id', clusterId)
            .single();
        if (getError) {
            throw new Error(`Failed to get cluster: ${getError.message}`);
        }
        // Add content ID to array
        const contentItems = cluster.content_items || [];
        if (!contentItems.includes(contentId)) {
            contentItems.push(contentId);
            const { error: updateError } = await supabase_1.supabase
                .from('pseo_content_clusters')
                .update({
                content_items: contentItems,
                completed_pages: contentItems.length
            })
                .eq('id', clusterId);
            if (updateError) {
                throw new Error(`Failed to add content to cluster: ${updateError.message}`);
            }
        }
    }
    // =====================================================
    // CONTENT TEMPLATES
    // =====================================================
    async createContentTemplate(templateData) {
        const { data, error } = await supabase_1.supabase
            .from('pseo_content_templates')
            .insert({
            ...templateData,
            created_at: new Date().toISOString()
        })
            .select()
            .single();
        if (error) {
            throw new Error(`Failed to create content template: ${error.message}`);
        }
        return data;
    }
    async getContentTemplates() {
        const { data, error } = await supabase_1.supabase
            .from('pseo_content_templates')
            .select('*')
            .order('name');
        if (error) {
            throw new Error(`Failed to get content templates: ${error.message}`);
        }
        return data || [];
    }
    // =====================================================
    // CONTENT PERFORMANCE TRACKING
    // =====================================================
    async recordContentPerformance(metrics) {
        const { error } = await supabase_1.supabase
            .from('pseo_content_performance')
            .upsert({
            ...metrics,
            updated_at: new Date().toISOString()
        }, {
            onConflict: 'content_id,date'
        });
        if (error) {
            throw new Error(`Failed to record content performance: ${error.message}`);
        }
    }
    async getContentPerformance(contentId, startDate, endDate) {
        let query = supabase_1.supabase
            .from('pseo_content_performance')
            .select('*')
            .eq('content_id', contentId);
        if (startDate) {
            query = query.gte('date', startDate);
        }
        if (endDate) {
            query = query.lte('date', endDate);
        }
        query = query.order('date', { ascending: true });
        const { data, error } = await query;
        if (error) {
            throw new Error(`Failed to get content performance: ${error.message}`);
        }
        return data || [];
    }
    // =====================================================
    // CONTENT PUBLISHING
    // =====================================================
    async publishContent(contentId, publishedBy) {
        const content = await this.updateContent(contentId, {
            status: 'published',
            published_at: new Date().toISOString(),
        });
        // Create workflow step for publishing
        await this.createWorkflowStep({
            content_id: contentId,
            workflow_step: 'publishing',
            status: 'completed',
            completed_at: new Date().toISOString(),
            notes: 'Content published successfully'
        });
        return content;
    }
    async scheduleContent(contentId, publishDate) {
        return this.updateContent(contentId, {
            scheduled_publish_date: publishDate,
            status: 'approved'
        });
    }
    async getScheduledContent() {
        const now = new Date().toISOString();
        const { data, error } = await supabase_1.supabase
            .from('pseo_content_items')
            .select('*')
            .eq('status', 'approved')
            .not('scheduled_publish_date', 'is', null)
            .lte('scheduled_publish_date', now);
        if (error) {
            throw new Error(`Failed to get scheduled content: ${error.message}`);
        }
        return data || [];
    }
    // =====================================================
    // ANALYTICS & REPORTING
    // =====================================================
    async getContentAnalytics(websiteId) {
        // Get content stats
        const { data: contentStats, error: statsError } = await supabase_1.supabase
            .from('pseo_content_items')
            .select('status, content_type, word_count, seo_score')
            .eq('website_id', websiteId);
        if (statsError) {
            throw new Error(`Failed to get content stats: ${statsError.message}`);
        }
        // Get performance data
        const { data: performanceData, error: perfError } = await supabase_1.supabase
            .from('pseo_content_performance')
            .select('impressions, clicks, ctr, average_position')
            .in('content_id', contentStats.map((c) => c.id));
        if (perfError) {
            throw new Error(`Failed to get performance data: ${perfError.message}`);
        }
        // Calculate analytics
        const byStatus = contentStats.reduce((acc, item) => {
            acc[item.status] = (acc[item.status] || 0) + 1;
            return acc;
        }, {});
        const byType = contentStats.reduce((acc, item) => {
            acc[item.content_type] = (acc[item.content_type] || 0) + 1;
            return acc;
        }, {});
        const avgWordCount = contentStats.length > 0
            ? contentStats.reduce((sum, item) => sum + (item.word_count || 0), 0) / contentStats.length
            : 0;
        const avgSeoScore = contentStats.length > 0
            ? contentStats.reduce((sum, item) => sum + (item.seo_score || 0), 0) / contentStats.length
            : 0;
        const performanceSummary = performanceData.reduce((acc, perf) => {
            acc.total_views += perf.impressions || 0;
            acc.total_clicks += perf.clicks || 0;
            acc.avg_ctr += perf.ctr || 0;
            acc.avg_position += perf.average_position || 0;
            return acc;
        }, { total_views: 0, total_clicks: 0, avg_ctr: 0, avg_position: 0 });
        if (performanceData.length > 0) {
            performanceSummary.avg_ctr /= performanceData.length;
            performanceSummary.avg_position /= performanceData.length;
        }
        return {
            total_content: contentStats.length,
            by_status: byStatus,
            by_type: byType,
            avg_word_count: avgWordCount,
            avg_seo_score: avgSeoScore,
            performance_summary: performanceSummary
        };
    }
    // =====================================================
    // AI-GENERATED CONTENT METHODS
    // =====================================================
    async getAllAIGeneratedContent(websiteId, filters) {
        return this.getContentByWebsite(websiteId, {
            ...filters,
            ai_generated: true
        });
    }
    async getContentByAgent(websiteId, agentName, filters) {
        return this.getContentByWebsite(websiteId, {
            ...filters,
            ai_generated: true,
            generated_by_agent: agentName
        });
    }
    async getAIContentStats(websiteId) {
        // Get all content for the website
        const { items: allContent } = await this.getContentByWebsite(websiteId, { limit: 10000 });
        const aiContent = allContent.filter((c) => c.ai_generated);
        const humanContent = allContent.filter((c) => !c.ai_generated);
        const byAgent = aiContent.reduce((acc, item) => {
            const agent = item.generated_by_agent || 'unknown';
            acc[agent] = (acc[agent] || 0) + 1;
            return acc;
        }, {});
        const byStatus = aiContent.reduce((acc, item) => {
            acc[item.status] = (acc[item.status] || 0) + 1;
            return acc;
        }, {});
        const byContentType = aiContent.reduce((acc, item) => {
            acc[item.content_type] = (acc[item.content_type] || 0) + 1;
            return acc;
        }, {});
        // Calculate performance averages
        const calculateAverages = (content) => {
            if (content.length === 0) {
                return { avg_views: 0, avg_clicks: 0, avg_ranking: 0 };
            }
            const totals = content.reduce((acc, item) => {
                acc.views += item.performance_data?.views || 0;
                acc.clicks += item.performance_data?.clicks || 0;
                acc.ranking += item.performance_data?.ranking_position || 0;
                return acc;
            }, { views: 0, clicks: 0, ranking: 0 });
            return {
                avg_views: totals.views / content.length,
                avg_clicks: totals.clicks / content.length,
                avg_ranking: totals.ranking / content.length
            };
        };
        return {
            total_ai_content: aiContent.length,
            by_agent: byAgent,
            by_status: byStatus,
            by_content_type: byContentType,
            performance_comparison: {
                ai_generated: calculateAverages(aiContent),
                human_generated: calculateAverages(humanContent)
            }
        };
    }
}
exports.ContentManagementService = ContentManagementService;
//# sourceMappingURL=ContentManagementService.js.map