"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../base/common/route/auth");
const supabase_1 = require("../base/common/apps/supabase");
const router = express_1.default.Router();
// Get example data
router.get("/", auth_1.validate<PERSON><PERSON><PERSON><PERSON>, async (req, res) => {
    try {
        // Example of using supabase client
        const { data, error } = await supabase_1.supabase
            .from("examples")
            .select("*")
            .limit(10);
        if (error)
            throw error;
        res.status(200).json({
            success: true,
            data,
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : "Unknown error",
        });
    }
});
// Export router
exports.default = router;
//# sourceMappingURL=example.js.map