// =====================================================
// MAIN AI CHAT WORKFLOW - LANGGRAPH ORCHESTRATION
// =====================================================

import { BaseWorkflow } from '../core/BaseWorkflow';
import { AIChatWorkflowState, WorkflowConfig } from '../types/WorkflowState';
import { NodeMetadata } from '../types/NodeTypes';
import { IntentAnalysisNode } from '../nodes/IntentAnalysisNode';
import { NavigationNode } from '../nodes/NavigationNode';
import { AIResponseNode } from '../nodes/AIResponseNode';

export class AIChatWorkflow extends BaseWorkflow {
  private intentAnalysisNode: IntentAnalysisNode;
  private navigationNode: NavigationNode;
  private aiResponseNode: AIResponseNode;

  constructor(config: WorkflowConfig) {
    super(config);

    // Initialize nodes
    this.intentAnalysisNode = new IntentAnalysisNode();
    this.navigationNode = new NavigationNode();
    this.aiResponseNode = new AIResponseNode();

    // Initialize the workflow after nodes are created
    this.initializeWorkflow();
  }

  getWorkflowName(): string {
    return 'AIChatWorkflow';
  }

  // Define all nodes in the workflow
  defineNodes(): void {
    // Register intent analysis node (always first)
    this.registerNode(this.intentAnalysisNode, {
      node_name: 'intent_analysis',
      execution_order: 1,
      dependencies: [],
      optional: false,
      timeout_seconds: 30,
      retry_attempts: 2,
      resource_requirements: {
        memory_mb: 128,
        cpu_cores: 1
      }
    });

    // Register navigation node (conditional execution)
    this.registerNode(this.navigationNode, {
      node_name: 'navigation',
      execution_order: 2,
      dependencies: ['intent_analysis'],
      optional: true, // Only executes for navigation intents
      timeout_seconds: 15,
      retry_attempts: 2,
      resource_requirements: {
        memory_mb: 64,
        cpu_cores: 1
      }
    });

    // Register database query node (placeholder for future implementation)
    this.registerNode({
      name: 'database_query',
      description: 'Handles database queries and searches',
      execute: async (context) => {
        const { state, logger } = context;
        
        if (state.current_intent !== 'database_query') {
          logger.debug('Skipping database query - intent is not database_query');
          return {};
        }

        logger.info('Database query node executed (placeholder)', {
          workflow_id: state.workflow_id,
          entities: state.detected_entities
        });

        // Placeholder implementation
        return {
          database_queries: [
            ...state.database_queries,
            {
              query: state.detected_entities.search_terms || 'general search',
              results: [],
              timestamp: new Date().toISOString(),
              success: true,
              error: undefined,
              execution_time_ms: 100
            }
          ]
        };
      }
    }, {
      node_name: 'database_query',
      execution_order: 3,
      dependencies: ['intent_analysis'],
      optional: true,
      timeout_seconds: 30,
      retry_attempts: 2,
      resource_requirements: {
        memory_mb: 128,
        cpu_cores: 1
      }
    });

    // Register knowledge base search node (placeholder for future implementation)
    this.registerNode({
      name: 'knowledge_search',
      description: 'Handles knowledge base searches',
      execute: async (context) => {
        const { state, logger } = context;
        
        if (state.current_intent !== 'knowledge_search') {
          logger.debug('Skipping knowledge search - intent is not knowledge_search');
          return {};
        }

        logger.info('Knowledge search node executed (placeholder)', {
          workflow_id: state.workflow_id,
          entities: state.detected_entities
        });

        // Placeholder implementation
        return {
          knowledge_searches: [
            ...state.knowledge_searches,
            {
              query: state.detected_entities.search_terms || 'general search',
              results: [],
              timestamp: new Date().toISOString(),
              success: true,
              error: undefined,
              total_found: 0
            }
          ]
        };
      }
    }, {
      node_name: 'knowledge_search',
      execution_order: 4,
      dependencies: ['intent_analysis'],
      optional: true,
      timeout_seconds: 30,
      retry_attempts: 2,
      resource_requirements: {
        memory_mb: 128,
        cpu_cores: 1
      }
    });

    // Register AI response generation node (always last)
    this.registerNode(this.aiResponseNode, {
      node_name: 'ai_response',
      execution_order: 5,
      dependencies: ['intent_analysis'],
      optional: false,
      timeout_seconds: 45,
      retry_attempts: 3,
      resource_requirements: {
        memory_mb: 256,
        cpu_cores: 1
      }
    });

    // Register completion node
    this.registerNode({
      name: 'completion',
      description: 'Finalizes workflow execution and prepares final response',
      execute: async (context) => {
        const { state, logger } = context;
        
        logger.info('Finalizing AI chat workflow execution', {
          workflow_id: state.workflow_id,
          intent: state.current_intent,
          actions_performed: {
            navigation: state.navigation_actions.length,
            database_queries: state.database_queries.length,
            knowledge_searches: state.knowledge_searches.length
          }
        });

        return {
          status: 'completed',
          current_step: 'workflow_completed',
          progress: 100,
          completed_at: new Date().toISOString(),
          last_updated: new Date().toISOString()
        };
      }
    }, {
      node_name: 'completion',
      execution_order: 6,
      dependencies: ['ai_response'],
      optional: false,
      timeout_seconds: 10,
      retry_attempts: 1,
      resource_requirements: {
        memory_mb: 64,
        cpu_cores: 1
      }
    });
  }

  // Define edges (workflow flow)
  defineEdges(): void {
    // Set entry point
    this.setEntryPoint('intent_analysis');

    // Define the workflow flow
    this.addConditionalEdge(
      'intent_analysis',
      (state: AIChatWorkflowState) => {
        if (state.status === 'failed') {
          return 'ai_response'; // Skip to response generation if analysis failed
        }
        
        // Continue to specific action nodes based on intent
        switch (state.current_intent) {
          case 'navigation':
            return 'navigation';
          case 'database_query':
            return 'database_query';
          case 'knowledge_search':
            return 'knowledge_search';
          default:
            return 'ai_response';
        }
      },
      {
        'navigation': 'navigation',
        'database_query': 'database_query',
        'knowledge_search': 'knowledge_search',
        'ai_response': 'ai_response'
      }
    );

    // All action nodes flow to AI response generation
    this.addEdge('navigation', 'ai_response');
    this.addEdge('database_query', 'ai_response');
    this.addEdge('knowledge_search', 'ai_response');

    // AI response flows to completion
    this.addEdge('ai_response', 'completion');

    // Set exit point
    this.setExitPoint('completion');
  }

  // Get workflow configuration with defaults
  public static getDefaultConfig(): WorkflowConfig {
    return {
      openai_api_key: process.env.OPENAI_API_KEY,
      max_concurrent_requests: 3,
      request_timeout: 30000,
      retry_attempts: 2,
      cache_ttl: 1800, // 30 minutes
      navigation_enabled: true,
      database_access_enabled: true,
      knowledge_base_enabled: true
    };
  }

  // Validate workflow configuration
  public static validateConfig(config: WorkflowConfig): { valid: boolean; issues: string[] } {
    const issues: string[] = [];

    if (!config.openai_api_key) {
      issues.push('OpenAI API key is required for AI chat functionality');
    }

    if (config.max_concurrent_requests && (config.max_concurrent_requests < 1 || config.max_concurrent_requests > 10)) {
      issues.push('max_concurrent_requests should be between 1 and 10');
    }

    if (config.request_timeout && (config.request_timeout < 5000 || config.request_timeout > 120000)) {
      issues.push('request_timeout should be between 5000ms and 120000ms');
    }

    return {
      valid: issues.length === 0,
      issues
    };
  }

  // Create workflow instance with validation
  public static async create(config?: Partial<WorkflowConfig>): Promise<AIChatWorkflow> {
    const fullConfig = { ...AIChatWorkflow.getDefaultConfig(), ...config };
    const validation = AIChatWorkflow.validateConfig(fullConfig);

    if (!validation.valid) {
      throw new Error(`Invalid workflow configuration: ${validation.issues.join(', ')}`);
    }

    return new AIChatWorkflow(fullConfig);
  }

  // Process chat message (main entry point)
  public async processMessage(input: {
    user_id: string;
    session_id: string;
    conversation_id?: string;
    message: string;
    context?: any;
  }): Promise<AIChatWorkflowState> {
    // Prepare workflow input
    const workflowInput = {
      user_id: input.user_id,
      session_id: input.session_id,
      conversation_id: input.conversation_id,
      messages: [
        {
          id: `msg_${Date.now()}`,
          role: 'user' as const,
          content: input.message,
          timestamp: new Date().toISOString()
        }
      ],
      context: input.context || {}
    };

    // Execute the workflow
    return this.execute(workflowInput);
  }

  // Get workflow capabilities
  public static getCapabilities(): string[] {
    return [
      'Natural language intent analysis',
      'Automatic page navigation',
      'Database queries and searches',
      'Knowledge base searches',
      'Contextual AI responses',
      'Multi-turn conversations',
      'Error handling and recovery'
    ];
  }

  // Get supported intents
  public static getSupportedIntents(): string[] {
    return [
      'navigation',
      'database_query',
      'knowledge_search',
      'general_chat'
    ];
  }
}
