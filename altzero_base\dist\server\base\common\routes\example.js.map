{"version": 3, "file": "example.js", "sourceRoot": "", "sources": ["../../../../../server/base/common/routes/example.ts"], "names": [], "mappings": ";;;;;AAAA,sDAA8B;AAC9B,oDAA2D;AAC3D,2DAAwD;AAExD,MAAM,MAAM,GAAG,iBAAO,CAAC,MAAM,EAAE,CAAC;AAEhC,mBAAmB;AACnB,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,qBAAc,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACjD,IAAI,CAAC;QACH,mCAAmC;QACnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,mBAAQ;aACnC,IAAI,CAAC,UAAU,CAAC;aAChB,MAAM,CAAC,GAAG,CAAC;aACX,KAAK,CAAC,EAAE,CAAC,CAAC;QAEb,IAAI,KAAK;YAAE,MAAM,KAAK,CAAC;QAEvB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,IAAI;YACb,IAAI;SACL,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;SAChE,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,gBAAgB;AAChB,kBAAe,MAAM,CAAC"}