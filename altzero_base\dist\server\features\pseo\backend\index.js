"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const pseo_1 = __importDefault(require("../routes/pseo"));
// Create the backend plugin for pSEO
const pseoBackendPlugin = {
    router: pseo_1.default,
    config: {
        name: 'pSEO API',
        version: '1.0.0',
        apiPrefix: '/api/pseo'
    },
    initialize: async () => {
        console.log('📊 pSEO backend plugin initialized');
    },
    cleanup: async () => {
        console.log('📊 pSEO backend plugin cleaned up');
    },
    healthCheck: async () => {
        try {
            return true;
        }
        catch (error) {
            console.error('pSEO backend health check failed:', error);
            return false;
        }
    }
};
exports.default = pseoBackendPlugin;
//# sourceMappingURL=index.js.map