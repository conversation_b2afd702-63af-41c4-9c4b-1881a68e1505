import express from "express";
import { BackendPlugin } from "../../../plugins/loader";
import aichatRoutes from "../routes/aichat";

// Create the backend plugin for AI Chat (CopilotKit)
const aichatBackendPlugin: BackendPlugin = {
  router: aichatRoutes,
  config: {
    name: "AI Chat API",
    version: "1.0.0",
    apiPrefix: "/api/aichat",
  },
  initialize: async () => {
    console.log("🤖 AI Chat backend plugin initialized");
  },
  cleanup: async () => {
    console.log("🤖 AI Chat backend plugin cleaned up");
  },
  healthCheck: async () => {
    try {
      return true;
    } catch (error) {
      console.error("AI Chat backend health check failed:", error);
      return false;
    }
  },
};

export default aichatBackendPlugin;
