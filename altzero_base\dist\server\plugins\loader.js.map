{"version": 3, "file": "loader.js", "sourceRoot": "", "sources": ["../../../server/plugins/loader.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,yCAA8E;AAe9E,MAAa,YAAY;IAGvB,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,GAAwB;QAC/C,MAAM,cAAc,GAAG,IAAA,mCAAwB,GAAE,CAAC;QAElD,OAAO,CAAC,GAAG,CAAC,cAAc,cAAc,CAAC,MAAM,mBAAmB,EAAE,cAAc,CAAC,CAAC;QAEpF,KAAK,MAAM,UAAU,IAAI,cAAc,EAAE,CAAC;YACxC,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;YAC1C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,UAAU,EAAE,EAAE,KAAK,CAAC,CAAC;YACzE,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,8CAA8C,IAAI,CAAC,aAAa,CAAC,IAAI,UAAU,CAAC,CAAC;IAC/F,CAAC;IAEO,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,GAAwB,EAAE,UAAkB;QAC3E,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,8BAA8B,UAAU,EAAE,CAAC,CAAC;YAExD,MAAM,YAAY,GAAG,IAAA,iCAAsB,EAAC,UAAU,CAAC,CAAC;YACxD,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,MAAM,IAAI,KAAK,CAAC,sCAAsC,UAAU,EAAE,CAAC,CAAC;YACtE,CAAC;YAED,mDAAmD;YACnD,IAAI,YAA2B,CAAC;YAEhC,IAAI,CAAC;gBACH,4DAA4D;gBAC5D,MAAM,YAAY,GAAG,yBAAa,eAAe,UAAU,mBAAmB,uCAAC,CAAC;gBAChF,YAAY,GAAG,YAAY,CAAC,OAAO,IAAI,YAAY,CAAC;YACtD,CAAC;YAAC,OAAO,WAAW,EAAE,CAAC;gBACrB,+DAA+D;gBAC/D,IAAI,CAAC;oBACH,MAAM,YAAY,GAAG,yBAAa,eAAe,UAAU,YAAY,uCAAC,CAAC;oBAEzE,qDAAqD;oBACrD,YAAY,GAAG;wBACb,MAAM,EAAE,YAAY,CAAC,OAAO,IAAI,YAAY;wBAC5C,MAAM,EAAE;4BACN,IAAI,EAAE,YAAY,CAAC,IAAI;4BACvB,OAAO,EAAE,YAAY,CAAC,OAAO;4BAC7B,SAAS,EAAE,YAAY,CAAC,SAAS;yBAClC;qBACF,CAAC;gBACJ,CAAC;gBAAC,OAAO,aAAa,EAAE,CAAC;oBACvB,OAAO,CAAC,KAAK,CAAC,8BAA8B,UAAU,GAAG,EAAE,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC,CAAC;oBAC3F,MAAM,IAAI,KAAK,CAAC,8CAA8C,UAAU,EAAE,CAAC,CAAC;gBAC9E,CAAC;YACH,CAAC;YAED,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;gBACzB,MAAM,IAAI,KAAK,CAAC,UAAU,UAAU,2BAA2B,CAAC,CAAC;YACnE,CAAC;YAED,yCAAyC;YACzC,IAAI,YAAY,CAAC,UAAU,EAAE,CAAC;gBAC5B,OAAO,CAAC,GAAG,CAAC,2BAA2B,UAAU,EAAE,CAAC,CAAC;gBACrD,MAAM,YAAY,CAAC,UAAU,EAAE,CAAC;YAClC,CAAC;YAED,gDAAgD;YAChD,IAAI,YAAY,CAAC,UAAU,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;gBACtE,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;oBAC3C,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;gBAC9C,CAAC,CAAC,CAAC;YACL,CAAC;YAED,0BAA0B;YAC1B,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,SAAS,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;YAErD,0BAA0B;YAC1B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;YAEjD,OAAO,CAAC,GAAG,CAAC,6BAA6B,UAAU,OAAO,YAAY,CAAC,SAAS,EAAE,CAAC,CAAC;QAEtF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YACvE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,MAAM,CAAC,gBAAgB;QACrB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;IAED,MAAM,CAAC,SAAS,CAAC,UAAkB;QACjC,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAC5C,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,WAAW;QACtB,MAAM,OAAO,GAA4B,EAAE,CAAC;QAE5C,KAAK,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACtD,IAAI,CAAC;gBACH,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;oBACvB,OAAO,CAAC,UAAU,CAAC,GAAG,MAAM,MAAM,CAAC,WAAW,EAAE,CAAC;gBACnD,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,CAAC,oCAAoC;gBAClE,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;gBACtE,OAAO,CAAC,UAAU,CAAC,GAAG,KAAK,CAAC;YAC9B,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,UAAkB;QAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAElD,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC;YACH,2BAA2B;YAC3B,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;YACzB,CAAC;YAED,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACtC,OAAO,CAAC,GAAG,CAAC,wBAAwB,UAAU,EAAE,CAAC,CAAC;YAClD,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YAChE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;;AArIH,oCAsIC;AArIgB,0BAAa,GAA+B,IAAI,GAAG,EAAE,CAAC"}