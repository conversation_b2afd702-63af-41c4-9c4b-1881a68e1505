// =====================================================
// CRM ACTION NODE - REAL CRM OPERATIONS
// =====================================================

import { BaseNode } from '../types/NodeTypes';
import { WorkflowContext } from '../types/WorkflowState';

export class CRMActionNode implements BaseNode {
  name = 'crm_action';
  description = 'Performs real CRM operations like creating contacts, searching data, and controlling CRM UI';

  async execute(context: WorkflowContext): Promise<any> {
    const { state, tools, logger } = context;
    const startTime = Date.now();

    try {
      logger.info('Starting CRM action processing', {
        workflow_id: state.workflow_id,
        current_intent: state.current_intent,
        detected_entities: state.detected_entities
      });

      // Check if this is a CRM-related intent
      if (!this.isCRMRelated(state)) {
        logger.debug('Skipping CRM actions - not CRM related');
        return {
          processing_time: state.processing_time,
          last_updated: new Date().toISOString()
        };
      }

      // Analyze the user message for CRM actions
      const crmAction = await this.analyzeCRMAction(state);
      logger.info('CRM action detected:', crmAction);

      let result: any = {};

      switch (crmAction.type) {
        case 'create_contact':
          result = await this.createContact(crmAction, state, logger);
          break;
        case 'search_contacts':
          result = await this.searchContacts(crmAction, state, logger);
          break;
        case 'create_company':
          result = await this.createCompany(crmAction, state, logger);
          break;
        case 'create_opportunity':
          result = await this.createOpportunity(crmAction, state, logger);
          break;
        case 'navigate_crm':
          result = await this.navigateCRM(crmAction, state, logger);
          break;
        case 'get_crm_stats':
          result = await this.getCRMStats(state, logger);
          break;
        default:
          logger.warn('Unknown CRM action type:', crmAction.type);
          result = { success: false, error: 'Unknown CRM action' };
      }

      const executionTime = Date.now() - startTime;

      return {
        ...result,
        processing_time: (state.processing_time || 0) + executionTime,
        last_updated: new Date().toISOString(),
        node_data: {
          ...state.node_data,
          crm_action: {
            action_type: crmAction.type,
            success: result.success,
            execution_time: executionTime,
            data: result.data
          }
        }
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      logger.error('CRM action processing failed', error);

      return {
        errors: [
          ...state.errors,
          {
            node_name: this.name,
            error_message: error instanceof Error ? error.message : 'Unknown CRM error',
            error_code: 'CRM_ACTION_FAILED',
            timestamp: new Date().toISOString(),
            recoverable: true
          }
        ],
        processing_time: (state.processing_time || 0) + executionTime,
        last_updated: new Date().toISOString()
      };
    }
  }

  // Check if the request is CRM-related
  private isCRMRelated(state: any): boolean {
    const message = state.messages[state.messages.length - 1]?.content.toLowerCase() || '';
    
    const crmKeywords = [
      'crm', 'contact', 'customer', 'lead', 'company', 'opportunity',
      'create contact', 'add contact', 'new contact', 'find contact',
      'search contact', 'contact list', 'customer list', 'crm dashboard'
    ];

    return crmKeywords.some(keyword => message.includes(keyword));
  }

  // Analyze what CRM action the user wants
  private async analyzeCRMAction(state: any): Promise<any> {
    const message = state.messages[state.messages.length - 1]?.content.toLowerCase() || '';
    
    // Create contact patterns
    if (message.includes('create contact') || message.includes('add contact') || message.includes('new contact')) {
      return {
        type: 'create_contact',
        data: this.extractContactData(message, state.detected_entities)
      };
    }

    // Search contacts patterns
    if (message.includes('find contact') || message.includes('search contact') || message.includes('show contacts')) {
      return {
        type: 'search_contacts',
        data: this.extractSearchTerms(message, state.detected_entities)
      };
    }

    // Create company patterns
    if (message.includes('create company') || message.includes('add company') || message.includes('new company')) {
      return {
        type: 'create_company',
        data: this.extractCompanyData(message, state.detected_entities)
      };
    }

    // Create opportunity patterns
    if (message.includes('create opportunity') || message.includes('add opportunity') || message.includes('new deal')) {
      return {
        type: 'create_opportunity',
        data: this.extractOpportunityData(message, state.detected_entities)
      };
    }

    // Navigation patterns
    if (message.includes('crm dashboard') || message.includes('go to crm') || message.includes('open crm')) {
      return {
        type: 'navigate_crm',
        data: this.extractNavigationTarget(message)
      };
    }

    // Stats patterns
    if (message.includes('crm stats') || message.includes('crm summary') || message.includes('crm overview')) {
      return {
        type: 'get_crm_stats',
        data: {}
      };
    }

    // Default to navigation if CRM is mentioned
    return {
      type: 'navigate_crm',
      data: { target: 'dashboard' }
    };
  }

  // Extract contact data from message
  private extractContactData(message: string, entities: any): any {
    const contactData: any = {};

    // Extract name patterns
    const nameMatch = message.match(/(?:contact|person|customer)\s+(?:named|called)?\s*([a-zA-Z\s]+)/i);
    if (nameMatch) {
      contactData.full_name = nameMatch[1].trim();
    }

    // Extract email patterns
    const emailMatch = message.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
    if (emailMatch) {
      contactData.email = emailMatch[1];
    }

    // Extract phone patterns
    const phoneMatch = message.match(/(?:phone|number|call)\s*:?\s*([+]?[\d\s\-\(\)]+)/i);
    if (phoneMatch) {
      contactData.phone = phoneMatch[1].replace(/\s+/g, '');
    }

    // Extract company patterns
    const companyMatch = message.match(/(?:company|works at|from)\s+([a-zA-Z\s&]+)/i);
    if (companyMatch) {
      contactData.company_name = companyMatch[1].trim();
    }

    return contactData;
  }

  // Extract search terms
  private extractSearchTerms(message: string, entities: any): any {
    const searchMatch = message.match(/(?:find|search|show|get)\s+(?:contacts?|customers?|people)\s+(?:named|called|with|for)?\s*([a-zA-Z\s@.]+)/i);
    
    return {
      search_term: searchMatch ? searchMatch[1].trim() : '',
      filters: entities
    };
  }

  // Extract company data
  private extractCompanyData(message: string, entities: any): any {
    const companyData: any = {};

    const nameMatch = message.match(/(?:company|business)\s+(?:named|called)?\s*([a-zA-Z\s&]+)/i);
    if (nameMatch) {
      companyData.name = nameMatch[1].trim();
    }

    const websiteMatch = message.match(/(https?:\/\/[^\s]+|www\.[^\s]+|[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
    if (websiteMatch) {
      companyData.website = websiteMatch[1];
    }

    return companyData;
  }

  // Extract opportunity data
  private extractOpportunityData(message: string, entities: any): any {
    const opportunityData: any = {};

    const titleMatch = message.match(/(?:opportunity|deal)\s+(?:for|named|called)?\s*([a-zA-Z\s]+)/i);
    if (titleMatch) {
      opportunityData.title = titleMatch[1].trim();
    }

    const valueMatch = message.match(/(?:worth|value|amount)\s*[$]?([0-9,]+)/i);
    if (valueMatch) {
      opportunityData.value = parseFloat(valueMatch[1].replace(/,/g, ''));
    }

    return opportunityData;
  }

  // Extract navigation target
  private extractNavigationTarget(message: string): any {
    if (message.includes('contacts')) return { target: 'contacts' };
    if (message.includes('companies')) return { target: 'companies' };
    if (message.includes('opportunities')) return { target: 'opportunities' };
    if (message.includes('activities')) return { target: 'activities' };
    if (message.includes('events')) return { target: 'events' };
    
    return { target: 'dashboard' };
  }

  // Create contact using real CRM API
  private async createContact(action: any, state: any, logger: any): Promise<any> {
    try {
      logger.info('Creating contact with data:', action.data);

      // Validate required fields
      if (!action.data.full_name) {
        return {
          success: false,
          error: 'Contact name is required',
          message: 'Please provide a name for the contact. For example: "Create contact named John Smith"'
        };
      }

      // Make API call to create contact
      const response = await fetch('http://localhost:3001/api/crm/contacts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': state.user_id,
          'x-api-key': process.env.API_KEY || 'dev-key'
        },
        body: JSON.stringify({
          full_name: action.data.full_name,
          email: action.data.email || '',
          phone: action.data.phone || '',
          job_title: action.data.job_title || '',
          organisation_id: await this.getUserDefaultOrganization(state.user_id),
          tags: ['ai-created'],
          custom_fields: {
            created_via: 'ai_chat',
            created_at: new Date().toISOString()
          }
        })
      });

      if (!response.ok) {
        throw new Error(`API call failed: ${response.status}`);
      }

      const newContact = await response.json();
      logger.info('Contact created successfully:', newContact.id);

      return {
        success: true,
        data: newContact,
        message: `✅ Contact "${action.data.full_name}" created successfully! You can view it in the CRM contacts page.`,
        navigation_action: {
          type: 'navigate',
          target: '/crm/contacts',
          highlight_id: newContact.id
        }
      };

    } catch (error) {
      logger.error('Failed to create contact:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to create contact. Please try again or create it manually in the CRM.'
      };
    }
  }

  // Search contacts using real CRM API
  private async searchContacts(action: any, state: any, logger: any): Promise<any> {
    try {
      logger.info('Searching contacts with term:', action.data.search_term);

      const searchParams = new URLSearchParams({
        search: action.data.search_term,
        limit: '10'
      });

      const response = await fetch(`http://localhost:3001/api/crm/contacts?${searchParams}`, {
        method: 'GET',
        headers: {
          'x-user-id': state.user_id,
          'x-api-key': process.env.API_KEY || 'dev-key'
        }
      });

      if (!response.ok) {
        throw new Error(`API call failed: ${response.status}`);
      }

      const result = await response.json();
      logger.info('Found contacts:', result.data.length);

      const contactsList = result.data.map((contact: any) => 
        `• ${contact.full_name}${contact.email ? ` (${contact.email})` : ''}${contact.phone ? ` - ${contact.phone}` : ''}`
      ).join('\n');

      return {
        success: true,
        data: result.data,
        message: result.data.length > 0 
          ? `Found ${result.data.length} contacts:\n\n${contactsList}\n\nYou can view all contacts in the CRM.`
          : 'No contacts found matching your search. Try a different search term or create a new contact.',
        navigation_action: {
          type: 'navigate',
          target: '/crm/contacts',
          search_term: action.data.search_term
        }
      };

    } catch (error) {
      logger.error('Failed to search contacts:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to search contacts. Please try again or search manually in the CRM.'
      };
    }
  }

  // Create company using real CRM API
  private async createCompany(action: any, state: any, logger: any): Promise<any> {
    try {
      logger.info('Creating company with data:', action.data);

      if (!action.data.name) {
        return {
          success: false,
          error: 'Company name is required',
          message: 'Please provide a name for the company. For example: "Create company named Acme Corp"'
        };
      }

      const response = await fetch('http://localhost:3001/api/crm/companies', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': state.user_id,
          'x-api-key': process.env.API_KEY || 'dev-key'
        },
        body: JSON.stringify({
          name: action.data.name,
          website: action.data.website || '',
          organisation_id: await this.getUserDefaultOrganization(state.user_id),
          custom_fields: {
            created_via: 'ai_chat',
            created_at: new Date().toISOString()
          }
        })
      });

      if (!response.ok) {
        throw new Error(`API call failed: ${response.status}`);
      }

      const newCompany = await response.json();
      logger.info('Company created successfully:', newCompany.id);

      return {
        success: true,
        data: newCompany,
        message: `✅ Company "${action.data.name}" created successfully! You can view it in the CRM companies page.`,
        navigation_action: {
          type: 'navigate',
          target: '/crm/companies',
          highlight_id: newCompany.id
        }
      };

    } catch (error) {
      logger.error('Failed to create company:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to create company. Please try again or create it manually in the CRM.'
      };
    }
  }

  // Create opportunity
  private async createOpportunity(action: any, state: any, logger: any): Promise<any> {
    // Implementation for creating opportunities
    return {
      success: true,
      message: 'Opportunity creation feature coming soon!',
      navigation_action: {
        type: 'navigate',
        target: '/crm/opportunities'
      }
    };
  }

  // Navigate to CRM section
  private async navigateCRM(action: any, state: any, logger: any): Promise<any> {
    const targetMap: Record<string, string> = {
      dashboard: '/crm',
      contacts: '/crm/contacts',
      companies: '/crm/companies',
      opportunities: '/crm/opportunities',
      activities: '/crm/activities',
      events: '/crm/events'
    };

    const target = targetMap[action.data.target] || '/crm';

    return {
      success: true,
      message: `Navigating to CRM ${action.data.target}...`,
      navigation_action: {
        type: 'navigate',
        target: target
      }
    };
  }

  // Get CRM statistics
  private async getCRMStats(state: any, logger: any): Promise<any> {
    try {
      // Get basic stats from CRM API
      const [contactsResponse, companiesResponse] = await Promise.all([
        fetch(`http://localhost:3001/api/crm/contacts?limit=1`, {
          headers: {
            'x-user-id': state.user_id,
            'x-api-key': process.env.API_KEY || 'dev-key'
          }
        }),
        fetch(`http://localhost:3001/api/crm/companies?limit=1`, {
          headers: {
            'x-user-id': state.user_id,
            'x-api-key': process.env.API_KEY || 'dev-key'
          }
        })
      ]);

      const contactsData = await contactsResponse.json();
      const companiesData = await companiesResponse.json();

      const stats = {
        total_contacts: contactsData.total || 0,
        total_companies: companiesData.total || 0
      };

      return {
        success: true,
        data: stats,
        message: `📊 CRM Overview:\n• Total Contacts: ${stats.total_contacts}\n• Total Companies: ${stats.total_companies}\n\nYou can view detailed analytics in the CRM dashboard.`,
        navigation_action: {
          type: 'navigate',
          target: '/crm'
        }
      };

    } catch (error) {
      return {
        success: false,
        message: 'Unable to fetch CRM statistics at the moment. Please check the CRM dashboard manually.'
      };
    }
  }

  // Get user's default organization
  private async getUserDefaultOrganization(userId: string): Promise<string> {
    // For now, return a default organization ID
    // In production, this would query the user's organizations
    return 'default-org-id';
  }
}
