// Load environment variables first
import dotenv from "dotenv";
import path from "path";

// Try to load .env from multiple locations
const envPaths = [
  path.join(__dirname, "../.env"), // altzero_base/.env
  path.join(__dirname, "../../.env"), // parent directory
  path.join(process.cwd(), ".env"), // current working directory
];

let envLoaded = false;
for (const envPath of envPaths) {
  try {
    const result = dotenv.config({ path: envPath });
    if (!result.error) {
      console.log(`✅ Loaded .env from: ${envPath}`);
      envLoaded = true;
      break;
    }
  } catch (error) {
    // Continue to next path
  }
}

if (!envLoaded) {
  console.log("⚠️  No .env file found. Please create one with your API keys.");
  console.log("📍 Expected location: altzero_base/.env");
}

// Debug: Log environment variables
console.log("🔧 Environment Variables Status:");
console.log(
  `LLAMA_CLOUD_API_KEY: ${
    process.env.LLAMA_CLOUD_API_KEY ? "✅ Set" : "❌ Missing"
  }`
);
console.log(
  `OPENAI_API_KEY: ${process.env.OPENAI_API_KEY ? "✅ Set" : "❌ Missing"}`
);
console.log(`NODE_ENV: ${process.env.NODE_ENV || "development"}`);
console.log(`PORT: ${process.env.PORT || 3001}`);

// SEO Environment Variables
console.log("📊 SEO Configuration:");
console.log(
  `SEO_PAGESPEED_PROVIDER: ${
    process.env.SEO_PAGESPEED_PROVIDER || "❌ Missing"
  }`
);
console.log(
  `SEO_PAGESPEED_API_KEY: ${
    process.env.SEO_PAGESPEED_API_KEY ? "✅ Set" : "❌ Missing"
  }`
);
console.log(
  `SEO_GENERATOR_PROVIDER: ${
    process.env.SEO_GENERATOR_PROVIDER || "❌ Missing"
  }`
);
console.log(
  `SEO_GENERATOR_API_KEY: ${
    process.env.SEO_GENERATOR_API_KEY ? "✅ Set" : "❌ Missing"
  }`
);

import express from "express";
import cors from "cors";
import session from "express-session";
import { validateApiKey } from "./base/common/routes/auth";
import { environment } from "./base/common/apps/config/environment";
import { PluginLoader } from "./plugins/loader";

// Extend Express Request type to include session
declare module "express-session" {
  interface SessionData {
    [key: string]: any;
  }
}

// Initialize the app
const app = express();

// Configure middleware
app.use(
  cors({
    origin: [
      "http://localhost:5173", // Vite dev server
      "http://localhost:3000", // CRA dev server (optional)
      "http://localhost:3001", // API server itself (optional)
      "http://127.0.0.1:5173", // Alternative localhost
    ],
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allowedHeaders: [
      "Content-Type",
      "x-copilotkit-runtime-client-gql-version",
      "Authorization",
      "x-api-key",
      "x-user-id",
      "Accept",
      "Origin",
      "X-Requested-With",
      "Access-Control-Request-Method",
      "Access-Control-Request-Headers",
    ],
    credentials: true,
    exposedHeaders: ["Content-Disposition"],
    optionsSuccessStatus: 200, // For legacy browser support
    preflightContinue: false,
  })
);

// Configure basic JSON parsing for all routes
app.use(
  express.json({
    limit: "10mb", // Increase limit for pSEO content analysis
  })
);

// Configure URL-encoded parsing with same limit
app.use(
  express.urlencoded({
    limit: "10mb",
    extended: true,
  })
);

// Session configuration
const sessionMiddleware = session({
  secret:
    process.env.SESSION_SECRET ||
    environment.sessionSecret ||
    "default-secret-key",
  resave: false,
  saveUninitialized: true,
  cookie: {
    secure: environment.nodeEnv === "production",
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
  },
});

app.use(sessionMiddleware as any);

// Logging middleware
app.use((req, _res, next) => {
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);
  next();
});

// Health check route
app.get("/health", (_req, res) => {
  res.status(200).json({ status: "ok" });
});

// Plugin health check route
app.get("/api/plugins/health", async (_req, res) => {
  try {
    const pluginHealth = await PluginLoader.healthCheck();
    res.status(200).json({
      status: "ok",
      plugins: pluginHealth,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    res.status(500).json({
      status: "error",
      error: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
    });
  }
});

// Protected route example
app.get("/api/protected", validateApiKey, (_req, res) => {
  res.status(200).json({ message: "This is a protected route" });
});

// 🆕 Load plugins automatically
const startServer = async () => {
  try {
    // TEMPORARY: Mount scopingAi routes directly for testing
    console.log("🧪 Temporarily mounting scopingAi routes directly...");
    try {
      const scopingAiRoutes = await import(
        "./features/scopingAi/routes/scopingAi"
      );
      app.use("/api/scopingai", scopingAiRoutes.default);
      console.log("✅ ScopingAI routes mounted directly at /api/scopingai");
    } catch (error) {
      console.error("❌ Failed to mount scopingAi routes directly:", error);
    }

    // TEMPORARY: Mount pSEO routes directly for testing
    console.log("🧪 Temporarily mounting pSEO routes directly...");
    try {
      const pseoRoutes = await import("./features/pseo/routes");
      app.use("/api/pseo", pseoRoutes.default);
      console.log("✅ pSEO routes mounted directly at /api/pseo");
    } catch (error) {
      console.error("❌ Failed to mount pSEO routes directly:", error);
    }

    // TEMPORARY: Mount knowledge routes directly for testing
    console.log("🧪 Temporarily mounting knowledge routes directly...");
    try {
      const knowledgeRoutes = await import(
        "./features/knowledge/routes/knowledge"
      );
      app.use("/api/knowledge", knowledgeRoutes.default);
      console.log("✅ Knowledge routes mounted directly at /api/knowledge");

      // Test if the upload route is available
      console.log("🔍 Testing knowledge routes structure:");
      console.log(
        "Router stack length:",
        knowledgeRoutes.default?.stack?.length || "unknown"
      );
      if (knowledgeRoutes.default?.stack) {
        knowledgeRoutes.default.stack.forEach((layer: any, index: number) => {
          console.log(`Route ${index}:`, {
            method: layer.route?.methods || "unknown",
            path: layer.route?.path || "unknown",
          });
        });
      }
    } catch (error) {
      console.error("❌ Failed to mount knowledge routes directly:", error);

      // EMERGENCY FALLBACK: If knowledge routes fail to mount, redirect to scopingAi
      console.log("🚨 Adding emergency fallback for /api/knowledge/documents");
      app.get("/api/knowledge/documents", (req, res) => {
        console.log(
          "🚨 EMERGENCY FALLBACK: /api/knowledge/documents -> /api/scopingai/knowledge/documents"
        );
        console.log("Query params:", req.query);

        // Forward the request to our working scopingAi endpoint with API key
        const params = new URLSearchParams(req.query as any);
        // Ensure API key is included
        if (!params.has("apiKey")) {
          params.set("apiKey", "scopingai");
        }
        const redirectUrl = `/api/scopingai/knowledge/documents?${params.toString()}`;

        console.log("Redirecting to:", redirectUrl);
        res.redirect(302, redirectUrl);
      });
    }

    // TEMPORARY: Mount aichat routes directly for testing
    console.log("🧪 Temporarily mounting aichat routes directly...");
    try {
      const aichatRoutes = await import("./features/aichat/routes/aichat");
      app.use("/api/aichat", aichatRoutes.default);
      console.log("✅ AI Chat routes mounted directly at /api/aichat");
    } catch (error) {
      console.error("❌ Failed to mount aichat routes directly:", error);
    }

    // Load all enabled plugins
    await PluginLoader.loadPlugins(app);

    // Import and mount any remaining core routes
    const coreRoutes = await import("./base/common/routes/index");
    app.use(coreRoutes.default);

    // ADDITIONAL EMERGENCY FALLBACK: Catch any missed /api/knowledge/documents requests
    app.get("/api/knowledge/documents", (req, res) => {
      console.log(
        "🚨 FINAL FALLBACK: /api/knowledge/documents -> /api/scopingai/knowledge/documents"
      );
      console.log("Query params:", req.query);
      console.log("Headers:", {
        "x-user-id": req.headers["x-user-id"],
        "x-api-key": req.headers["x-api-key"],
      });

      // Forward the request to our working scopingAi endpoint with API key
      const params = new URLSearchParams(req.query as any);
      // Ensure API key is included
      if (!params.has("apiKey")) {
        params.set("apiKey", "scopingai");
      }
      const redirectUrl = `/api/scopingai/knowledge/documents?${params.toString()}`;

      console.log("Final redirect to:", redirectUrl);
      res.redirect(302, redirectUrl);
    });

    // FINAL FALLBACK: DELETE handler for knowledge documents
    app.delete("/api/knowledge/documents/:documentId", async (req, res) => {
      try {
        console.log(
          "🚨 FINAL FALLBACK DELETE: /api/knowledge/documents/:documentId"
        );
        console.log("Document ID:", req.params.documentId);
        console.log("User ID:", req.headers["x-user-id"]);

        // Import pineconeService directly and use it
        const { pineconeService } = await import(
          "./features/knowledge/services/pineconeService"
        );

        const documentId = req.params.documentId;
        const userId = (req.headers["x-user-id"] as string) || "anonymous";

        // Verify user owns this document by checking Pinecone
        const userDocs = await pineconeService.getUserDocuments(userId);
        const document = userDocs.find((d) => d.id === documentId);

        if (!document) {
          console.log(
            `❌ Document ${documentId} not found or access denied for user ${userId}`
          );
          return res
            .status(404)
            .json({ error: "Document not found or access denied" });
        }

        console.log(
          `✅ Document ${documentId} found, proceeding with deletion`
        );

        // Remove from Pinecone (this will delete all chunks)
        await pineconeService.deleteDocuments([documentId]);

        console.log(
          `✅ Successfully deleted document ${documentId} for user ${userId}`
        );
        res.json({
          message: "Document deleted successfully",
          documentId: documentId,
          documentName: document.name,
        });
      } catch (error) {
        console.error(`❌ Error in fallback delete:`, error);
        res.status(500).json({
          error: "Failed to delete document",
          details: error instanceof Error ? error.message : "Unknown error",
        });
      }
    });

    // FINAL FALLBACK: POST handler for knowledge document upload
    app.post("/api/knowledge/documents/upload", async (req, res) => {
      try {
        console.log(
          "🚨 FINAL FALLBACK UPLOAD: /api/knowledge/documents/upload"
        );
        console.log("User ID:", req.headers["x-user-id"]);
        console.log("Files:", req.files);

        // Since the knowledge routes aren't properly mounted, return an informative error
        res.status(503).json({
          error: "Knowledge routes not properly mounted",
          message:
            "The document upload service is temporarily unavailable. Please restart the server.",
          suggestion:
            "Try restarting the server to reload the knowledge routes properly.",
        });
      } catch (error) {
        console.error(`❌ Error in fallback upload:`, error);
        res.status(500).json({
          error: "Upload service unavailable",
          details: error instanceof Error ? error.message : "Unknown error",
        });
      }
    });

    // Start the server
    const PORT = process.env.PORT || 3001;
    app.listen(PORT, () => {
      console.log(`=======================================================`);
      console.log(`🚀 Backend server running at http://localhost:${PORT}`);
      console.log(`📦 Loaded plugins: ${PluginLoader.getLoadedPlugins().size}`);
      console.log(`=======================================================`);
    });
  } catch (error) {
    console.error("❌ Failed to start server:", error);
    process.exit(1);
  }
};

startServer();
