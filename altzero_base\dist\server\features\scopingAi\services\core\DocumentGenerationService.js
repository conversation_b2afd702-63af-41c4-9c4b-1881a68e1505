"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.documentGenerationService = exports.DocumentGenerationService = void 0;
const openai_1 = require("openai");
const ScopingAiDatabaseService_1 = require("./ScopingAiDatabaseService");
class DocumentGenerationService {
    constructor() {
        this.initialized = false;
        this.llm = new openai_1.OpenAI({
            apiKey: process.env.OPENAI_API_KEY,
            baseURL: process.env.OPENAI_BASE_URL || "https://api.openai.com/v1",
            timeout: 60000,
            maxRetries: 3,
        });
    }
    async initialize() {
        try {
            console.log("🔄 Initializing Document Generation Service...");
            // Test LLM connection
            const testResponse = await this.llm.chat.completions.create({
                messages: [{ role: "user", content: "Test connection" }],
                model: process.env.OPENAI_MODEL || "gpt-3.5-turbo",
                max_tokens: 10,
            });
            console.log("✅ Document Generation Service initialized successfully");
            this.initialized = true;
        }
        catch (error) {
            const sanitizedError = error.message?.replace(/sk-[a-zA-Z0-9_-]+/g, "[API_KEY]");
            console.error("❌ Document Generation Service initialization failed:", sanitizedError);
            throw error;
        }
    }
    async *generateDocumentStream(request, knowledgeBaseContent, referenceDocuments) {
        const documentId = `doc_${Date.now()}`;
        try {
            if (!this.initialized) {
                await this.initialize();
            }
            // Send initial event
            yield {
                type: "started",
                documentId,
                data: {
                    message: "Starting document generation...",
                    client: request.client.name,
                    project: request.project.name
                },
            };
            const model = process.env.OPENAI_MODEL || "gpt-3.5-turbo";
            const sections = [];
            // Research phase
            yield {
                type: "research",
                documentId,
                data: { status: "started", message: "Analyzing project requirements..." },
            };
            const researchPrompt = this.buildResearchPrompt(request, knowledgeBaseContent);
            const researchResponse = await this.llm.chat.completions.create({
                messages: [{ role: "user", content: researchPrompt }],
                model: model,
                max_tokens: 2000,
                temperature: 0.7,
            });
            const researchContent = researchResponse.choices[0].message.content || "";
            yield {
                type: "research",
                documentId,
                data: {
                    status: "completed",
                    content: researchContent,
                    message: "Research phase completed"
                },
            };
            // Generate sections
            const totalSections = request.template.sections.length;
            let completedSections = 0;
            for (const sectionDef of request.template.sections) {
                yield {
                    type: "progress",
                    documentId,
                    data: {
                        message: `Generating ${sectionDef.title}...`,
                        progress: Math.round((completedSections / totalSections) * 100)
                    },
                };
                const sectionPrompt = this.buildSectionPrompt(sectionDef, request, researchContent, knowledgeBaseContent);
                const sectionResponse = await this.llm.chat.completions.create({
                    messages: [{ role: "user", content: sectionPrompt }],
                    model: model,
                    max_tokens: 1500,
                    temperature: 0.7,
                });
                const section = {
                    title: sectionDef.title,
                    content: sectionResponse.choices[0].message.content || "",
                    metadata: {
                        description: sectionDef.description,
                        required: sectionDef.required,
                        generatedAt: new Date().toISOString()
                    }
                };
                sections.push(section);
                completedSections++;
                yield {
                    type: "section",
                    documentId,
                    data: {
                        status: "completed",
                        section,
                        progress: Math.round((completedSections / totalSections) * 100)
                    },
                };
            }
            // Create final document
            const document = {
                id: documentId,
                title: `${request.project.name} - ${request.template.name}`,
                sections,
                metadata: {
                    generatedBy: "AltZero ScopingAI",
                    version: "1.0",
                    totalSections: sections.length,
                    referenceDocumentsUsed: referenceDocuments?.length || 0,
                    hasKnowledgeBase: !!knowledgeBaseContent,
                    proposalQuality: knowledgeBaseContent ? "Enhanced with Knowledge Base" : "Standard",
                    aiModel: model,
                    generationTimestamp: new Date().toISOString(),
                },
            };
            // Save to database
            try {
                const proposalId = await ScopingAiDatabaseService_1.scopingAiDbService.createProposal({
                    userId: request.userId,
                    clientId: request.client.id,
                    title: document.title,
                    description: request.project.description,
                    status: 'generated',
                    templateId: request.template.id,
                    metadata: {
                        generationRequest: request,
                        referenceDocuments: referenceDocuments?.map(doc => ({
                            id: doc.id,
                            title: doc.title,
                            userRequirement: doc.userRequirement
                        })) || []
                    }
                });
                await ScopingAiDatabaseService_1.scopingAiDbService.saveGeneratedDocument({
                    proposalId,
                    userId: request.userId,
                    title: document.title,
                    content: {
                        sections: document.sections,
                        metadata: document.metadata
                    },
                    metadata: document.metadata,
                    generationSettings: {
                        model,
                        hasKnowledgeBase: !!knowledgeBaseContent,
                        referenceDocumentsCount: referenceDocuments?.length || 0
                    }
                });
                console.log(`✅ Document saved to database with proposal ID: ${proposalId}`);
            }
            catch (dbError) {
                console.error("❌ Failed to save document to database:", dbError);
                // Continue with generation even if DB save fails
            }
            // Completion event
            yield {
                type: "completed",
                documentId,
                data: {
                    document,
                    message: "Document generation completed successfully!",
                    progress: 100
                },
            };
        }
        catch (error) {
            const sanitizedError = error.message?.replace(/sk-[a-zA-Z0-9_-]+/g, "[API_KEY]");
            console.error("❌ Error in document generation:", sanitizedError);
            yield {
                type: "error",
                documentId,
                error: "Document generation failed",
                data: { details: sanitizedError }
            };
        }
    }
    buildResearchPrompt(request, knowledgeBaseContent) {
        return `You are a senior business analyst creating a comprehensive research foundation for a client proposal.

CLIENT & PROJECT OVERVIEW:
- Client: ${request.client.name}
- Company: ${request.client.company || 'Not specified'}
- Industry: ${request.client.industry || 'Not specified'}
- Contact: ${request.client.contactPerson || 'Not specified'}
- Project: ${request.project.name}
- Description: ${request.project.description}
- Objectives: ${request.project.objectives || 'Not specified'}

PROJECT REQUIREMENTS:
${request.requirements.projectRequirements}

${knowledgeBaseContent ? `
KNOWLEDGE BASE REFERENCE MATERIALS:
${knowledgeBaseContent}

INSTRUCTIONS: Use the reference materials above to enhance your research with specific insights, methodologies, and best practices relevant to this project.
` : ''}

Please conduct thorough research and provide a comprehensive analysis that will serve as the foundation for generating a professional proposal document.`;
    }
    buildSectionPrompt(sectionDef, request, researchContent, knowledgeBaseContent) {
        return `Based on the following research and project information:

Research:
${researchContent}

Client Information:
- Name: ${request.client.name}
- Company: ${request.client.company || 'Not specified'}
- Industry: ${request.client.industry || 'Not specified'}

Project Information:
- Name: ${request.project.name}
- Description: ${request.project.description}

${knowledgeBaseContent ? `
Reference Materials:
${knowledgeBaseContent}
` : ''}

Please write the "${sectionDef.title}" section for the project proposal document.
Section Description: ${sectionDef.description}

Requirements:
- Professional tone appropriate for executive-level stakeholders
- Clear, concise, and actionable content
- Data-driven insights and recommendations
- Industry-specific terminology and considerations
- Compelling value proposition aligned with client needs
- Structured format with clear headings and bullet points where appropriate
${knowledgeBaseContent ? '- Strategic integration of reference materials to strengthen credibility' : ''}`;
    }
}
exports.DocumentGenerationService = DocumentGenerationService;
exports.documentGenerationService = new DocumentGenerationService();
//# sourceMappingURL=DocumentGenerationService.js.map