"use strict";
// =====================================================
// BASE WORKFLOW CLASS FOR LANGGRAPH PSEO WORKFLOWS
// =====================================================
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseWorkflow = void 0;
const ToolRegistry_1 = require("./ToolRegistry");
const StateManager_1 = require("./StateManager");
class BaseWorkflow {
    constructor(config) {
        this.config = config;
        this.tools = new ToolRegistry_1.ToolRegistry(config);
        this.stateManager = StateManager_1.StateManager.getInstance(); // Use singleton instance
        this.nodes = new Map();
        this.nodeMetadata = new Map();
        this.nodeExecutionOrder = [];
        this.logger = this.createLogger();
        // Don't initialize workflow here - let subclass call it after setup
    }
    // Initialize the workflow
    initializeWorkflow() {
        this.defineNodes();
        this.defineEdges();
        this.setupErrorHandling();
    }
    // Register a node in the workflow
    registerNode(node, metadata) {
        if (!node) {
            throw new Error('Node is null or undefined');
        }
        if (!node.name) {
            throw new Error(`Node name is undefined. Node type: ${node.constructor?.name}`);
        }
        this.nodes.set(node.name, node);
        this.nodeMetadata.set(node.name, metadata);
        // Add to execution order based on metadata
        this.addToExecutionOrder(node.name, metadata.execution_order);
        this.logger.debug(`Registered node: ${node.name}`, { metadata });
    }
    // Add node to execution order
    addToExecutionOrder(nodeName, order) {
        // Insert node in correct position based on execution order
        let inserted = false;
        for (let i = 0; i < this.nodeExecutionOrder.length; i++) {
            const existingNode = this.nodeExecutionOrder[i];
            const existingOrder = this.nodeMetadata.get(existingNode)?.execution_order || 0;
            if (order < existingOrder) {
                this.nodeExecutionOrder.splice(i, 0, nodeName);
                inserted = true;
                break;
            }
        }
        if (!inserted) {
            this.nodeExecutionOrder.push(nodeName);
        }
    }
    // Execute a specific node
    async executeNode(nodeName, state) {
        const node = this.nodes.get(nodeName);
        const metadata = this.nodeMetadata.get(nodeName);
        if (!node || !metadata) {
            throw new Error(`Node not found: ${nodeName}`);
        }
        const startTime = Date.now();
        let result;
        try {
            this.logger.info(`Executing node: ${nodeName}`, {
                workflow_id: state.workflow_id,
                current_step: state.current_step
            });
            // Update state to show current node execution
            const updatedState = {
                current_step: nodeName,
                last_updated: new Date().toISOString()
            };
            // Create execution context
            const context = {
                state: { ...state, ...updatedState },
                tools: await this.tools.getTools(),
                logger: this.logger,
                config: this.config
            };
            // Execute the node with retry logic
            const nodeResult = await this.executeWithRetry(node, context, metadata.retry_attempts);
            const endTime = Date.now();
            const executionTime = endTime - startTime;
            result = {
                ...nodeResult,
                node_name: nodeName,
                status: nodeResult.success ? 'completed' : 'failed',
                started_at: new Date(startTime).toISOString(),
                completed_at: new Date(endTime).toISOString(),
                execution_time_ms: executionTime,
                retry_count: 0, // Will be updated by retry logic
                warnings: []
            };
            // Log execution result
            this.logger.info(`Node execution completed: ${nodeName}`, {
                success: result.success,
                execution_time: executionTime,
                data_points: result.metrics?.data_points_processed || 0
            });
            // Update processing time in state
            const totalProcessingTime = (state.processing_time || 0) + executionTime;
            return {
                ...nodeResult.data,
                processing_time: totalProcessingTime,
                last_updated: new Date().toISOString(),
                node_data: {
                    ...state.node_data,
                    [nodeName]: result
                }
            };
        }
        catch (error) {
            const endTime = Date.now();
            const executionTime = endTime - startTime;
            this.logger.error(`Node execution failed: ${nodeName}`, error);
            // Add error to state
            const workflowError = {
                node_name: nodeName,
                error_message: error instanceof Error ? error.message : 'Unknown error',
                error_code: 'NODE_EXECUTION_FAILED',
                timestamp: new Date().toISOString(),
                recoverable: false
            };
            return {
                status: 'failed',
                errors: [...(state.errors || []), workflowError],
                processing_time: (state.processing_time || 0) + executionTime,
                last_updated: new Date().toISOString()
            };
        }
    }
    // Execute node with retry logic
    async executeWithRetry(node, context, maxRetries) {
        let lastError = new Error('Unknown error');
        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                if (attempt > 0) {
                    this.logger.warn(`Retrying node execution: ${node.name}`, {
                        attempt,
                        maxRetries
                    });
                    // Exponential backoff
                    const delay = Math.pow(2, attempt) * 1000;
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
                const result = await node.execute(context);
                return { success: true, data: result };
            }
            catch (error) {
                lastError = error instanceof Error ? error : new Error('Unknown error');
                if (attempt === maxRetries) {
                    break;
                }
            }
        }
        return {
            success: false,
            error: lastError.message,
            data: {}
        };
    }
    // Simplified edge methods (for sequential execution)
    addEdge(fromNode, toNode) {
        // In simplified version, edges are handled by execution order
        this.logger.debug(`Edge added: ${fromNode} -> ${toNode}`);
    }
    // Add conditional edge (simplified)
    addConditionalEdge(fromNode, condition, pathMap) {
        // Store conditional logic for later use
        this.logger.debug(`Conditional edge added: ${fromNode}`, { pathMap });
    }
    // Set entry and exit points (simplified)
    setEntryPoint(nodeName) {
        this.logger.debug(`Entry point set: ${nodeName}`);
    }
    setExitPoint(nodeName) {
        this.logger.debug(`Exit point set: ${nodeName}`);
    }
    // Execute workflow sequentially
    async executeSequentially(state) {
        let currentState = { ...state };
        for (const nodeName of this.nodeExecutionOrder) {
            try {
                this.logger.info(`Executing node: ${nodeName}`);
                // Update progress
                const progress = Math.round((this.nodeExecutionOrder.indexOf(nodeName) / this.nodeExecutionOrder.length) * 100);
                currentState.progress = progress;
                currentState.current_step = nodeName;
                // Save state
                await this.stateManager.saveWorkflowState(currentState.workflow_id, currentState);
                // Execute node
                const nodeResult = await this.executeNode(nodeName, currentState);
                // Merge results
                currentState = { ...currentState, ...nodeResult };
                // Check for errors
                if (currentState.status === 'failed') {
                    this.logger.error(`Node ${nodeName} failed, stopping workflow`);
                    break;
                }
            }
            catch (error) {
                this.logger.error(`Error executing node ${nodeName}:`, error);
                currentState.status = 'failed';
                currentState.errors = [
                    ...(currentState.errors || []),
                    {
                        node_name: nodeName,
                        error_message: error instanceof Error ? error.message : 'Unknown error',
                        error_code: 'NODE_EXECUTION_ERROR',
                        timestamp: new Date().toISOString(),
                        recoverable: false
                    }
                ];
                break;
            }
        }
        return currentState;
    }
    // Execute the entire workflow
    async execute(input) {
        const workflowId = this.generateWorkflowId();
        const startTime = Date.now();
        try {
            this.logger.info(`Starting workflow execution: ${this.getWorkflowName()}`, {
                workflow_id: workflowId,
                input
            });
            // Initialize state
            const initialState = {
                workflow_id: workflowId,
                user_id: input.user_id,
                website_id: input.website_id,
                domain: input.domain || '',
                seed_keywords: input.seed_keywords || [],
                research_method: input.research_method,
                topic_input: input.topic_input,
                competitor_domains: input.competitor_domains || [],
                max_keywords: input.max_keywords || 100,
                data_sources: input.data_sources || [],
                current_step: 'initializing',
                progress: 0,
                status: 'running',
                errors: [],
                keywords: [],
                keyword_clusters: [],
                competitor_data: [],
                content_suggestions: [],
                api_calls_made: [],
                processing_time: 0,
                data_sources_used: [],
                total_cost: 0,
                started_at: new Date().toISOString(),
                last_updated: new Date().toISOString(),
                node_data: {},
                config: {
                    timeout_seconds: 300,
                    retry_attempts: 3,
                    enable_caching: true,
                    quality_threshold: 0.7
                }
            };
            // Save initial state immediately
            await this.stateManager.saveWorkflowState(workflowId, initialState);
            // Execute the workflow sequentially
            const result = await this.executeSequentially(initialState);
            // Update final state
            const endTime = Date.now();
            const finalState = {
                ...result,
                status: result.errors && result.errors.length > 0 ? 'failed' : 'completed',
                completed_at: new Date().toISOString(),
                processing_time: endTime - startTime,
                progress: 100
            };
            // Save final state
            await this.stateManager.saveWorkflowState(workflowId, finalState);
            this.logger.info(`Workflow execution completed: ${this.getWorkflowName()}`, {
                workflow_id: workflowId,
                status: finalState.status,
                processing_time: finalState.processing_time,
                keywords_found: finalState.keywords.length
            });
            return finalState;
        }
        catch (error) {
            this.logger.error(`Workflow execution failed: ${this.getWorkflowName()}`, error);
            const endTime = Date.now();
            return {
                workflow_id: workflowId,
                user_id: input.user_id,
                website_id: input.website_id,
                domain: input.domain || '',
                seed_keywords: input.seed_keywords || [],
                research_method: input.research_method,
                topic_input: input.topic_input,
                competitor_domains: input.competitor_domains || [],
                max_keywords: input.max_keywords || 100,
                data_sources: input.data_sources || [],
                current_step: 'failed',
                progress: 0,
                status: 'failed',
                errors: [{
                        node_name: 'workflow',
                        error_message: error instanceof Error ? error.message : 'Unknown error',
                        error_code: 'WORKFLOW_EXECUTION_FAILED',
                        timestamp: new Date().toISOString(),
                        recoverable: false
                    }],
                keywords: [],
                keyword_clusters: [],
                competitor_data: [],
                content_suggestions: [],
                api_calls_made: [],
                processing_time: endTime - startTime,
                data_sources_used: [],
                total_cost: 0,
                started_at: new Date().toISOString(),
                completed_at: new Date().toISOString(),
                last_updated: new Date().toISOString(),
                node_data: {},
                config: {
                    timeout_seconds: 300,
                    retry_attempts: 3,
                    enable_caching: true,
                    quality_threshold: 0.7
                }
            };
        }
    }
    // Setup error handling
    setupErrorHandling() {
        // Add global error handling logic here
        this.logger.debug('Error handling setup completed');
    }
    // Create logger instance
    createLogger() {
        return {
            info: (message, data) => {
                console.log(`[INFO] ${this.getWorkflowName()}: ${message}`, data || '');
            },
            warn: (message, data) => {
                console.warn(`[WARN] ${this.getWorkflowName()}: ${message}`, data || '');
            },
            error: (message, error) => {
                console.error(`[ERROR] ${this.getWorkflowName()}: ${message}`, error || '');
            },
            debug: (message, data) => {
                if (process.env.NODE_ENV === 'development') {
                    console.debug(`[DEBUG] ${this.getWorkflowName()}: ${message}`, data || '');
                }
            }
        };
    }
    // Generate unique workflow ID
    generateWorkflowId() {
        return `${this.getWorkflowName()}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    // Get workflow status
    async getStatus(workflowId) {
        return this.stateManager.getWorkflowStatus(workflowId);
    }
    // Cancel workflow execution
    async cancel(workflowId) {
        this.logger.info(`Cancelling workflow: ${workflowId}`);
        // Implementation for workflow cancellation
    }
}
exports.BaseWorkflow = BaseWorkflow;
//# sourceMappingURL=BaseWorkflow.js.map