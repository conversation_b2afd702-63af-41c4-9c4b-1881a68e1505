{"hash": "62daf765", "browserHash": "734a132f", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "0c665f6b", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "fbc13733", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "3ccfbede", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "dc1019b1", "needsInterop": true}, "@copilotkit/react-core": {"src": "../../@copilotkit/react-core/dist/index.mjs", "file": "@copilotkit_react-core.js", "fileHash": "653bbde7", "needsInterop": false}, "@hookform/resolvers/zod": {"src": "../../@hookform/resolvers/zod/dist/zod.mjs", "file": "@hookform_resolvers_zod.js", "fileHash": "7f17c8ad", "needsInterop": false}, "@radix-ui/react-alert-dialog": {"src": "../../@radix-ui/react-alert-dialog/dist/index.mjs", "file": "@radix-ui_react-alert-dialog.js", "fileHash": "2903e23d", "needsInterop": false}, "@radix-ui/react-avatar": {"src": "../../@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "a7f94b5c", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "89d6b1e7", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "bfa6b03e", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "f3be6e4f", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "0660b245", "needsInterop": false}, "@radix-ui/react-progress": {"src": "../../@radix-ui/react-progress/dist/index.mjs", "file": "@radix-ui_react-progress.js", "fileHash": "ce4eab68", "needsInterop": false}, "@radix-ui/react-radio-group": {"src": "../../@radix-ui/react-radio-group/dist/index.mjs", "file": "@radix-ui_react-radio-group.js", "fileHash": "d35668e6", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "adf9419b", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "6518155a", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "943092f3", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "0c5eb8d7", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "b3a4ad63", "needsInterop": false}, "@supabase/supabase-js": {"src": "../../@supabase/supabase-js/dist/module/index.js", "file": "@supabase_supabase-js.js", "fileHash": "fa3001fa", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "2d3f9a1b", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "193a32fb", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "80313a9f", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "fafc3da6", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "47531ccc", "needsInterop": true}, "react-hook-form": {"src": "../../react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "494f1890", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "c2e0b61d", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "49267d52", "needsInterop": false}, "zod": {"src": "../../zod/dist/esm/index.js", "file": "zod.js", "fileHash": "18b008fa", "needsInterop": false}}, "chunks": {"lib-EZJSIVHL": {"file": "lib-EZJSIVHL.js"}, "browser-7ILX4ERV": {"file": "browser-7ILX4ERV.js"}, "browser-FYKLWRQN": {"file": "browser-FYKLWRQN.js"}, "chunk-FMEW3YNC": {"file": "chunk-FMEW3YNC.js"}, "chunk-AJTCXCUR": {"file": "chunk-AJTCXCUR.js"}, "chunk-X2CLXSXA": {"file": "chunk-X2CLXSXA.js"}, "chunk-3KAGYO6E": {"file": "chunk-3KAGYO6E.js"}, "chunk-SBRM6RS4": {"file": "chunk-SBRM6RS4.js"}, "chunk-JOZ6ASKG": {"file": "chunk-JOZ6ASKG.js"}, "chunk-A7E35SHE": {"file": "chunk-A7E35SHE.js"}, "chunk-JBVW7BNC": {"file": "chunk-JBVW7BNC.js"}, "chunk-SURRYCUG": {"file": "chunk-SURRYCUG.js"}, "chunk-V665XK4Y": {"file": "chunk-V665XK4Y.js"}, "chunk-AECO5ISZ": {"file": "chunk-AECO5ISZ.js"}, "chunk-CMRLIO42": {"file": "chunk-CMRLIO42.js"}, "chunk-M4AFC7J6": {"file": "chunk-M4AFC7J6.js"}, "chunk-6VEFFVLK": {"file": "chunk-6VEFFVLK.js"}, "chunk-3PDT32C7": {"file": "chunk-3PDT32C7.js"}, "chunk-EVMCGSRL": {"file": "chunk-EVMCGSRL.js"}, "chunk-6ORVEWJI": {"file": "chunk-6ORVEWJI.js"}, "chunk-GE7ADNQB": {"file": "chunk-GE7ADNQB.js"}, "chunk-KYQVGTGN": {"file": "chunk-KYQVGTGN.js"}, "chunk-MYWBKV7L": {"file": "chunk-MYWBKV7L.js"}, "chunk-GUJ5INIC": {"file": "chunk-GUJ5INIC.js"}, "chunk-SBIIHPLX": {"file": "chunk-SBIIHPLX.js"}, "chunk-6XNNJITF": {"file": "chunk-6XNNJITF.js"}, "chunk-H3PEK6XH": {"file": "chunk-H3PEK6XH.js"}, "chunk-V5LT2MCF": {"file": "chunk-V5LT2MCF.js"}, "chunk-DZUOJV22": {"file": "chunk-DZUOJV22.js"}, "chunk-GHX6QOSA": {"file": "chunk-GHX6QOSA.js"}, "chunk-2GTGKKMZ": {"file": "chunk-2GTGKKMZ.js"}}}