{"hash": "77480c31", "browserHash": "2c1be90f", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "cac9de5b", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "c40b50ec", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "cc2e4d1f", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "6ffa606b", "needsInterop": true}, "@copilotkit/react-core": {"src": "../../@copilotkit/react-core/dist/index.mjs", "file": "@copilotkit_react-core.js", "fileHash": "cc5bf70c", "needsInterop": false}, "@hookform/resolvers/zod": {"src": "../../@hookform/resolvers/zod/dist/zod.mjs", "file": "@hookform_resolvers_zod.js", "fileHash": "b30b963d", "needsInterop": false}, "@radix-ui/react-alert-dialog": {"src": "../../@radix-ui/react-alert-dialog/dist/index.mjs", "file": "@radix-ui_react-alert-dialog.js", "fileHash": "2449cca5", "needsInterop": false}, "@radix-ui/react-avatar": {"src": "../../@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "da27b927", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "69c08752", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "b84ba6b8", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "ba3a5b1b", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "7cc55f30", "needsInterop": false}, "@radix-ui/react-progress": {"src": "../../@radix-ui/react-progress/dist/index.mjs", "file": "@radix-ui_react-progress.js", "fileHash": "cd95be93", "needsInterop": false}, "@radix-ui/react-radio-group": {"src": "../../@radix-ui/react-radio-group/dist/index.mjs", "file": "@radix-ui_react-radio-group.js", "fileHash": "2214cd66", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "23e92896", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "49e28460", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "451696ed", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "a1e9a6f9", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "ebec5849", "needsInterop": false}, "@supabase/supabase-js": {"src": "../../@supabase/supabase-js/dist/module/index.js", "file": "@supabase_supabase-js.js", "fileHash": "0b305db3", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "651d9b03", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "aff73d03", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "bc693012", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "6d39b2d3", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "8cee5a88", "needsInterop": true}, "react-hook-form": {"src": "../../react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "87878b13", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "4b84058e", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "4ad7aae6", "needsInterop": false}, "zod": {"src": "../../zod/dist/esm/index.js", "file": "zod.js", "fileHash": "8f53e25b", "needsInterop": false}}, "chunks": {"lib-EZJSIVHL": {"file": "lib-EZJSIVHL.js"}, "browser-7ILX4ERV": {"file": "browser-7ILX4ERV.js"}, "browser-FYKLWRQN": {"file": "browser-FYKLWRQN.js"}, "chunk-FMEW3YNC": {"file": "chunk-FMEW3YNC.js"}, "chunk-AJTCXCUR": {"file": "chunk-AJTCXCUR.js"}, "chunk-X2CLXSXA": {"file": "chunk-X2CLXSXA.js"}, "chunk-V665XK4Y": {"file": "chunk-V665XK4Y.js"}, "chunk-3KAGYO6E": {"file": "chunk-3KAGYO6E.js"}, "chunk-SBRM6RS4": {"file": "chunk-SBRM6RS4.js"}, "chunk-JOZ6ASKG": {"file": "chunk-JOZ6ASKG.js"}, "chunk-A7E35SHE": {"file": "chunk-A7E35SHE.js"}, "chunk-JBVW7BNC": {"file": "chunk-JBVW7BNC.js"}, "chunk-SURRYCUG": {"file": "chunk-SURRYCUG.js"}, "chunk-AECO5ISZ": {"file": "chunk-AECO5ISZ.js"}, "chunk-CMRLIO42": {"file": "chunk-CMRLIO42.js"}, "chunk-FLIDSUMP": {"file": "chunk-FLIDSUMP.js"}, "chunk-6VEFFVLK": {"file": "chunk-6VEFFVLK.js"}, "chunk-73XT7QZM": {"file": "chunk-73XT7QZM.js"}, "chunk-EVMCGSRL": {"file": "chunk-EVMCGSRL.js"}, "chunk-6ORVEWJI": {"file": "chunk-6ORVEWJI.js"}, "chunk-GE7ADNQB": {"file": "chunk-GE7ADNQB.js"}, "chunk-MYWBKV7L": {"file": "chunk-MYWBKV7L.js"}, "chunk-GUJ5INIC": {"file": "chunk-GUJ5INIC.js"}, "chunk-SBIIHPLX": {"file": "chunk-SBIIHPLX.js"}, "chunk-6XNNJITF": {"file": "chunk-6XNNJITF.js"}, "chunk-H3PEK6XH": {"file": "chunk-H3PEK6XH.js"}, "chunk-KYQVGTGN": {"file": "chunk-KYQVGTGN.js"}, "chunk-V5LT2MCF": {"file": "chunk-V5LT2MCF.js"}, "chunk-DZUOJV22": {"file": "chunk-DZUOJV22.js"}, "chunk-GHX6QOSA": {"file": "chunk-GHX6QOSA.js"}, "chunk-2GTGKKMZ": {"file": "chunk-2GTGKKMZ.js"}}}