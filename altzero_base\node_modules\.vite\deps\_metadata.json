{"hash": "77480c31", "browserHash": "2c1be90f", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "c5382120", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "49a927f6", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "9f5c0d80", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "762592f7", "needsInterop": true}, "@copilotkit/react-core": {"src": "../../@copilotkit/react-core/dist/index.mjs", "file": "@copilotkit_react-core.js", "fileHash": "a498d480", "needsInterop": false}, "@hookform/resolvers/zod": {"src": "../../@hookform/resolvers/zod/dist/zod.mjs", "file": "@hookform_resolvers_zod.js", "fileHash": "899d603d", "needsInterop": false}, "@radix-ui/react-alert-dialog": {"src": "../../@radix-ui/react-alert-dialog/dist/index.mjs", "file": "@radix-ui_react-alert-dialog.js", "fileHash": "fd1903cf", "needsInterop": false}, "@radix-ui/react-avatar": {"src": "../../@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "fd1f7f2e", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "b5406684", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "982e0f6f", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "61c4ccc2", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "4c9131ac", "needsInterop": false}, "@radix-ui/react-progress": {"src": "../../@radix-ui/react-progress/dist/index.mjs", "file": "@radix-ui_react-progress.js", "fileHash": "06167348", "needsInterop": false}, "@radix-ui/react-radio-group": {"src": "../../@radix-ui/react-radio-group/dist/index.mjs", "file": "@radix-ui_react-radio-group.js", "fileHash": "f44d1f2a", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "1f5ab539", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "8a4f67d0", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "ac35ae86", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "c48fa22c", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "feb3bb78", "needsInterop": false}, "@supabase/supabase-js": {"src": "../../@supabase/supabase-js/dist/module/index.js", "file": "@supabase_supabase-js.js", "fileHash": "26b276f1", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "e033c917", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "7faf4835", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "6c361d29", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "a2eb29e6", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "2d719cb2", "needsInterop": true}, "react-hook-form": {"src": "../../react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "e487b5bd", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "a9d75f13", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "970bcdb7", "needsInterop": false}, "zod": {"src": "../../zod/dist/esm/index.js", "file": "zod.js", "fileHash": "6f7d5e06", "needsInterop": false}}, "chunks": {"lib-EZJSIVHL": {"file": "lib-EZJSIVHL.js"}, "browser-7ILX4ERV": {"file": "browser-7ILX4ERV.js"}, "browser-FYKLWRQN": {"file": "browser-FYKLWRQN.js"}, "chunk-FMEW3YNC": {"file": "chunk-FMEW3YNC.js"}, "chunk-3KAGYO6E": {"file": "chunk-3KAGYO6E.js"}, "chunk-AJTCXCUR": {"file": "chunk-AJTCXCUR.js"}, "chunk-SBRM6RS4": {"file": "chunk-SBRM6RS4.js"}, "chunk-JOZ6ASKG": {"file": "chunk-JOZ6ASKG.js"}, "chunk-X2CLXSXA": {"file": "chunk-X2CLXSXA.js"}, "chunk-A7E35SHE": {"file": "chunk-A7E35SHE.js"}, "chunk-JBVW7BNC": {"file": "chunk-JBVW7BNC.js"}, "chunk-SURRYCUG": {"file": "chunk-SURRYCUG.js"}, "chunk-V665XK4Y": {"file": "chunk-V665XK4Y.js"}, "chunk-CMRLIO42": {"file": "chunk-CMRLIO42.js"}, "chunk-AECO5ISZ": {"file": "chunk-AECO5ISZ.js"}, "chunk-OUZQOUYE": {"file": "chunk-OUZQOUYE.js"}, "chunk-73XT7QZM": {"file": "chunk-73XT7QZM.js"}, "chunk-EVMCGSRL": {"file": "chunk-EVMCGSRL.js"}, "chunk-6ORVEWJI": {"file": "chunk-6ORVEWJI.js"}, "chunk-6VEFFVLK": {"file": "chunk-6VEFFVLK.js"}, "chunk-GE7ADNQB": {"file": "chunk-GE7ADNQB.js"}, "chunk-MYWBKV7L": {"file": "chunk-MYWBKV7L.js"}, "chunk-GUJ5INIC": {"file": "chunk-GUJ5INIC.js"}, "chunk-SBIIHPLX": {"file": "chunk-SBIIHPLX.js"}, "chunk-6XNNJITF": {"file": "chunk-6XNNJITF.js"}, "chunk-H3PEK6XH": {"file": "chunk-H3PEK6XH.js"}, "chunk-KYQVGTGN": {"file": "chunk-KYQVGTGN.js"}, "chunk-V5LT2MCF": {"file": "chunk-V5LT2MCF.js"}, "chunk-DZUOJV22": {"file": "chunk-DZUOJV22.js"}, "chunk-GHX6QOSA": {"file": "chunk-GHX6QOSA.js"}, "chunk-2GTGKKMZ": {"file": "chunk-2GTGKKMZ.js"}}}