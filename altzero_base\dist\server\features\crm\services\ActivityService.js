"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityService = void 0;
const supabase_1 = require("../../../base/common/apps/supabase");
class ActivityService {
    /**
     * Get user's organization IDs from organisation_members table
     */
    async getUserOrganisationIds(userId) {
        const { data, error } = await supabase_1.supabase
            .from('organisation_members')
            .select('organisation_id')
            .eq('user_id', userId);
        if (error) {
            console.error('Error fetching user organizations:', error);
            return [];
        }
        return data.map(row => row.organisation_id);
    }
    /**
     * Get activities with pagination and filtering
     */
    async getActivities(filters) {
        try {
            if (!filters.userId) {
                throw new Error('User ID is required');
            }
            const organisationIds = await this.getUserOrganisationIds(filters.userId);
            if (organisationIds.length === 0) {
                return { data: [], total: 0, page: filters.page, limit: filters.limit };
            }
            let query = supabase_1.supabase
                .from('crm_activities')
                .select(`
          *,
          crm_contacts!crm_activities_contact_id_fkey (
            id,
            full_name,
            email
          ),
          crm_opportunities!crm_activities_opportunity_id_fkey (
            id,
            title,
            value
          )
        `, { count: 'exact' })
                .in('organisation_id', organisationIds);
            // Apply type filter
            if (filters.type) {
                query = query.eq('type', filters.type);
            }
            // Apply contact filter
            if (filters.contactId) {
                query = query.eq('contact_id', filters.contactId);
            }
            // Apply opportunity filter
            if (filters.opportunityId) {
                query = query.eq('opportunity_id', filters.opportunityId);
            }
            // Apply pagination
            const offset = (filters.page - 1) * filters.limit;
            query = query
                .order('created_at', { ascending: false })
                .range(offset, offset + filters.limit - 1);
            const { data, error, count } = await query;
            if (error) {
                console.error('Error fetching activities:', error);
                throw error;
            }
            return {
                data: data || [],
                total: count || 0,
                page: filters.page,
                limit: filters.limit
            };
        }
        catch (error) {
            console.error('ActivityService.getActivities error:', error);
            throw error;
        }
    }
    /**
     * Get a single activity by ID
     */
    async getActivityById(activityId, userId) {
        try {
            if (!userId) {
                throw new Error('User ID is required');
            }
            const organisationIds = await this.getUserOrganisationIds(userId);
            if (organisationIds.length === 0) {
                return null;
            }
            const { data, error } = await supabase_1.supabase
                .from('crm_activities')
                .select(`
          *,
          crm_contacts!crm_activities_contact_id_fkey (
            id,
            full_name,
            email
          ),
          crm_opportunities!crm_activities_opportunity_id_fkey (
            id,
            title,
            value
          )
        `)
                .eq('id', activityId)
                .in('organisation_id', organisationIds)
                .single();
            if (error) {
                if (error.code === 'PGRST116') {
                    return null; // Not found
                }
                console.error('Error fetching activity:', error);
                throw error;
            }
            return data;
        }
        catch (error) {
            console.error('ActivityService.getActivityById error:', error);
            throw error;
        }
    }
    /**
     * Create a new activity
     */
    async createActivity(activityData, userId) {
        try {
            if (!userId) {
                throw new Error('User ID is required');
            }
            const organisationIds = await this.getUserOrganisationIds(userId);
            if (organisationIds.length === 0) {
                throw new Error('User is not a member of any organization');
            }
            // Use the first organization if not specified
            if (!activityData.organisation_id) {
                activityData.organisation_id = organisationIds[0];
            }
            // Verify user has access to the specified organization
            if (!organisationIds.includes(activityData.organisation_id)) {
                throw new Error('User does not have access to the specified organization');
            }
            // Set created_by to current user if not specified
            if (!activityData.created_by) {
                activityData.created_by = userId;
            }
            const { data, error } = await supabase_1.supabase
                .from('crm_activities')
                .insert([activityData])
                .select()
                .single();
            if (error) {
                console.error('Error creating activity:', error);
                throw error;
            }
            return data;
        }
        catch (error) {
            console.error('ActivityService.createActivity error:', error);
            throw error;
        }
    }
    /**
     * Update an existing activity
     */
    async updateActivity(activityId, activityData, userId) {
        try {
            if (!userId) {
                throw new Error('User ID is required');
            }
            const organisationIds = await this.getUserOrganisationIds(userId);
            if (organisationIds.length === 0) {
                return null;
            }
            // Remove fields that shouldn't be updated
            const { id, created_at, ...updateData } = activityData;
            const { data, error } = await supabase_1.supabase
                .from('crm_activities')
                .update(updateData)
                .eq('id', activityId)
                .in('organisation_id', organisationIds)
                .select()
                .single();
            if (error) {
                if (error.code === 'PGRST116') {
                    return null; // Not found
                }
                console.error('Error updating activity:', error);
                throw error;
            }
            return data;
        }
        catch (error) {
            console.error('ActivityService.updateActivity error:', error);
            throw error;
        }
    }
    /**
     * Delete an activity
     */
    async deleteActivity(activityId, userId) {
        try {
            if (!userId) {
                throw new Error('User ID is required');
            }
            const organisationIds = await this.getUserOrganisationIds(userId);
            if (organisationIds.length === 0) {
                return false;
            }
            const { error } = await supabase_1.supabase
                .from('crm_activities')
                .delete()
                .eq('id', activityId)
                .in('organisation_id', organisationIds);
            if (error) {
                console.error('Error deleting activity:', error);
                throw error;
            }
            return true;
        }
        catch (error) {
            console.error('ActivityService.deleteActivity error:', error);
            throw error;
        }
    }
    /**
     * Get recent activities for dashboard
     */
    async getRecentActivities(userId, limit = 10) {
        try {
            if (!userId) {
                throw new Error('User ID is required');
            }
            const organisationIds = await this.getUserOrganisationIds(userId);
            if (organisationIds.length === 0) {
                return [];
            }
            const { data, error } = await supabase_1.supabase
                .from('crm_activities')
                .select(`
          *,
          crm_contacts!crm_activities_contact_id_fkey (
            id,
            full_name
          ),
          crm_opportunities!crm_activities_opportunity_id_fkey (
            id,
            title
          )
        `)
                .in('organisation_id', organisationIds)
                .order('created_at', { ascending: false })
                .limit(limit);
            if (error) {
                console.error('Error fetching recent activities:', error);
                throw error;
            }
            return data || [];
        }
        catch (error) {
            console.error('ActivityService.getRecentActivities error:', error);
            throw error;
        }
    }
    /**
     * Get activity statistics
     */
    async getActivityStats(userId) {
        try {
            if (!userId) {
                throw new Error('User ID is required');
            }
            const organisationIds = await this.getUserOrganisationIds(userId);
            if (organisationIds.length === 0) {
                return {
                    total: 0,
                    byType: {},
                    thisWeek: 0,
                    thisMonth: 0
                };
            }
            const { data, error } = await supabase_1.supabase
                .from('crm_activities')
                .select('type, created_at')
                .in('organisation_id', organisationIds);
            if (error) {
                console.error('Error fetching activity stats:', error);
                throw error;
            }
            const activities = data || [];
            const total = activities.length;
            // Group by type
            const byType = activities.reduce((acc, activity) => {
                const type = activity.type || 'unknown';
                acc[type] = (acc[type] || 0) + 1;
                return acc;
            }, {});
            // Calculate time-based stats
            const now = new Date();
            const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            const thisWeek = activities.filter(activity => new Date(activity.created_at) >= oneWeekAgo).length;
            const thisMonth = activities.filter(activity => new Date(activity.created_at) >= oneMonthAgo).length;
            return {
                total,
                byType,
                thisWeek,
                thisMonth
            };
        }
        catch (error) {
            console.error('ActivityService.getActivityStats error:', error);
            throw error;
        }
    }
}
exports.ActivityService = ActivityService;
//# sourceMappingURL=ActivityService.js.map