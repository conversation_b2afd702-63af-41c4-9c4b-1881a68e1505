// =====================================================
// AI RESPONSE GENERATION NODE - AI CHAT WORKFLOW
// =====================================================

import { BaseNode, AIResponseNodeResult } from '../types/NodeTypes';
import { WorkflowContext, ChatMessage } from '../types/WorkflowState';

export class AIResponseNode implements BaseNode {
  name = 'ai_response';
  description = 'Generates AI responses based on conversation context and workflow results';

  async execute(context: WorkflowContext): Promise<any> {
    const { state, tools, logger } = context;
    const startTime = Date.now();

    try {
      logger.info('Starting AI response generation', {
        workflow_id: state.workflow_id,
        current_intent: state.current_intent,
        messages_count: state.messages.length
      });

      // Check if response was already generated by other nodes
      if (state.ai_response?.content && state.ai_response.content.trim() !== '') {
        logger.debug('Response already generated by previous node');
        return {
          ai_response: state.ai_response,
          processing_time: state.processing_time,
          last_updated: new Date().toISOString()
        };
      }

      // Prepare conversation context
      const conversationMessages = this.prepareConversationContext(state);
      
      // Generate AI response
      const responseContent = await tools.ai.generateResponse(conversationMessages, {
        model: 'gpt-4',
        temperature: state.config.temperature,
        max_tokens: state.config.max_tokens
      });

      const executionTime = Date.now() - startTime;
      const tokensUsed = this.estimateTokens(responseContent);

      // Calculate confidence based on context and response quality
      const confidence = this.calculateResponseConfidence(state, responseContent);

      // Generate reasoning for the response
      const reasoning = this.generateResponseReasoning(state, responseContent);

      // Generate suggested follow-up actions
      const suggestedActions = this.generateSuggestedActions(state, responseContent);

      const result: AIResponseNodeResult = {
        response_generated: true,
        content: responseContent,
        confidence,
        reasoning,
        tokens_used: tokensUsed,
        processing_time: executionTime,
        model_used: 'gpt-4',
        success: true
      };

      logger.info('AI response generated successfully', {
        content_length: responseContent.length,
        confidence,
        tokens_used: tokensUsed,
        execution_time: executionTime
      });

      return {
        ai_response: {
          content: responseContent,
          confidence,
          reasoning,
          suggested_actions: suggestedActions
        },
        total_tokens_used: (state.total_tokens_used || 0) + tokensUsed,
        total_cost: this.calculateCost(tokensUsed),
        node_data: {
          ...state.node_data,
          ai_response: result
        },
        processing_time: (state.processing_time || 0) + executionTime,
        last_updated: new Date().toISOString()
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      logger.error('AI response generation failed', error);

      // Generate fallback response
      const fallbackResponse = this.generateFallbackResponse(state);

      return {
        ai_response: {
          content: fallbackResponse,
          confidence: 0.5,
          reasoning: 'Generated fallback response due to AI service error',
          suggested_actions: ['Try rephrasing your question', 'Ask me something else']
        },
        errors: [
          ...state.errors,
          {
            node_name: this.name,
            error_message: error instanceof Error ? error.message : 'Unknown AI response error',
            error_code: 'AI_RESPONSE_GENERATION_FAILED',
            timestamp: new Date().toISOString(),
            recoverable: true
          }
        ],
        processing_time: (state.processing_time || 0) + executionTime,
        last_updated: new Date().toISOString()
      };
    }
  }

  // Prepare conversation context for AI
  private prepareConversationContext(state: any): ChatMessage[] {
    const messages: ChatMessage[] = [];

    // Add system message with context
    messages.push({
      id: 'system',
      role: 'system',
      content: this.generateSystemPrompt(state),
      timestamp: new Date().toISOString()
    });

    // Add conversation history (last 10 messages to stay within token limits)
    const recentMessages = state.messages.slice(-10);
    messages.push(...recentMessages);

    return messages;
  }

  // Generate system prompt based on workflow state
  private generateSystemPrompt(state: any): string {
    let prompt = `You are AltZero AI, an intelligent assistant for the AltZero platform. You help users with navigation, data queries, and information searches.

Current Context:
- User Intent: ${state.current_intent}
- Detected Entities: ${JSON.stringify(state.detected_entities)}`;

    // Add context based on actions performed
    if (state.navigation_actions.length > 0) {
      const lastNav = state.navigation_actions[state.navigation_actions.length - 1];
      prompt += `\n- Recent Navigation: ${lastNav.success ? 'Successfully navigated to' : 'Failed to navigate to'} ${lastNav.page}`;
    }

    if (state.database_queries.length > 0) {
      const lastQuery = state.database_queries[state.database_queries.length - 1];
      prompt += `\n- Recent Database Query: ${lastQuery.success ? 'Successfully executed' : 'Failed to execute'} query for "${lastQuery.query}"`;
    }

    if (state.knowledge_searches.length > 0) {
      const lastSearch = state.knowledge_searches[state.knowledge_searches.length - 1];
      prompt += `\n- Recent Knowledge Search: ${lastSearch.success ? 'Found' : 'Failed to find'} results for "${lastSearch.query}"`;
    }

    prompt += `

Guidelines:
- Be helpful, concise, and professional
- If navigation was successful, confirm the action and offer help with the new page
- If actions failed, acknowledge the issue and suggest alternatives
- Provide specific, actionable suggestions
- Don't repeat information that's already been communicated
- Focus on the user's current needs and context`;

    return prompt;
  }

  // Calculate response confidence
  private calculateResponseConfidence(state: any, response: string): number {
    let confidence = 0.7; // Base confidence

    // Increase confidence if we have clear intent
    if (state.current_intent !== 'unknown') {
      confidence += 0.1;
    }

    // Increase confidence if we have entities
    if (Object.keys(state.detected_entities).length > 0) {
      confidence += 0.1;
    }

    // Increase confidence if actions were successful
    const recentActions = [
      ...state.navigation_actions,
      ...state.database_queries,
      ...state.knowledge_searches
    ].filter(action => action.timestamp && 
      new Date(action.timestamp).getTime() > Date.now() - 60000 // Last minute
    );

    const successfulActions = recentActions.filter(action => action.success);
    if (successfulActions.length > 0) {
      confidence += 0.1;
    }

    // Decrease confidence if there are recent errors
    const recentErrors = state.errors.filter((error: any) => 
      new Date(error.timestamp).getTime() > Date.now() - 60000
    );
    if (recentErrors.length > 0) {
      confidence -= 0.1;
    }

    // Adjust based on response length and quality
    if (response.length > 50 && response.length < 500) {
      confidence += 0.05;
    }

    return Math.max(0.1, Math.min(1.0, confidence));
  }

  // Generate reasoning for the response
  private generateResponseReasoning(state: any, response: string): string {
    const reasons: string[] = [];

    if (state.current_intent !== 'unknown') {
      reasons.push(`Addressed ${state.current_intent} intent`);
    }

    if (state.navigation_actions.length > 0) {
      const lastNav = state.navigation_actions[state.navigation_actions.length - 1];
      reasons.push(`Responded to navigation ${lastNav.success ? 'success' : 'failure'}`);
    }

    if (state.database_queries.length > 0) {
      reasons.push('Incorporated database query results');
    }

    if (state.knowledge_searches.length > 0) {
      reasons.push('Included knowledge base search results');
    }

    if (reasons.length === 0) {
      reasons.push('Provided general assistance based on conversation context');
    }

    return reasons.join(', ');
  }

  // Generate suggested follow-up actions
  private generateSuggestedActions(state: any, response: string): string[] {
    const actions: string[] = [];

    switch (state.current_intent) {
      case 'navigation':
        if (state.navigation_actions.some((nav: any) => nav.success)) {
          actions.push('Explore the current page features');
          actions.push('Ask me about other available pages');
        } else {
          actions.push('Try specifying a different page name');
          actions.push('Ask me "What pages are available?"');
        }
        break;

      case 'database_query':
        actions.push('Refine your search with more specific criteria');
        actions.push('Ask me to export the results');
        actions.push('Request additional data analysis');
        break;

      case 'knowledge_search':
        actions.push('Ask for more detailed information');
        actions.push('Search for related topics');
        actions.push('Request document summaries');
        break;

      case 'general_chat':
        actions.push('Ask me to help with navigation');
        actions.push('Request data queries or searches');
        actions.push('Get information about platform features');
        break;

      default:
        actions.push('Tell me what you\'d like to do');
        actions.push('Ask me about navigation, data, or information');
    }

    return actions;
  }

  // Generate fallback response for errors
  private generateFallbackResponse(state: any): string {
    const fallbacks = [
      "I apologize, but I'm having trouble generating a response right now. How can I help you with navigation, data queries, or information searches?",
      "I'm experiencing some technical difficulties. Please try asking me again, or let me know if you'd like help navigating to a specific page.",
      "Sorry for the inconvenience. I'm here to help with navigation, database queries, and knowledge searches. What would you like to do?",
      "I encountered an issue processing your request. Please try rephrasing your question or ask me about available features."
    ];

    // Select fallback based on intent
    if (state.current_intent === 'navigation') {
      return "I'm having trouble with navigation right now. You can try using the menu to navigate manually, or ask me again in a moment.";
    }

    return fallbacks[Math.floor(Math.random() * fallbacks.length)];
  }

  // Estimate token usage (rough approximation)
  private estimateTokens(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters
    return Math.ceil(text.length / 4);
  }

  // Calculate cost based on tokens (rough estimation)
  private calculateCost(tokens: number): number {
    // GPT-4 pricing (approximate): $0.03 per 1K tokens for input, $0.06 per 1K tokens for output
    return (tokens / 1000) * 0.06;
  }

  // Validate response quality
  private validateResponse(response: string): { valid: boolean; issues: string[] } {
    const issues: string[] = [];

    if (!response || response.trim().length === 0) {
      issues.push('Response is empty');
    }

    if (response.length < 10) {
      issues.push('Response is too short');
    }

    if (response.length > 2000) {
      issues.push('Response is too long');
    }

    // Check for common AI response issues
    if (response.includes('I cannot') && response.includes('I am not able')) {
      issues.push('Response is overly restrictive');
    }

    return {
      valid: issues.length === 0,
      issues
    };
  }
}
