"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ragService = void 0;
const openai_1 = require("@langchain/openai");
const langGraphService_1 = require("./langGraphService");
const pineconeService_1 = require("./pineconeService");
class RAGService {
    constructor() {
        this.llm = null;
        this.isEnabled = false;
        if (!process.env.OPENAI_API_KEY) {
            console.warn("OPENAI_API_KEY not found. RAG service will be disabled.");
            this.isEnabled = false;
        }
        else {
            try {
                this.llm = new openai_1.ChatOpenAI({
                    openAIApiKey: process.env.OPENAI_API_KEY,
                    modelName: process.env.OPENAI_MODEL || "gpt-4",
                    temperature: 0.7,
                    maxTokens: 2048,
                });
                this.isEnabled = true;
            }
            catch (error) {
                console.error("Failed to initialize RAG service:", error);
                this.isEnabled = false;
            }
        }
    }
    async generateResponse(request) {
        if (!this.isEnabled) {
            throw new Error("RAG service not available - OpenAI API key not configured");
        }
        const startTime = Date.now();
        try {
            console.log(`Generating RAG response for user: ${request.userId || "anonymous"}`);
            // Use LangGraph for RAG processing with user filter
            const ragResponse = await langGraphService_1.langGraphService.performRAG({
                query: request.message,
                selectedDocuments: request.selectedDocuments,
                systemMessage: request.systemMessage ||
                    "You are a helpful AI assistant with access to a comprehensive knowledge base.",
                temperature: 0.7,
                maxTokens: 2048,
                userFilter: request.userId ? { userId: request.userId } : undefined,
            });
            const processingTime = Date.now() - startTime;
            return {
                message: ragResponse.answer,
                sources: ragResponse.sources,
                metadata: {
                    sessionId: request.sessionId,
                    processingTime,
                    model: this.llm?.modelName || "gpt-4",
                    userId: request.userId,
                },
            };
        }
        catch (error) {
            console.error("Error generating RAG response:", error);
            throw error;
        }
    }
    async searchDocuments(query, userId, maxResults = 10) {
        try {
            console.log(`Searching documents for user: ${userId || "anonymous"}`);
            // Use LangGraph search with user filter
            const results = await langGraphService_1.langGraphService.searchDocuments(query, {
                topK: maxResults,
                minScore: 0.5,
                filter: userId ? { userId } : undefined,
            });
            return results.map((result) => ({
                id: result.id,
                text: result.text,
                score: result.score,
                metadata: result.metadata,
            }));
        }
        catch (error) {
            console.error("Error searching documents:", error);
            return [];
        }
    }
    async healthCheck() {
        if (!this.isEnabled) {
            return false;
        }
        try {
            // Test LangGraph and Pinecone connectivity
            const langGraphHealthy = await langGraphService_1.langGraphService.healthCheck();
            const pineconeHealthy = await pineconeService_1.pineconeService.healthCheck();
            return langGraphHealthy && pineconeHealthy;
        }
        catch (error) {
            console.error("RAG service health check failed:", error);
            return false;
        }
    }
    // Getter to check if RAG service is enabled
    get enabled() {
        return this.isEnabled;
    }
}
exports.ragService = new RAGService();
//# sourceMappingURL=ragService.js.map