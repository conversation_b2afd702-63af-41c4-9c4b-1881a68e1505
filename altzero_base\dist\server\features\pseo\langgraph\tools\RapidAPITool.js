"use strict";
// =====================================================
// RAPIDAPI TOOL FOR LANGGRAPH WORKFLOWS
// =====================================================
Object.defineProperty(exports, "__esModule", { value: true });
exports.RapidAPITool = void 0;
const RapidAPIGateway_1 = require("./RapidAPIGateway");
class RapidAPITool {
    constructor(config) {
        this.name = 'rapidapi_keyword_research';
        this.description = 'Research keywords using the configured SEO API through RapidAPI gateway (Ubersuggest, Semrush, Ahrefs, or Moz)';
        this.config = config;
        this.gateway = new RapidAPIGateway_1.RapidAPIGateway(config);
    }
    async _call(input) {
        try {
            const { keywords, options } = JSON.parse(input);
            if (!Array.isArray(keywords)) {
                throw new Error('Keywords must be an array');
            }
            const results = await this.getKeywordData(keywords, options);
            return JSON.stringify(results);
        }
        catch (error) {
            console.error('RapidAPI tool error:', error);
            throw error;
        }
    }
    // Get keyword data using RapidAPI gateway
    async getKeywordData(keywords, options = {}) {
        if (!this.config.enabled || !this.config.apiKey) {
            throw new Error('RapidAPI not configured');
        }
        try {
            const results = await this.gateway.getKeywordData(keywords, {
                max_results: options.max_results || 50,
                country: options.country || 'US',
                include_related: options.include_related !== false
            });
            // Add metadata about data source used
            const activeProvider = this.gateway.getAvailableProviders()[0];
            console.log(`✅ RapidAPI: Retrieved ${results.length} keywords from ${activeProvider}`);
            return results;
        }
        catch (error) {
            console.warn('RapidAPI gateway failed, using fallback data:', error);
            // Return fallback data to prevent workflow failure
            return this.generateFallbackData(keywords);
        }
    }
    // Get domain analysis data using SimilarWeb via RapidAPI
    async getDomainData(domain, options = {}) {
        if (!this.config.enabled || !this.config.apiKey) {
            throw new Error('RapidAPI not configured');
        }
        try {
            const result = await this.gateway.getDomainData(domain, options);
            console.log(`✅ RapidAPI: Retrieved domain data for ${domain} from SimilarWeb`);
            return result;
        }
        catch (error) {
            console.warn('RapidAPI SimilarWeb failed:', error);
            throw error; // Don't provide fallback for domain data
        }
    }
    // Generate fallback data when APIs are unavailable
    generateFallbackData(keywords) {
        return keywords.map(keyword => ({
            keyword,
            search_volume: Math.floor(Math.random() * 5000) + 100,
            keyword_difficulty: Math.floor(Math.random() * 80) + 20,
            cpc: Math.random() * 3 + 0.5,
            competition: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)],
            intent: this.inferKeywordIntent(keyword),
            trend: ['rising', 'stable', 'declining'][Math.floor(Math.random() * 3)],
            data_source: 'rapidapi_fallback'
        }));
    }
    // Infer keyword intent
    inferKeywordIntent(keyword) {
        const keywordLower = keyword.toLowerCase();
        if (keywordLower.includes('buy') || keywordLower.includes('price') || keywordLower.includes('cost') || keywordLower.includes('purchase')) {
            return 'transactional';
        }
        if (keywordLower.includes('review') || keywordLower.includes('vs') || keywordLower.includes('best') || keywordLower.includes('compare')) {
            return 'commercial';
        }
        if (keywordLower.includes('how') || keywordLower.includes('what') || keywordLower.includes('guide') || keywordLower.includes('tutorial')) {
            return 'informational';
        }
        return 'informational';
    }
    // Check if RapidAPI is available
    async healthCheck() {
        try {
            if (!this.config.enabled || !this.config.apiKey) {
                return false;
            }
            const healthResults = await this.gateway.healthCheck();
            const workingProviders = healthResults.filter(r => r.status).length;
            console.log(`RapidAPI Health Check: ${workingProviders}/${healthResults.length} providers working`);
            return workingProviders > 0;
        }
        catch (error) {
            console.warn('RapidAPI health check failed:', error);
            return false;
        }
    }
    // Get usage statistics
    async getUsageStats() {
        try {
            const healthResults = await this.gateway.healthCheck();
            const availableProviders = this.gateway.getAvailableProviders();
            return {
                available_providers: availableProviders,
                provider_status: healthResults,
                total_providers: healthResults.length,
                working_providers: healthResults.filter(r => r.status).length
            };
        }
        catch (error) {
            console.warn('Failed to get RapidAPI usage stats:', error);
            return {
                available_providers: [],
                provider_status: [],
                total_providers: 0,
                working_providers: 0
            };
        }
    }
    // Get available providers
    getAvailableProviders() {
        return this.gateway.getAvailableProviders();
    }
    // Test specific provider
    async testProvider(providerName, testKeyword = 'digital marketing') {
        try {
            const results = await this.gateway.getKeywordData([testKeyword], {
                max_results: 1,
                provider: providerName
            });
            return results.length > 0 && results[0].data_source.includes(providerName);
        }
        catch (error) {
            console.warn(`Provider ${providerName} test failed:`, error);
            return false;
        }
    }
    // Get provider-specific data
    async getProviderData(providerName, keywords) {
        try {
            // This would require extending the gateway to support provider-specific requests
            const results = await this.gateway.getKeywordData(keywords, {
                preferred_provider: providerName
            });
            return results.filter(r => r.data_source.includes(providerName));
        }
        catch (error) {
            console.warn(`Failed to get data from ${providerName}:`, error);
            return [];
        }
    }
}
exports.RapidAPITool = RapidAPITool;
//# sourceMappingURL=RapidAPITool.js.map