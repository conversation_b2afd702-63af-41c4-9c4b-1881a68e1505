{"version": 3, "file": "ragService.js", "sourceRoot": "", "sources": ["../../../../../server/features/knowledge/services/ragService.ts"], "names": [], "mappings": ";;;AAAA,8CAA+C;AAG/C,yDAAsD;AACtD,uDAAoD;AA2BpD,MAAM,UAAU;IAId;QAHQ,QAAG,GAAsB,IAAI,CAAC;QAC9B,cAAS,GAAY,KAAK,CAAC;QAGjC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;YACxE,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACzB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC;gBACH,IAAI,CAAC,GAAG,GAAG,IAAI,mBAAU,CAAC;oBACxB,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc;oBACxC,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,OAAO;oBAC9C,WAAW,EAAE,GAAG;oBAChB,SAAS,EAAE,IAAI;iBAChB,CAAC,CAAC;gBACH,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACxB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;gBAC1D,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YACzB,CAAC;QACH,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,OAAoB;QACzC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CACb,2DAA2D,CAC5D,CAAC;QACJ,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CACT,qCAAqC,OAAO,CAAC,MAAM,IAAI,WAAW,EAAE,CACrE,CAAC;YAEF,oDAAoD;YACpD,MAAM,WAAW,GAAG,MAAM,mCAAgB,CAAC,UAAU,CAAC;gBACpD,KAAK,EAAE,OAAO,CAAC,OAAO;gBACtB,iBAAiB,EAAE,OAAO,CAAC,iBAAiB;gBAC5C,aAAa,EACX,OAAO,CAAC,aAAa;oBACrB,+EAA+E;gBACjF,WAAW,EAAE,GAAG;gBAChB,SAAS,EAAE,IAAI;gBACf,UAAU,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,SAAS;aACpE,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE9C,OAAO;gBACL,OAAO,EAAE,WAAW,CAAC,MAAM;gBAC3B,OAAO,EAAE,WAAW,CAAC,OAAO;gBAC5B,QAAQ,EAAE;oBACR,SAAS,EAAE,OAAO,CAAC,SAAS;oBAC5B,cAAc;oBACd,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,SAAS,IAAI,OAAO;oBACrC,MAAM,EAAE,OAAO,CAAC,MAAM;iBACvB;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CACnB,KAAa,EACb,MAAe,EACf,aAAqB,EAAE;QASvB,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,iCAAiC,MAAM,IAAI,WAAW,EAAE,CAAC,CAAC;YAEtE,wCAAwC;YACxC,MAAM,OAAO,GAAG,MAAM,mCAAgB,CAAC,eAAe,CAAC,KAAK,EAAE;gBAC5D,IAAI,EAAE,UAAU;gBAChB,QAAQ,EAAE,GAAG;gBACb,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,SAAS;aACxC,CAAC,CAAC;YAEH,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;gBAC9B,EAAE,EAAE,MAAM,CAAC,EAAE;gBACb,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,QAAQ,EAAE,MAAM,CAAC,QAAQ;aAC1B,CAAC,CAAC,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW;QACf,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC;YACH,2CAA2C;YAC3C,MAAM,gBAAgB,GAAG,MAAM,mCAAgB,CAAC,WAAW,EAAE,CAAC;YAC9D,MAAM,eAAe,GAAG,MAAM,iCAAe,CAAC,WAAW,EAAE,CAAC;YAE5D,OAAO,gBAAgB,IAAI,eAAe,CAAC;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,4CAA4C;IAC5C,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;CACF;AAEY,QAAA,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC"}