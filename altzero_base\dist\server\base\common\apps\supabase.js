"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.supabase = void 0;
const supabase_js_1 = require("@supabase/supabase-js");
// Get Supabase URL and key from environment variables
const supabaseUrl = process.env.SUPABASE_URL || "";
// Prefer service role key for backend operations (bypasses RLS), fallback to anon key
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE || process.env.SUPABASE_ANON_KEY || "";
if (!supabaseUrl || !supabaseKey) {
    console.error("Missing Supabase credentials in environment variables");
}
// Create and export the Supabase client (uses service role for backend if available)
exports.supabase = (0, supabase_js_1.createClient)(supabaseUrl, supabaseKey);
//# sourceMappingURL=supabase.js.map