"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.llamaCloudService = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const node_fetch_1 = __importDefault(require("node-fetch"));
const llamaindex_1 = require("llamaindex");
const form_data_1 = __importDefault(require("form-data"));
class LlamaCloudService {
    constructor() {
        this.index = null;
        this.retriever = null;
        this.chatEngine = null;
        this.documents = new Map(); // In-memory storage for demo
        this.apiKey = process.env.LLAMA_CLOUD_API_KEY || "";
        this.baseUrl =
            process.env.LLAMA_CLOUD_BASE_URL || "https://api.cloud.llamaindex.ai";
        this.endpointUrl =
            "https://api.cloud.llamaindex.ai/api/v1/pipelines/af5532fa-ba0d-48c2-99aa-124fd6c6f42f/retrieve";
        if (!this.apiKey) {
            console.warn("LLAMA_CLOUD_API_KEY not found. LlamaCloud features will be disabled.");
        }
        else {
            this.initializeLlamaCloud();
        }
        // Initialize with some demo documents if none exist
        this.initializeDemoDocuments();
    }
    initializeDemoDocuments() {
        if (this.documents.size === 0) {
            // Add some demo documents for testing
            const demoDocuments = [
                {
                    id: "demo-doc-1",
                    name: "Getting Started Guide.pdf",
                    type: "application/pdf",
                    size: 1024 * 500, // 500KB
                    status: "success",
                    uploadedAt: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
                    userId: "anonymous",
                    llamaCloudId: "llamacloud-demo-1",
                    metadata: {
                        pageCount: 15,
                        wordCount: 3500,
                        language: "en",
                        title: "Getting Started Guide",
                    },
                },
                {
                    id: "demo-doc-2",
                    name: "API Documentation.md",
                    type: "text/markdown",
                    size: 1024 * 200, // 200KB
                    status: "success",
                    uploadedAt: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
                    userId: "anonymous",
                    llamaCloudId: "llamacloud-demo-2",
                    metadata: {
                        pageCount: 8,
                        wordCount: 2100,
                        language: "en",
                        title: "API Documentation",
                    },
                },
                {
                    id: "demo-doc-3",
                    name: "User Manual.docx",
                    type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    size: 1024 * 800, // 800KB
                    status: "processing",
                    uploadedAt: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
                    userId: "anonymous",
                    llamaCloudId: "llamacloud-demo-3",
                    metadata: {
                        pageCount: 25,
                        wordCount: 5200,
                        language: "en",
                        title: "User Manual",
                    },
                },
                {
                    id: "demo-doc-4",
                    name: "Technical Specifications.txt",
                    type: "text/plain",
                    size: 1024 * 150, // 150KB
                    status: "success",
                    uploadedAt: new Date(Date.now() - 259200000).toISOString(), // 3 days ago
                    userId: "anonymous",
                    llamaCloudId: "llamacloud-demo-4",
                    metadata: {
                        pageCount: 5,
                        wordCount: 1800,
                        language: "en",
                        title: "Technical Specifications",
                    },
                },
                {
                    id: "demo-doc-5",
                    name: "Error Log Analysis.pdf",
                    type: "application/pdf",
                    size: 1024 * 300, // 300KB
                    status: "error",
                    uploadedAt: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
                    userId: "anonymous",
                    error: "Failed to parse document: Corrupted PDF file",
                },
            ];
            demoDocuments.forEach((doc) => {
                this.documents.set(doc.id, doc);
            });
            console.log(`Initialized ${demoDocuments.length} demo documents`);
        }
    }
    async initializeLlamaCloud() {
        try {
            // Initialize LlamaCloud index with your specific configuration
            this.index = new llamaindex_1.LlamaCloudIndex({
                name: "witty-minnow-2025-05-27",
                projectName: "Default",
                apiKey: this.apiKey,
            });
            // Create retriever with your specified settings
            this.retriever = this.index.asRetriever({
                similarityTopK: 5,
            });
            // Create chat engine for conversational queries
            this.chatEngine = new llamaindex_1.ContextChatEngine({ retriever: this.retriever });
            console.log("LlamaCloud service initialized successfully");
        }
        catch (error) {
            console.error("Failed to initialize LlamaCloud:", error);
            this.index = null;
            this.retriever = null;
            this.chatEngine = null;
        }
    }
    async parseDocument(filePath, originalName) {
        try {
            console.log(`🚀 Starting LlamaCloud parsing for: ${originalName}`);
            if (!this.apiKey) {
                throw new Error("LlamaCloud API key not configured");
            }
            // Read file stats
            const fileStats = fs_1.default.statSync(filePath);
            const fileSizeMB = fileStats.size / (1024 * 1024);
            console.log(`📊 File size: ${fileSizeMB.toFixed(2)}MB`);
            // Step 1: Upload file to LlamaCloud for parsing
            console.log(`📤 Uploading to LlamaCloud: ${originalName}`);
            const form = new form_data_1.default();
            form.append("file", fs_1.default.createReadStream(filePath), {
                filename: originalName,
                contentType: this.getMimeType(originalName),
            });
            const uploadUrl = `${this.baseUrl}/api/v1/parsing/upload`;
            const uploadResponse = await (0, node_fetch_1.default)(uploadUrl, {
                method: "POST",
                headers: {
                    Authorization: `Bearer ${this.apiKey}`,
                    accept: "application/json",
                    ...form.getHeaders(),
                },
                body: form,
            });
            if (!uploadResponse.ok) {
                const errorText = await uploadResponse.text();
                throw new Error(`LlamaCloud upload failed: ${uploadResponse.status} ${uploadResponse.statusText} - ${errorText}`);
            }
            const uploadResult = await uploadResponse.json();
            const jobId = uploadResult.id;
            console.log(`✅ Upload successful, job ID: ${jobId}`);
            console.log(`⏳ Waiting for parsing to complete...`);
            // Step 2: Wait for parsing to complete
            await this.waitForParsingCompletion(jobId);
            // Step 3: Get the parsed content
            console.log(`📥 Retrieving parsed content for job: ${jobId}`);
            const resultUrl = `${this.baseUrl}/api/v1/parsing/job/${jobId}/result/markdown`;
            const resultResponse = await (0, node_fetch_1.default)(resultUrl, {
                method: "GET",
                headers: {
                    Authorization: `Bearer ${this.apiKey}`,
                    accept: "application/json",
                },
            });
            if (!resultResponse.ok) {
                const errorText = await resultResponse.text();
                throw new Error(`Failed to get parsing result: ${resultResponse.status} ${resultResponse.statusText} - ${errorText}`);
            }
            const resultData = await resultResponse.json();
            console.log(`🔧 DEBUG: LlamaCloud result received`);
            console.log(`🔧 DEBUG: Result data keys:`, Object.keys(resultData));
            console.log(`🔧 DEBUG: Has markdown:`, !!resultData.markdown);
            console.log(`🔧 DEBUG: Has text:`, !!resultData.text);
            console.log(`🔧 DEBUG: Markdown length:`, resultData.markdown?.length || 0);
            const content = resultData.markdown || resultData.text || "";
            console.log(`📝 Extracted content length: ${content.length} characters`);
            console.log(`📝 Content preview: "${content.substring(0, 200)}..."`);
            if (!content || content.length === 0) {
                console.error(`❌ No content extracted! Result data keys:`, Object.keys(resultData));
                throw new Error("No content extracted from document");
            }
            console.log(`🔄 Creating chunks from content...`);
            // Step 4: Create chunks from the content
            const chunks = this.createChunks(content, originalName);
            console.log(`✅ Created ${chunks.length} chunks`);
            console.log(`🔄 Building parsed document object...`);
            const parsedDocument = {
                id: `parsed-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                content,
                metadata: {
                    fileName: originalName,
                    fileType: path_1.default.extname(originalName),
                    fileSize: fileStats.size,
                    pageCount: this.estimatePageCount(content),
                    parsedAt: new Date().toISOString(),
                },
                chunks,
            };
            console.log(`✅ Successfully parsed document: ${originalName}`);
            console.log(`📊 Generated ${chunks.length} chunks`);
            console.log(`🚀 Returning parsed document...`);
            return parsedDocument;
        }
        catch (error) {
            console.error(`❌ Error parsing document ${originalName}:`, error);
            throw new Error(`Failed to parse document with LlamaCloud: ${error instanceof Error ? error.message : "Unknown error"}`);
        }
    }
    async extractTextContent(filePath, fileName) {
        try {
            // Check file size first to prevent memory issues
            const fileStats = fs_1.default.statSync(filePath);
            const fileSizeMB = fileStats.size / (1024 * 1024);
            if (fileSizeMB > 5) {
                // Very conservative limit
                console.log(`File ${fileName} is ${fileSizeMB.toFixed(2)}MB, returning placeholder to prevent memory issues`);
                return `Large file: ${fileName} (${fileSizeMB.toFixed(2)}MB)\nContent not extracted to prevent memory overflow.\nFile will be processed by LlamaCloud directly.`;
            }
            const ext = path_1.default.extname(fileName).toLowerCase();
            if (ext === ".txt" || ext === ".md") {
                // Only read small text files
                const content = fs_1.default.readFileSync(filePath, "utf-8");
                if (content.length > 100000) {
                    // 100KB limit
                    return (content.substring(0, 100000) +
                        "\n\n[Content truncated to prevent memory issues]");
                }
                return content;
            }
            else {
                // For other file types, return a safe placeholder
                return `Document: ${fileName}\nFile type: ${ext}\nSize: ${fileSizeMB.toFixed(2)}MB\nProcessed at: ${new Date().toISOString()}\n\nThis document will be processed by LlamaCloud for content extraction.`;
            }
        }
        catch (error) {
            console.error(`Error extracting content from ${fileName}:`, error);
            return `Error processing ${fileName}: ${error instanceof Error ? error.message : "Unknown error"}`;
        }
    }
    createChunks(content, fileName) {
        console.log(`🔄 Starting createChunks for ${fileName}, content length: ${content.length}`);
        const chunkSize = parseInt(process.env.CHUNK_SIZE || "1000");
        const chunkOverlap = parseInt(process.env.CHUNK_OVERLAP || "200");
        console.log(`📋 Chunk settings: size=${chunkSize}, overlap=${chunkOverlap}`);
        const chunks = [];
        let startIndex = 0;
        let chunkIndex = 0;
        const maxChunks = 100; // Reasonable limit for most documents
        const minChunkSize = 50; // Don't create chunks smaller than this
        while (startIndex < content.length && chunkIndex < maxChunks) {
            const remainingContent = content.length - startIndex;
            // If remaining content is very small, just create one final chunk
            if (remainingContent <= minChunkSize) {
                if (remainingContent > 0) {
                    chunks.push({
                        id: `chunk-${chunkIndex}-${Date.now()}`,
                        content: content.slice(startIndex).trim(),
                        metadata: {
                            chunkIndex,
                            startChar: startIndex,
                            endChar: content.length,
                            pageNumber: this.estimatePageNumber(startIndex, content),
                        },
                    });
                }
                break;
            }
            console.log(`🔄 Processing chunk ${chunkIndex}, startIndex: ${startIndex}, remaining: ${remainingContent}`);
            const endIndex = Math.min(startIndex + chunkSize, content.length);
            const chunkContent = content.slice(startIndex, endIndex);
            // Try to break at sentence boundaries
            let actualEndIndex = endIndex;
            if (endIndex < content.length) {
                const lastSentenceEnd = chunkContent.lastIndexOf(".");
                const lastNewline = chunkContent.lastIndexOf("\n");
                const breakPoint = Math.max(lastSentenceEnd, lastNewline);
                if (breakPoint > startIndex + chunkSize * 0.5) {
                    actualEndIndex = startIndex + breakPoint + 1;
                }
            }
            const finalChunkContent = content.slice(startIndex, actualEndIndex);
            // Only create chunk if it has meaningful content
            if (finalChunkContent.trim().length >= minChunkSize) {
                chunks.push({
                    id: `chunk-${chunkIndex}-${Date.now()}`,
                    content: finalChunkContent.trim(),
                    metadata: {
                        chunkIndex,
                        startChar: startIndex,
                        endChar: actualEndIndex,
                        pageNumber: this.estimatePageNumber(startIndex, content),
                    },
                });
            }
            // Calculate next start position
            const nextStartIndex = Math.max(actualEndIndex - chunkOverlap, startIndex + minChunkSize);
            // Ensure we're making progress
            if (nextStartIndex <= startIndex) {
                console.log(`⚠️ Breaking to prevent infinite loop at position ${startIndex}`);
                break;
            }
            startIndex = nextStartIndex;
            chunkIndex++;
            // Log progress every 10 chunks
            if (chunkIndex % 10 === 0) {
                console.log(`📊 Processed ${chunkIndex} chunks, ${Math.round((startIndex / content.length) * 100)}% complete`);
            }
        }
        if (chunkIndex >= maxChunks) {
            console.warn(`⚠️ Hit maximum chunk limit (${maxChunks}) for ${fileName}`);
        }
        console.log(`✅ Created ${chunks.length} chunks for ${fileName}`);
        return chunks;
    }
    estimatePageCount(content) {
        // Rough estimation: 500 words per page
        const wordCount = content.split(/\s+/).length;
        return Math.ceil(wordCount / 500);
    }
    estimatePageNumber(charIndex, fullContent) {
        const contentUpToIndex = fullContent.slice(0, charIndex);
        const wordCount = contentUpToIndex.split(/\s+/).length;
        return Math.ceil(wordCount / 500);
    }
    async uploadToLlamaCloud(filePath, fileName, userId) {
        try {
            console.log(`Starting upload to LlamaCloud: ${fileName} for user: ${userId}`);
            // Create document entry immediately
            const documentId = `${userId}-${fileName}-${Date.now()}`;
            const document = {
                id: documentId,
                name: fileName,
                type: this.getMimeType(fileName),
                size: fs_1.default.statSync(filePath).size,
                status: "uploading",
                uploadedAt: new Date().toISOString(),
                userId: userId,
            };
            // Store document metadata
            this.documents.set(documentId, document);
            try {
                // Upload to LlamaCloud
                const result = await this.uploadFileToLlamaCloud(filePath, fileName, userId);
                // Update document with LlamaCloud ID and success status
                document.llamaCloudId = result.llamaCloudId;
                document.status = "success";
                this.documents.set(documentId, document);
                console.log(`Successfully uploaded ${fileName} to LlamaCloud`);
                return documentId;
            }
            catch (uploadError) {
                // Update document with error status
                document.status = "error";
                document.error =
                    uploadError instanceof Error ? uploadError.message : "Upload failed";
                this.documents.set(documentId, document);
                throw uploadError;
            }
        }
        catch (error) {
            console.error(`Error in uploadToLlamaCloud for ${fileName}:`, error);
            throw error;
        }
    }
    async searchUserDocuments(query, userId, topK = 5) {
        try {
            console.log(`Searching documents for user ${userId} with query: ${query}`);
            if (!this.apiKey) {
                console.log("LlamaCloud API key not configured, returning empty results");
                return [];
            }
            // Use the retrieval endpoint for search
            const response = await (0, node_fetch_1.default)(this.endpointUrl, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${this.apiKey}`,
                },
                body: JSON.stringify({
                    query: query,
                    top_k: topK,
                    filters: {
                        user_id: userId,
                    },
                }),
            });
            if (!response.ok) {
                console.error(`LlamaCloud search failed: ${response.status} ${response.statusText}`);
                return [];
            }
            const result = await response.json();
            // Transform LlamaCloud results to our format
            const searchResults = result.nodes?.map((node, index) => ({
                id: node.id || `result-${index}`,
                text: node.text || node.content || "",
                score: node.score || 0,
                metadata: {
                    ...node.metadata,
                    filename: node.metadata?.file_name ||
                        node.metadata?.filename ||
                        "Unknown Document",
                },
            })) || [];
            console.log(`Found ${searchResults.length} search results`);
            return searchResults;
        }
        catch (error) {
            console.error("Error searching user documents:", error);
            return [];
        }
    }
    async getUserDocumentSummary(documentId, userId) {
        try {
            console.log(`Getting summary for document ${documentId} for user ${userId}`);
            const document = this.documents.get(documentId);
            if (!document) {
                throw new Error(`Document ${documentId} not found`);
            }
            if (document.userId !== userId && userId !== "anonymous") {
                throw new Error(`Document ${documentId} does not belong to user ${userId}`);
            }
            // Generate a summary based on document metadata
            const summary = `Document: ${document.name}
Type: ${document.type}
Size: ${this.formatFileSize(document.size)}
Status: ${document.status}
Uploaded: ${new Date(document.uploadedAt).toLocaleDateString()}
${document.metadata?.pageCount ? `Pages: ${document.metadata.pageCount}` : ""}
${document.metadata?.wordCount ? `Words: ${document.metadata.wordCount}` : ""}
${document.metadata?.title ? `Title: ${document.metadata.title}` : ""}

This document contains important information relevant to the user's knowledge base and can be used for context in conversations.`;
            return summary;
        }
        catch (error) {
            console.error("Error getting document summary:", error);
            throw error;
        }
    }
    async getFileStatus(fileId) {
        const document = this.documents.get(fileId);
        if (!document) {
            return { status: "not_found", id: fileId };
        }
        return {
            status: document.status,
            id: fileId,
            name: document.name,
            progress: document.status === "success"
                ? 100
                : document.status === "error"
                    ? 0
                    : 50,
            error: document.error,
        };
    }
    // Get user documents with proper filtering and metadata
    async getUserDocuments(userId) {
        try {
            console.log(`Getting documents for user: ${userId}`);
            // Filter documents by user ID (or return all for anonymous/demo)
            const userDocuments = Array.from(this.documents.values()).filter((doc) => doc.userId === userId || userId === "anonymous");
            // Sort by upload date (newest first)
            userDocuments.sort((a, b) => new Date(b.uploadedAt).getTime() - new Date(a.uploadedAt).getTime());
            console.log(`Found ${userDocuments.length} documents for user ${userId}`);
            return userDocuments;
        }
        catch (error) {
            console.error("Error getting user documents:", error);
            return [];
        }
    }
    // Add a method to store document metadata
    storeDocumentMetadata(document) {
        this.documents.set(document.id, document);
    }
    // Add a method to update document status
    updateDocumentStatus(documentId, status, error) {
        const document = this.documents.get(documentId);
        if (document) {
            document.status = status;
            if (error) {
                document.error = error;
            }
            this.documents.set(documentId, document);
        }
    }
    // Add a method to get document by ID
    getDocumentById(documentId) {
        return this.documents.get(documentId);
    }
    // Add a method to delete document
    deleteDocument(documentId) {
        return this.documents.delete(documentId);
    }
    // Helper method to format file size
    formatFileSize(bytes) {
        if (bytes === 0)
            return "0 Bytes";
        const k = 1024;
        const sizes = ["Bytes", "KB", "MB", "GB"];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    }
    // Method to upload file directly to LlamaCloud using the upload API
    async uploadFileToLlamaCloud(filePath, fileName, userId) {
        try {
            if (!this.apiKey) {
                throw new Error("LlamaCloud API key not configured");
            }
            console.log(`Actually uploading ${fileName} to LlamaCloud for user ${userId}`);
            // Use LlamaCloud's parsing API directly
            const form = new form_data_1.default();
            // Add the file to the form
            form.append("file", fs_1.default.createReadStream(filePath), {
                filename: fileName,
                contentType: this.getMimeType(fileName),
            });
            // Upload to LlamaCloud using the correct parsing API endpoint
            const uploadUrl = `${this.baseUrl}/api/v1/parsing/upload`;
            const response = await (0, node_fetch_1.default)(uploadUrl, {
                method: "POST",
                headers: {
                    Authorization: `Bearer ${this.apiKey}`,
                    accept: "application/json",
                    ...form.getHeaders(),
                },
                body: form,
            });
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`LlamaCloud upload failed: ${response.status} ${response.statusText} - ${errorText}`);
            }
            const result = await response.json();
            const documentId = `${userId}-${fileName}-${Date.now()}`;
            const llamaCloudId = result.id || `llamacloud-${documentId}`;
            console.log(`Document ${fileName} successfully uploaded to LlamaCloud with job ID: ${llamaCloudId}`);
            // Wait for parsing to complete and get the result
            await this.waitForParsingCompletion(llamaCloudId);
            return {
                documentId: documentId,
                llamaCloudId: llamaCloudId,
            };
        }
        catch (error) {
            console.error("Error uploading to LlamaCloud:", error);
            // If LlamaCloud upload fails, throw error so user knows
            throw new Error(`Failed to upload to LlamaCloud: ${error instanceof Error ? error.message : "Unknown error"}`);
        }
    }
    // Wait for parsing job to complete
    async waitForParsingCompletion(jobId) {
        const maxAttempts = 30; // 5 minutes max wait time
        let attempts = 0;
        while (attempts < maxAttempts) {
            try {
                const statusUrl = `${this.baseUrl}/api/v1/parsing/job/${jobId}`;
                const response = await (0, node_fetch_1.default)(statusUrl, {
                    method: "GET",
                    headers: {
                        Authorization: `Bearer ${this.apiKey}`,
                        accept: "application/json",
                    },
                });
                if (response.ok) {
                    const result = await response.json();
                    const status = result.status;
                    console.log(`Parsing job ${jobId} status: ${status}`);
                    if (status === "SUCCESS") {
                        console.log(`Parsing completed successfully for job ${jobId}`);
                        return;
                    }
                    else if (status === "ERROR" || status === "FAILED") {
                        throw new Error(`Parsing failed for job ${jobId}: ${result.error || "Unknown error"}`);
                    }
                    // If status is PENDING or RUNNING, continue waiting
                }
                // Wait 10 seconds before checking again
                await new Promise((resolve) => setTimeout(resolve, 10000));
                attempts++;
            }
            catch (error) {
                console.error(`Error checking parsing status for job ${jobId}:`, error);
                attempts++;
                await new Promise((resolve) => setTimeout(resolve, 10000));
            }
        }
        throw new Error(`Parsing timeout for job ${jobId} after ${maxAttempts} attempts`);
    }
    getMimeType(fileName) {
        const ext = path_1.default.extname(fileName).toLowerCase();
        const mimeTypes = {
            ".pdf": "application/pdf",
            ".doc": "application/msword",
            ".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            ".txt": "text/plain",
            ".md": "text/markdown",
            ".rtf": "application/rtf",
            ".odt": "application/vnd.oasis.opendocument.text",
        };
        return mimeTypes[ext] || "application/octet-stream";
    }
}
exports.llamaCloudService = new LlamaCloudService();
//# sourceMappingURL=llamaCloudService.js.map