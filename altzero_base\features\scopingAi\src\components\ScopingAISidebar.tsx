import React, { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import {
  LayoutDashboard,
  FileText,
  BookOpen,
  Users,
  Settings,
  Brain,
  MessageSquare,
  Menu,
} from "lucide-react";
import { cn } from "../lib/utils";

const navigationItems = [
  {
    name: "Dashboard",
    href: "/scopingai/dashboard",
    icon: LayoutDashboard,
  },
  {
    name: "Proposals",
    href: "/scopingai/proposals",
    icon: FileText,
  },
  {
    name: "Knowledge Base",
    href: "/scopingai/knowledge-base",
    icon: BookOpen,
    children: [
      {
        name: "Documents",
        href: "/scopingai/knowledge-base/documents",
        icon: FileText,
      },
      {
        name: "Clients",
        href: "/scopingai/knowledge-base/clients",
        icon: Users,
      },
      {
        name: "Prompts",
        href: "/scopingai/knowledge-base/prompts",
        icon: MessageSquare,
      },
    ],
  },
];

export default function ScopingAISidebar() {
  const location = useLocation();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const isActive = (href: string) => {
    if (href === "/scopingai/dashboard") {
      return (
        location.pathname === "/scopingai" ||
        location.pathname === "/scopingai/dashboard"
      );
    }
    return (
      location.pathname === href || location.pathname.startsWith(href + "/")
    );
  };

  const isChildActive = (parentHref: string, childHref: string) => {
    return location.pathname === childHref;
  };

  return (
    <div
      className={cn(
        "bg-white border-r border-gray-200 flex flex-col transition-all duration-300 ease-in-out",
        sidebarCollapsed ? "w-16" : "w-64"
      )}
    >
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <Link
            to="/scopingai/dashboard"
            className="flex items-center space-x-3"
          >
            <div className="p-2 bg-blue-600 rounded-lg">
              <Brain className="h-6 w-6 text-white" />
            </div>
            {!sidebarCollapsed && (
              <div>
                <h1 className="text-xl font-bold text-gray-900">ScopingAI</h1>
                <p className="text-sm text-gray-600">AI-Powered Scoping</p>
              </div>
            )}
          </Link>
          <button
            onClick={toggleSidebar}
            className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
            title={sidebarCollapsed ? "Expand sidebar" : "Collapse sidebar"}
          >
            <Menu className="h-5 w-5 text-gray-600" />
          </button>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
        {navigationItems.map((item) => (
          <div key={item.name}>
            <Link
              to={item.href}
              className={cn(
                "flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors",
                sidebarCollapsed ? "justify-center" : "space-x-3",
                isActive(item.href)
                  ? "bg-blue-50 text-blue-700 border border-blue-200"
                  : "text-gray-700 hover:bg-gray-100"
              )}
              title={sidebarCollapsed ? item.name : undefined}
            >
              <item.icon className="h-5 w-5" />
              {!sidebarCollapsed && <span>{item.name}</span>}
            </Link>

            {/* Sub-navigation - only show when not collapsed */}
            {!sidebarCollapsed && item.children && isActive(item.href) && (
              <div className="ml-8 mt-2 space-y-1">
                {item.children.map((child) => (
                  <Link
                    key={child.name}
                    to={child.href}
                    className={cn(
                      "flex items-center space-x-2 px-3 py-1.5 rounded-md text-sm transition-colors",
                      isChildActive(item.href, child.href)
                        ? "bg-blue-100 text-blue-700"
                        : "text-gray-600 hover:bg-gray-50"
                    )}
                  >
                    <child.icon className="h-4 w-4" />
                    <span>{child.name}</span>
                  </Link>
                ))}
              </div>
            )}
          </div>
        ))}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        <Link
          to="/settings"
          className={cn(
            "flex items-center px-3 py-2 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-100 transition-colors",
            sidebarCollapsed ? "justify-center" : "space-x-3"
          )}
          title={sidebarCollapsed ? "Settings" : undefined}
        >
          <Settings className="h-5 w-5" />
          {!sidebarCollapsed && <span>Settings</span>}
        </Link>
      </div>
    </div>
  );
}
