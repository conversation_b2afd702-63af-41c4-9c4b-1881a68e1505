"use strict";
// =====================================================
// TOOL REGISTRY FOR SCOPINGAI LANGGRAPH WORKFLOWS
// =====================================================
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolRegistry = void 0;
const KnowledgeBaseService_1 = require("../../services/integration/KnowledgeBaseService");
const crmContactService_1 = require("../../services/integration/crmContactService");
class ToolRegistry {
    constructor(config) {
        this.tools = null;
        this.config = config;
    }
    // Get all available tools
    async getTools() {
        if (this.tools) {
            return this.tools;
        }
        this.tools = {
            ai: this.createAITools(),
            knowledgeBase: this.createKnowledgeBaseTools(),
            database: this.createDatabaseTools(),
            crm: this.createCRMTools(),
            market: this.createMarketTools(),
            http: this.createHTTPTools()
        };
        return this.tools;
    }
    // Create AI tools
    createAITools() {
        return {
            generateText: async (prompt, options = {}) => {
                try {
                    // Use OpenAI for text generation
                    const { default: OpenAI } = await Promise.resolve().then(() => __importStar(require('openai')));
                    const openai = new OpenAI({
                        apiKey: this.config.openai_api_key || process.env.OPENAI_API_KEY
                    });
                    const response = await openai.chat.completions.create({
                        model: options.model || 'gpt-4o-mini',
                        messages: [{ role: 'user', content: prompt }],
                        temperature: options.temperature || 0.7,
                        max_tokens: options.max_tokens || 2000,
                        response_format: options.response_format === 'json' ? { type: 'json_object' } : undefined
                    });
                    return response.choices[0].message.content || '';
                }
                catch (error) {
                    console.error('AI text generation failed:', error);
                    throw new Error(`AI text generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
                }
            },
            generateStructuredData: async (prompt, schema) => {
                try {
                    const structuredPrompt = `${prompt}\n\nPlease respond with valid JSON that matches this schema: ${JSON.stringify(schema)}`;
                    const { default: OpenAI } = await Promise.resolve().then(() => __importStar(require('openai')));
                    const openai = new OpenAI({
                        apiKey: this.config.openai_api_key || process.env.OPENAI_API_KEY
                    });
                    const response = await openai.chat.completions.create({
                        model: 'gpt-4o-mini',
                        messages: [{ role: 'user', content: structuredPrompt }],
                        temperature: 0.3,
                        max_tokens: 2000,
                        response_format: { type: 'json_object' }
                    });
                    const content = response.choices[0].message.content || '{}';
                    return JSON.parse(content);
                }
                catch (error) {
                    console.error('AI structured data generation failed:', error);
                    throw new Error(`AI structured data generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
                }
            },
            analyzeContent: async (content, criteria) => {
                try {
                    const prompt = `Analyze the following content based on these criteria: ${criteria.join(', ')}\n\nContent: ${content}\n\nProvide analysis as JSON with scores (0-100) for each criterion and overall recommendations.`;
                    const { default: OpenAI } = await Promise.resolve().then(() => __importStar(require('openai')));
                    const openai = new OpenAI({
                        apiKey: this.config.openai_api_key || process.env.OPENAI_API_KEY
                    });
                    const response = await openai.chat.completions.create({
                        model: 'gpt-4o-mini',
                        messages: [{ role: 'user', content: prompt }],
                        temperature: 0.3,
                        max_tokens: 1500,
                        response_format: { type: 'json_object' }
                    });
                    const content_result = response.choices[0].message.content || '{}';
                    return JSON.parse(content_result);
                }
                catch (error) {
                    console.error('AI content analysis failed:', error);
                    throw new Error(`AI content analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
                }
            }
        };
    }
    // Create Knowledge Base tools
    createKnowledgeBaseTools() {
        return {
            searchByRequirements: async (requirements, userId, documentIds) => {
                try {
                    return await KnowledgeBaseService_1.knowledgeBaseService.searchByRequirements(requirements, userId, documentIds);
                }
                catch (error) {
                    console.error('Knowledge base search failed:', error);
                    throw new Error(`Knowledge base search failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
                }
            },
            getUserDocuments: async (userId) => {
                try {
                    return await KnowledgeBaseService_1.knowledgeBaseService.getUserDocuments(userId);
                }
                catch (error) {
                    console.error('Get user documents failed:', error);
                    throw new Error(`Get user documents failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
                }
            },
            formatDocumentsForAI: (documents) => {
                try {
                    return KnowledgeBaseService_1.knowledgeBaseService.formatDocumentsForAI(documents);
                }
                catch (error) {
                    console.error('Format documents failed:', error);
                    return '';
                }
            }
        };
    }
    // Create Database tools
    createDatabaseTools() {
        return {
            query: async (sql, params = []) => {
                try {
                    // In a real implementation, this would use your database connection
                    console.log('Database query:', sql, params);
                    return [];
                }
                catch (error) {
                    console.error('Database query failed:', error);
                    throw new Error(`Database query failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
                }
            },
            insert: async (table, data) => {
                try {
                    console.log('Database insert:', table, data);
                    return { id: Date.now(), ...data };
                }
                catch (error) {
                    console.error('Database insert failed:', error);
                    throw new Error(`Database insert failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
                }
            },
            update: async (table, data, where) => {
                try {
                    console.log('Database update:', table, data, where);
                    return { affected: 1 };
                }
                catch (error) {
                    console.error('Database update failed:', error);
                    throw new Error(`Database update failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
                }
            }
        };
    }
    // Create CRM tools
    createCRMTools() {
        return {
            getClientData: async (clientId) => {
                try {
                    // Use existing CRM service
                    return await crmContactService_1.crmContactService.getContactById(clientId);
                }
                catch (error) {
                    console.error('Get client data failed:', error);
                    return null;
                }
            },
            getIndustryData: async (industry) => {
                try {
                    // Get industry-specific data
                    return {
                        industry,
                        market_size: 'Unknown',
                        growth_rate: 'Unknown',
                        key_trends: [],
                        challenges: [],
                        opportunities: []
                    };
                }
                catch (error) {
                    console.error('Get industry data failed:', error);
                    return null;
                }
            },
            getCompetitorData: async (industry, location) => {
                try {
                    // Get competitor information
                    return [];
                }
                catch (error) {
                    console.error('Get competitor data failed:', error);
                    return [];
                }
            }
        };
    }
    // Create Market tools
    createMarketTools() {
        return {
            getIndustryTrends: async (industry) => {
                try {
                    // Mock implementation - in production, this would call market research APIs
                    return [
                        { trend: 'Digital transformation', impact: 'high', timeline: '2024-2025' },
                        { trend: 'AI adoption', impact: 'medium', timeline: '2024-2026' },
                        { trend: 'Sustainability focus', impact: 'medium', timeline: '2024-2030' }
                    ];
                }
                catch (error) {
                    console.error('Get industry trends failed:', error);
                    return [];
                }
            },
            getMarketData: async (industry, location) => {
                try {
                    // Mock implementation
                    return {
                        market_size: '$1B+',
                        growth_rate: '5-10%',
                        key_players: [],
                        market_segments: []
                    };
                }
                catch (error) {
                    console.error('Get market data failed:', error);
                    return null;
                }
            },
            getCompetitiveAnalysis: async (industry, competitors) => {
                try {
                    // Mock implementation
                    return {
                        competitive_landscape: 'Moderate',
                        market_leaders: competitors.slice(0, 3),
                        opportunities: ['Market gap in X', 'Underserved segment Y'],
                        threats: ['New entrants', 'Price competition']
                    };
                }
                catch (error) {
                    console.error('Get competitive analysis failed:', error);
                    return null;
                }
            }
        };
    }
    // Create HTTP tools
    createHTTPTools() {
        return {
            get: async (url, options = {}) => {
                try {
                    const response = await fetch(url, {
                        method: 'GET',
                        headers: {
                            'User-Agent': 'ScopingAI-Workflow/1.0',
                            ...options.headers
                        },
                        ...options
                    });
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return await response.text();
                }
                catch (error) {
                    console.error('HTTP GET failed:', error);
                    throw new Error(`HTTP GET failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
                }
            },
            post: async (url, data, options = {}) => {
                try {
                    const response = await fetch(url, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'User-Agent': 'ScopingAI-Workflow/1.0',
                            ...options.headers
                        },
                        body: JSON.stringify(data),
                        ...options
                    });
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return await response.json();
                }
                catch (error) {
                    console.error('HTTP POST failed:', error);
                    throw new Error(`HTTP POST failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
                }
            }
        };
    }
    // Get tool availability status
    getToolStatus() {
        return {
            ai: !!(this.config.openai_api_key || process.env.OPENAI_API_KEY),
            knowledgeBase: KnowledgeBaseService_1.knowledgeBaseService.isAvailable(),
            database: true, // Always available (mock)
            crm: true, // Always available
            market: true, // Always available (mock)
            http: true // Always available
        };
    }
}
exports.ToolRegistry = ToolRegistry;
//# sourceMappingURL=ToolRegistry.js.map