{"version": 3, "sources": ["../../@radix-ui/react-use-size/src/use-size.tsx"], "sourcesContent": ["/// <reference types=\"resize-observer-browser\" />\n\nimport * as React from 'react';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\n\nfunction useSize(element: HTMLElement | null) {\n  const [size, setSize] = React.useState<{ width: number; height: number } | undefined>(undefined);\n\n  useLayoutEffect(() => {\n    if (element) {\n      // provide size as early as possible\n      setSize({ width: element.offsetWidth, height: element.offsetHeight });\n\n      const resizeObserver = new ResizeObserver((entries) => {\n        if (!Array.isArray(entries)) {\n          return;\n        }\n\n        // Since we only observe the one element, we don't need to loop over the\n        // array\n        if (!entries.length) {\n          return;\n        }\n\n        const entry = entries[0];\n        let width: number;\n        let height: number;\n\n        if ('borderBoxSize' in entry) {\n          const borderSizeEntry = entry['borderBoxSize'];\n          // iron out differences between browsers\n          const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;\n          width = borderSize['inlineSize'];\n          height = borderSize['blockSize'];\n        } else {\n          // for browsers that don't support `borderBoxSize`\n          // we calculate it ourselves to get the correct border box.\n          width = element.offsetWidth;\n          height = element.offsetHeight;\n        }\n\n        setSize({ width, height });\n      });\n\n      resizeObserver.observe(element, { box: 'border-box' });\n\n      return () => resizeObserver.unobserve(element);\n    } else {\n      // We only want to reset to `undefined` when the element becomes `null`,\n      // not if it changes to another element.\n      setSize(undefined);\n    }\n  }, [element]);\n\n  return size;\n}\n\nexport { useSize };\n"], "mappings": ";;;;;;;;;;;AAEA,YAAuB;AAGvB,SAAS,QAAQ,SAA6B;AAC5C,QAAM,CAAC,MAAM,OAAO,IAAU,eAAwD,MAAS;AAE/F,mBAAgB,MAAM;AACpB,QAAI,SAAS;AAEX,cAAQ,EAAE,OAAO,QAAQ,aAAa,QAAQ,QAAQ,aAAa,CAAC;AAEpE,YAAM,iBAAiB,IAAI,eAAe,CAAC,YAAY;AACrD,YAAI,CAAC,MAAM,QAAQ,OAAO,GAAG;AAC3B;QACF;AAIA,YAAI,CAAC,QAAQ,QAAQ;AACnB;QACF;AAEA,cAAM,QAAQ,QAAQ,CAAC;AACvB,YAAI;AACJ,YAAI;AAEJ,YAAI,mBAAmB,OAAO;AAC5B,gBAAM,kBAAkB,MAAM,eAAe;AAE7C,gBAAM,aAAa,MAAM,QAAQ,eAAe,IAAI,gBAAgB,CAAC,IAAI;AACzE,kBAAQ,WAAW,YAAY;AAC/B,mBAAS,WAAW,WAAW;QACjC,OAAO;AAGL,kBAAQ,QAAQ;AAChB,mBAAS,QAAQ;QACnB;AAEA,gBAAQ,EAAE,OAAO,OAAO,CAAC;MAC3B,CAAC;AAED,qBAAe,QAAQ,SAAS,EAAE,KAAK,aAAa,CAAC;AAErD,aAAO,MAAM,eAAe,UAAU,OAAO;IAC/C,OAAO;AAGL,cAAQ,MAAS;IACnB;EACF,GAAG,CAAC,OAAO,CAAC;AAEZ,SAAO;AACT;", "names": []}