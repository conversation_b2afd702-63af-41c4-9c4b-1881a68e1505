{"version": 3, "file": "DocumentGenerationService.js", "sourceRoot": "", "sources": ["../../../../../../server/features/scopingAi/services/core/DocumentGenerationService.ts"], "names": [], "mappings": ";;;AAAA,mCAAgC;AAChC,yEAAgE;AAiEhE,MAAa,yBAAyB;IAIpC;QAFQ,gBAAW,GAAG,KAAK,CAAC;QAG1B,IAAI,CAAC,GAAG,GAAG,IAAI,eAAM,CAAC;YACpB,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc;YAClC,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,2BAA2B;YACnE,OAAO,EAAE,KAAK;YACd,UAAU,EAAE,CAAC;SACd,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,UAAU;QACd,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;YAE9D,sBAAsB;YACtB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBAC1D,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;gBACxD,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,eAAe;gBAClD,UAAU,EAAE,EAAE;aACf,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAC;YACtE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QAC1B,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,cAAc,GAAG,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,oBAAoB,EAAE,WAAW,CAAC,CAAC;YACjF,OAAO,CAAC,KAAK,CAAC,sDAAsD,EAAE,cAAc,CAAC,CAAC;YACtF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,CAAC,sBAAsB,CAC3B,OAAkC,EAClC,oBAA6B,EAC7B,kBAA0B;QAE1B,MAAM,UAAU,GAAG,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QAEvC,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACtB,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YAC1B,CAAC;YAED,qBAAqB;YACrB,MAAM;gBACJ,IAAI,EAAE,SAAS;gBACf,UAAU;gBACV,IAAI,EAAE;oBACJ,OAAO,EAAE,iCAAiC;oBAC1C,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,IAAI;oBAC3B,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI;iBAC9B;aACF,CAAC;YAEF,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,eAAe,CAAC;YAC1D,MAAM,QAAQ,GAAsB,EAAE,CAAC;YAEvC,iBAAiB;YACjB,MAAM;gBACJ,IAAI,EAAE,UAAU;gBAChB,UAAU;gBACV,IAAI,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,mCAAmC,EAAE;aAC1E,CAAC;YAEF,MAAM,cAAc,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC;YAE/E,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBAC9D,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC;gBACrD,KAAK,EAAE,KAAK;gBACZ,UAAU,EAAE,IAAI;gBAChB,WAAW,EAAE,GAAG;aACjB,CAAC,CAAC;YAEH,MAAM,eAAe,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC;YAE1E,MAAM;gBACJ,IAAI,EAAE,UAAU;gBAChB,UAAU;gBACV,IAAI,EAAE;oBACJ,MAAM,EAAE,WAAW;oBACnB,OAAO,EAAE,eAAe;oBACxB,OAAO,EAAE,0BAA0B;iBACpC;aACF,CAAC;YAEF,oBAAoB;YACpB,MAAM,aAAa,GAAG,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC;YACvD,IAAI,iBAAiB,GAAG,CAAC,CAAC;YAE1B,KAAK,MAAM,UAAU,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBACnD,MAAM;oBACJ,IAAI,EAAE,UAAU;oBAChB,UAAU;oBACV,IAAI,EAAE;wBACJ,OAAO,EAAE,cAAc,UAAU,CAAC,KAAK,KAAK;wBAC5C,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,iBAAiB,GAAG,aAAa,CAAC,GAAG,GAAG,CAAC;qBAChE;iBACF,CAAC;gBAEF,MAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAC3C,UAAU,EACV,OAAO,EACP,eAAe,EACf,oBAAoB,CACrB,CAAC;gBAEF,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;oBAC7D,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,CAAC;oBACpD,KAAK,EAAE,KAAK;oBACZ,UAAU,EAAE,IAAI;oBAChB,WAAW,EAAE,GAAG;iBACjB,CAAC,CAAC;gBAEH,MAAM,OAAO,GAAoB;oBAC/B,KAAK,EAAE,UAAU,CAAC,KAAK;oBACvB,OAAO,EAAE,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE;oBACzD,QAAQ,EAAE;wBACR,WAAW,EAAE,UAAU,CAAC,WAAW;wBACnC,QAAQ,EAAE,UAAU,CAAC,QAAQ;wBAC7B,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qBACtC;iBACF,CAAC;gBAEF,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACvB,iBAAiB,EAAE,CAAC;gBAEpB,MAAM;oBACJ,IAAI,EAAE,SAAS;oBACf,UAAU;oBACV,IAAI,EAAE;wBACJ,MAAM,EAAE,WAAW;wBACnB,OAAO;wBACP,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,iBAAiB,GAAG,aAAa,CAAC,GAAG,GAAG,CAAC;qBAChE;iBACF,CAAC;YACJ,CAAC;YAED,wBAAwB;YACxB,MAAM,QAAQ,GAAsB;gBAClC,EAAE,EAAE,UAAU;gBACd,KAAK,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE;gBAC3D,QAAQ;gBACR,QAAQ,EAAE;oBACR,WAAW,EAAE,mBAAmB;oBAChC,OAAO,EAAE,KAAK;oBACd,aAAa,EAAE,QAAQ,CAAC,MAAM;oBAC9B,sBAAsB,EAAE,kBAAkB,EAAE,MAAM,IAAI,CAAC;oBACvD,gBAAgB,EAAE,CAAC,CAAC,oBAAoB;oBACxC,eAAe,EAAE,oBAAoB,CAAC,CAAC,CAAC,8BAA8B,CAAC,CAAC,CAAC,UAAU;oBACnF,OAAO,EAAE,KAAK;oBACd,mBAAmB,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBAC9C;aACF,CAAC;YAEF,mBAAmB;YACnB,IAAI,CAAC;gBACH,MAAM,UAAU,GAAG,MAAM,6CAAkB,CAAC,cAAc,CAAC;oBACzD,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,QAAQ,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE;oBAC3B,KAAK,EAAE,QAAQ,CAAC,KAAK;oBACrB,WAAW,EAAE,OAAO,CAAC,OAAO,CAAC,WAAW;oBACxC,MAAM,EAAE,WAAW;oBACnB,UAAU,EAAE,OAAO,CAAC,QAAQ,CAAC,EAAE;oBAC/B,QAAQ,EAAE;wBACR,iBAAiB,EAAE,OAAO;wBAC1B,kBAAkB,EAAE,kBAAkB,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;4BAClD,EAAE,EAAE,GAAG,CAAC,EAAE;4BACV,KAAK,EAAE,GAAG,CAAC,KAAK;4BAChB,eAAe,EAAE,GAAG,CAAC,eAAe;yBACrC,CAAC,CAAC,IAAI,EAAE;qBACV;iBACF,CAAC,CAAC;gBAEH,MAAM,6CAAkB,CAAC,qBAAqB,CAAC;oBAC7C,UAAU;oBACV,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,KAAK,EAAE,QAAQ,CAAC,KAAK;oBACrB,OAAO,EAAE;wBACP,QAAQ,EAAE,QAAQ,CAAC,QAAQ;wBAC3B,QAAQ,EAAE,QAAQ,CAAC,QAAQ;qBAC5B;oBACD,QAAQ,EAAE,QAAQ,CAAC,QAAQ;oBAC3B,kBAAkB,EAAE;wBAClB,KAAK;wBACL,gBAAgB,EAAE,CAAC,CAAC,oBAAoB;wBACxC,uBAAuB,EAAE,kBAAkB,EAAE,MAAM,IAAI,CAAC;qBACzD;iBACF,CAAC,CAAC;gBAEH,OAAO,CAAC,GAAG,CAAC,kDAAkD,UAAU,EAAE,CAAC,CAAC;YAC9E,CAAC;YAAC,OAAO,OAAO,EAAE,CAAC;gBACjB,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,OAAO,CAAC,CAAC;gBACjE,iDAAiD;YACnD,CAAC;YAED,mBAAmB;YACnB,MAAM;gBACJ,IAAI,EAAE,WAAW;gBACjB,UAAU;gBACV,IAAI,EAAE;oBACJ,QAAQ;oBACR,OAAO,EAAE,6CAA6C;oBACtD,QAAQ,EAAE,GAAG;iBACd;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,cAAc,GAAG,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,oBAAoB,EAAE,WAAW,CAAC,CAAC;YACjF,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,cAAc,CAAC,CAAC;YAEjE,MAAM;gBACJ,IAAI,EAAE,OAAO;gBACb,UAAU;gBACV,KAAK,EAAE,4BAA4B;gBACnC,IAAI,EAAE,EAAE,OAAO,EAAE,cAAc,EAAE;aAClC,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,mBAAmB,CAAC,OAAkC,EAAE,oBAA6B;QAC3F,OAAO;;;YAGC,OAAO,CAAC,MAAM,CAAC,IAAI;aAClB,OAAO,CAAC,MAAM,CAAC,OAAO,IAAI,eAAe;cACxC,OAAO,CAAC,MAAM,CAAC,QAAQ,IAAI,eAAe;aAC3C,OAAO,CAAC,MAAM,CAAC,aAAa,IAAI,eAAe;aAC/C,OAAO,CAAC,OAAO,CAAC,IAAI;iBAChB,OAAO,CAAC,OAAO,CAAC,WAAW;gBAC5B,OAAO,CAAC,OAAO,CAAC,UAAU,IAAI,eAAe;;;EAG3D,OAAO,CAAC,YAAY,CAAC,mBAAmB;;EAExC,oBAAoB,CAAC,CAAC,CAAC;;EAEvB,oBAAoB;;;CAGrB,CAAC,CAAC,CAAC,EAAE;;yJAEmJ,CAAC;IACxJ,CAAC;IAEO,kBAAkB,CACxB,UAAe,EACf,OAAkC,EAClC,eAAuB,EACvB,oBAA6B;QAE7B,OAAO;;;EAGT,eAAe;;;UAGP,OAAO,CAAC,MAAM,CAAC,IAAI;aAChB,OAAO,CAAC,MAAM,CAAC,OAAO,IAAI,eAAe;cACxC,OAAO,CAAC,MAAM,CAAC,QAAQ,IAAI,eAAe;;;UAG9C,OAAO,CAAC,OAAO,CAAC,IAAI;iBACb,OAAO,CAAC,OAAO,CAAC,WAAW;;EAE1C,oBAAoB,CAAC,CAAC,CAAC;;EAEvB,oBAAoB;CACrB,CAAC,CAAC,CAAC,EAAE;;oBAEc,UAAU,CAAC,KAAK;uBACb,UAAU,CAAC,WAAW;;;;;;;;;EAS3C,oBAAoB,CAAC,CAAC,CAAC,0EAA0E,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACzG,CAAC;CACF;AA3RD,8DA2RC;AAEY,QAAA,yBAAyB,GAAG,IAAI,yBAAyB,EAAE,CAAC"}