"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const aichat_1 = __importDefault(require("../routes/aichat"));
// Create the backend plugin for AI Chat (CopilotKit)
const aichatBackendPlugin = {
    router: aichat_1.default,
    config: {
        name: "AI Chat API",
        version: "1.0.0",
        apiPrefix: "/api/aichat",
    },
    initialize: async () => {
        console.log("🤖 AI Chat backend plugin initialized");
    },
    cleanup: async () => {
        console.log("🤖 AI Chat backend plugin cleaned up");
    },
    healthCheck: async () => {
        try {
            return true;
        }
        catch (error) {
            console.error("AI Chat backend health check failed:", error);
            return false;
        }
    },
};
exports.default = aichatBackendPlugin;
//# sourceMappingURL=index.js.map