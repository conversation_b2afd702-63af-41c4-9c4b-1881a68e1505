"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.documentProcessorService = exports.DocumentProcessorService = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
class DocumentProcessorService {
    constructor(options = {}) {
        this.options = options;
        this.defaultOptions = {
            extractText: true,
            preserveFormatting: false,
            maxSize: 50 * 1024 * 1024, // 50MB
            allowedTypes: ['pdf', 'doc', 'docx', 'txt', 'md']
        };
        this.options = { ...this.defaultOptions, ...options };
    }
    /**
     * Process a document file and extract content
     */
    async processDocument(filePath, originalName, options) {
        try {
            const processingOptions = { ...this.options, ...options };
            console.log(`📄 Processing document: ${originalName}`);
            // Check file size
            const stats = fs_1.default.statSync(filePath);
            if (stats.size > (processingOptions.maxSize || this.defaultOptions.maxSize)) {
                throw new Error(`File size exceeds maximum allowed size of ${processingOptions.maxSize} bytes`);
            }
            // Get file extension
            const ext = path_1.default.extname(originalName).toLowerCase().replace('.', '');
            // Check allowed types
            if (processingOptions.allowedTypes && !processingOptions.allowedTypes.includes(ext)) {
                throw new Error(`File type '${ext}' is not allowed`);
            }
            let content = '';
            let metadata = {
                originalName,
                fileType: ext,
                fileSize: stats.size,
                processedAt: new Date().toISOString(),
                processingMethod: 'basic'
            };
            // Process based on file type
            switch (ext) {
                case 'txt':
                case 'md':
                    content = await this.processTextFile(filePath);
                    break;
                case 'pdf':
                    content = await this.processPdfFile(filePath);
                    metadata.processingMethod = 'pdf-extraction';
                    break;
                case 'doc':
                case 'docx':
                    content = await this.processWordFile(filePath);
                    metadata.processingMethod = 'word-extraction';
                    break;
                default:
                    // Try to read as text
                    content = await this.processTextFile(filePath);
                    metadata.processingMethod = 'fallback-text';
            }
            // Clean and validate content
            content = this.cleanContent(content);
            if (!content || content.trim().length === 0) {
                throw new Error('No readable content found in document');
            }
            const processedDoc = {
                id: `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                title: originalName,
                content,
                type: ext,
                size: stats.size,
                metadata: {
                    ...metadata,
                    contentLength: content.length,
                    wordCount: this.countWords(content)
                }
            };
            console.log(`✅ Document processed successfully: ${processedDoc.id}`);
            console.log(`📊 Content length: ${content.length} characters, Word count: ${metadata.wordCount}`);
            return processedDoc;
        }
        catch (error) {
            console.error(`❌ Error processing document ${originalName}:`, error);
            throw new Error(`Document processing failed: ${error.message}`);
        }
    }
    /**
     * Process text files (txt, md)
     */
    async processTextFile(filePath) {
        try {
            return fs_1.default.readFileSync(filePath, 'utf-8');
        }
        catch (error) {
            throw new Error(`Failed to read text file: ${error.message}`);
        }
    }
    /**
     * Process PDF files
     */
    async processPdfFile(filePath) {
        try {
            // For now, return a placeholder - in production, you'd use a PDF parsing library
            console.log('⚠️ PDF processing not implemented - using placeholder');
            return `[PDF Content from ${path_1.default.basename(filePath)}]\n\nThis is a placeholder for PDF content extraction. In production, this would use a PDF parsing library to extract the actual text content.`;
        }
        catch (error) {
            throw new Error(`Failed to process PDF file: ${error.message}`);
        }
    }
    /**
     * Process Word documents (doc, docx)
     */
    async processWordFile(filePath) {
        try {
            // For now, return a placeholder - in production, you'd use a Word parsing library
            console.log('⚠️ Word document processing not implemented - using placeholder');
            return `[Word Document Content from ${path_1.default.basename(filePath)}]\n\nThis is a placeholder for Word document content extraction. In production, this would use a Word parsing library to extract the actual text content.`;
        }
        catch (error) {
            throw new Error(`Failed to process Word file: ${error.message}`);
        }
    }
    /**
     * Clean and normalize content
     */
    cleanContent(content) {
        return content
            // Remove excessive whitespace
            .replace(/\s+/g, ' ')
            // Remove control characters
            .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
            // Normalize line breaks
            .replace(/\r\n/g, '\n')
            .replace(/\r/g, '\n')
            // Remove excessive line breaks
            .replace(/\n{3,}/g, '\n\n')
            // Trim
            .trim();
    }
    /**
     * Count words in content
     */
    countWords(content) {
        return content.trim().split(/\s+/).filter(word => word.length > 0).length;
    }
    /**
     * Validate file type
     */
    isValidFileType(filename) {
        const ext = path_1.default.extname(filename).toLowerCase().replace('.', '');
        return this.options.allowedTypes?.includes(ext) || false;
    }
    /**
     * Get supported file types
     */
    getSupportedTypes() {
        return this.options.allowedTypes || [];
    }
    /**
     * Check if file size is within limits
     */
    isValidFileSize(size) {
        return size <= (this.options.maxSize || this.defaultOptions.maxSize);
    }
    /**
     * Get maximum file size
     */
    getMaxFileSize() {
        return this.options.maxSize || this.defaultOptions.maxSize;
    }
    /**
     * Format file size for display
     */
    formatFileSize(bytes) {
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        if (bytes === 0)
            return '0 Bytes';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }
    /**
     * Extract metadata from processed document
     */
    extractMetadata(doc) {
        return {
            id: doc.id,
            title: doc.title,
            type: doc.type,
            size: doc.size,
            sizeFormatted: this.formatFileSize(doc.size),
            contentLength: doc.content.length,
            wordCount: this.countWords(doc.content),
            ...doc.metadata
        };
    }
}
exports.DocumentProcessorService = DocumentProcessorService;
exports.documentProcessorService = new DocumentProcessorService();
//# sourceMappingURL=DocumentProcessorService.js.map