// =====================================================
// AI CHAT WORKFLOW STATE MANAGER
// =====================================================

import { AIChatWorkflowState } from '../types/WorkflowState';

export class StateManager {
  private static instance: StateManager;
  private workflowStates: Map<string, AIChatWorkflowState> = new Map();
  private executionHistory: Map<string, any[]> = new Map();
  private stateSnapshots: Map<string, AIChatWorkflowState[]> = new Map();

  public static getInstance(): StateManager {
    if (!StateManager.instance) {
      StateManager.instance = new StateManager();
    }
    return StateManager.instance;
  }

  // Save workflow state
  public async saveWorkflowState(workflowId: string, state: AIChatWorkflowState): Promise<void> {
    try {
      this.workflowStates.set(workflowId, { ...state });
      this.createStateSnapshot(workflowId, state);
      await this.persistStateToDatabase(workflowId, state);
    } catch (error) {
      console.error(`Failed to save workflow state for ${workflowId}:`, error);
      throw error;
    }
  }

  // Get workflow state
  public async getWorkflowState(workflowId: string): Promise<AIChatWorkflowState | null> {
    try {
      const memoryState = this.workflowStates.get(workflowId);
      if (memoryState) {
        return { ...memoryState };
      }

      const dbState = await this.loadStateFromDatabase(workflowId);
      if (dbState) {
        this.workflowStates.set(workflowId, dbState);
        return { ...dbState };
      }

      return null;
    } catch (error) {
      console.error(`Failed to get workflow state for ${workflowId}:`, error);
      return null;
    }
  }

  // Get workflow status
  public async getWorkflowStatus(workflowId: string): Promise<any> {
    const state = await this.getWorkflowState(workflowId);
    
    if (!state) {
      return {
        status: 'not_found',
        message: `Workflow ${workflowId} not found`
      };
    }

    return {
      workflow_id: state.workflow_id,
      status: state.status,
      progress: state.progress,
      current_step: state.current_step,
      started_at: state.started_at,
      completed_at: state.completed_at,
      processing_time: state.processing_time,
      errors: state.errors,
      ai_response: state.ai_response,
      navigation_actions: state.navigation_actions,
      database_queries: state.database_queries,
      knowledge_searches: state.knowledge_searches,
      last_updated: state.last_updated
    };
  }

  // Create state snapshot for history
  private createStateSnapshot(workflowId: string, state: AIChatWorkflowState): void {
    const snapshots = this.stateSnapshots.get(workflowId) || [];
    snapshots.push({ ...state });
    
    // Keep only last 10 snapshots
    if (snapshots.length > 10) {
      snapshots.shift();
    }
    
    this.stateSnapshots.set(workflowId, snapshots);
  }

  // Persist state to database (placeholder)
  private async persistStateToDatabase(workflowId: string, state: AIChatWorkflowState): Promise<void> {
    // In production, this would save to a real database
    // For now, we'll just log it
    console.log(`💾 Persisting state for workflow: ${workflowId} - Step: ${state.current_step} (${state.progress}%)`);
  }

  // Load state from database (placeholder)
  private async loadStateFromDatabase(workflowId: string): Promise<AIChatWorkflowState | null> {
    // In production, this would load from a real database
    console.log(`📂 Loading state for workflow: ${workflowId} from database`);
    return null;
  }

  // Get workflow statistics
  public getWorkflowStats(): any {
    const totalWorkflows = this.workflowStates.size;
    const runningWorkflows = Array.from(this.workflowStates.values()).filter(s => s.status === 'running').length;
    const completedWorkflows = Array.from(this.workflowStates.values()).filter(s => s.status === 'completed').length;
    const failedWorkflows = Array.from(this.workflowStates.values()).filter(s => s.status === 'failed').length;

    return {
      total_workflows: totalWorkflows,
      running_workflows: runningWorkflows,
      completed_workflows: completedWorkflows,
      failed_workflows: failedWorkflows,
      success_rate: totalWorkflows > 0 ? (completedWorkflows / totalWorkflows) * 100 : 0
    };
  }

  // Update workflow progress
  public async updateProgress(workflowId: string, progress: number, currentStep: string): Promise<void> {
    const state = this.workflowStates.get(workflowId);

    if (state) {
      const updatedState: AIChatWorkflowState = {
        ...state,
        progress,
        current_step: currentStep,
        last_updated: new Date().toISOString(),
      };

      await this.saveWorkflowState(workflowId, updatedState);
    }
  }

  // Add error to workflow
  public async addError(workflowId: string, error: any): Promise<void> {
    const state = this.workflowStates.get(workflowId);

    if (state) {
      const updatedState: AIChatWorkflowState = {
        ...state,
        errors: [...state.errors, error],
        last_updated: new Date().toISOString(),
      };

      await this.saveWorkflowState(workflowId, updatedState);
    }
  }

  // Clean up old workflows
  public cleanupOldWorkflows(maxAge: number = 24 * 60 * 60 * 1000): void {
    const now = Date.now();
    const workflowsToDelete: string[] = [];

    this.workflowStates.forEach((state, workflowId) => {
      const stateAge = now - new Date(state.started_at).getTime();
      if (stateAge > maxAge && (state.status === 'completed' || state.status === 'failed')) {
        workflowsToDelete.push(workflowId);
      }
    });

    workflowsToDelete.forEach(workflowId => {
      this.workflowStates.delete(workflowId);
      this.executionHistory.delete(workflowId);
      this.stateSnapshots.delete(workflowId);
    });

    if (workflowsToDelete.length > 0) {
      console.log(`🧹 Cleaned up ${workflowsToDelete.length} old workflows`);
    }
  }

  // Get execution history
  public getExecutionHistory(workflowId: string): any[] {
    return this.executionHistory.get(workflowId) || [];
  }

  // Add execution history entry
  public addExecutionHistory(workflowId: string, entry: any): void {
    const history = this.executionHistory.get(workflowId) || [];
    history.push({
      ...entry,
      timestamp: new Date().toISOString()
    });
    
    // Keep only last 50 entries
    if (history.length > 50) {
      history.shift();
    }
    
    this.executionHistory.set(workflowId, history);
  }

  // Get state snapshots
  public getStateSnapshots(workflowId: string): AIChatWorkflowState[] {
    return this.stateSnapshots.get(workflowId) || [];
  }

  // Cancel workflow
  public async cancelWorkflow(workflowId: string): Promise<boolean> {
    const state = this.workflowStates.get(workflowId);
    
    if (state && state.status === 'running') {
      const cancelledState: AIChatWorkflowState = {
        ...state,
        status: 'failed',
        current_step: 'cancelled',
        completed_at: new Date().toISOString(),
        last_updated: new Date().toISOString(),
        errors: [
          ...state.errors,
          {
            node_name: 'workflow',
            error_message: 'Workflow cancelled by user',
            error_code: 'WORKFLOW_CANCELLED',
            timestamp: new Date().toISOString(),
            recoverable: false
          }
        ]
      };

      await this.saveWorkflowState(workflowId, cancelledState);
      return true;
    }

    return false;
  }

  // List all active workflows
  public listActiveWorkflows(): string[] {
    return Array.from(this.workflowStates.entries())
      .filter(([_, state]) => state.status === 'running')
      .map(([workflowId, _]) => workflowId);
  }

  // Get workflow summary
  public getWorkflowSummary(workflowId: string): any {
    const state = this.workflowStates.get(workflowId);
    
    if (!state) {
      return null;
    }

    return {
      workflow_id: state.workflow_id,
      user_id: state.user_id,
      status: state.status,
      progress: state.progress,
      current_step: state.current_step,
      started_at: state.started_at,
      completed_at: state.completed_at,
      processing_time: state.processing_time,
      total_tokens_used: state.total_tokens_used,
      error_count: state.errors.length,
      actions_performed: {
        navigation: state.navigation_actions.length,
        database_queries: state.database_queries.length,
        knowledge_searches: state.knowledge_searches.length
      }
    };
  }
}
