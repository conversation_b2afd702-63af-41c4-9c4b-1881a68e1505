"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const scopingAi_1 = __importDefault(require("../routes/scopingAi"));
// Create the backend plugin for ScopingAI
const scopingAiBackendPlugin = {
    router: scopingAi_1.default,
    config: {
        name: 'ScopingAI API',
        version: '1.0.0',
        apiPrefix: '/api/scopingai'
    },
    initialize: async () => {
        console.log('🎯 ScopingAI backend plugin initialized');
    },
    cleanup: async () => {
        console.log('🎯 ScopingAI backend plugin cleaned up');
    },
    healthCheck: async () => {
        try {
            // Basic health check - could be expanded to check database connectivity
            return true;
        }
        catch (error) {
            console.error('ScopingAI backend health check failed:', error);
            return false;
        }
    }
};
exports.default = scopingAiBackendPlugin;
//# sourceMappingURL=index.js.map