{"version": 3, "file": "KnowledgeRetrievalNode.js", "sourceRoot": "", "sources": ["../../../../../../server/features/scopingAi/langgraph/nodes/KnowledgeRetrievalNode.ts"], "names": [], "mappings": ";AAAA,wDAAwD;AACxD,iDAAiD;AACjD,wDAAwD;;;AAKxD,MAAa,sBAAsB;IAAnC;QACE,SAAI,GAAG,qBAAqB,CAAC;QAC7B,gBAAW,GAAG,iFAAiF,CAAC;IAuSlG,CAAC;IArSC,KAAK,CAAC,OAAO,CAAC,OAAwB;QACpC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;QAEzC,MAAM,CAAC,IAAI,CAAC,mCAAmC,EAAE;YAC/C,WAAW,EAAE,KAAK,CAAC,WAAW;YAC9B,kBAAkB,EAAE,KAAK,CAAC,4BAA4B,EAAE,MAAM,IAAI,CAAC;YACnE,gBAAgB,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,qBAAqB,IAAI,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC;SAC5E,CAAC,CAAC;QAEH,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,IAAI,eAA6C,CAAC;YAElD,IAAI,KAAK,CAAC,4BAA4B,IAAI,KAAK,CAAC,4BAA4B,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxF,gDAAgD;gBAChD,eAAe,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;YAC7E,CAAC;iBAAM,CAAC;gBACN,sDAAsD;gBACtD,eAAe,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;YAC5E,CAAC;YAED,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE9C,MAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE;gBAC3C,eAAe,EAAE,eAAe,CAAC,mBAAmB,CAAC,MAAM;gBAC3D,cAAc,EAAE,eAAe,CAAC,gBAAgB,CAAC,MAAM;gBACvD,eAAe,EAAE,cAAc;aAChC,CAAC,CAAC;YAEH,OAAO;gBACL,sBAAsB,EAAE;oBACtB,mBAAmB,EAAE,eAAe,CAAC,mBAAmB;oBACxD,gBAAgB,EAAE,eAAe,CAAC,gBAAgB;oBAClD,cAAc,EAAE,eAAe,CAAC,cAAc;oBAC9C,eAAe,EAAE,eAAe,CAAC,eAAe;iBACjD;gBACD,YAAY,EAAE,+BAA+B;gBAC7C,QAAQ,EAAE,EAAE;gBACZ,eAAe,EAAE,CAAC,KAAK,CAAC,eAAe,IAAI,CAAC,CAAC,GAAG,cAAc;gBAC9D,iBAAiB,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,iBAAiB,IAAI,EAAE,CAAC,EAAE,gBAAgB,CAAC;gBACzE,cAAc,EAAE;oBACd,GAAG,CAAC,KAAK,CAAC,cAAc,IAAI,EAAE,CAAC;oBAC/B;wBACE,QAAQ,EAAE,gBAAgB;wBAC1B,QAAQ,EAAE,wBAAwB;wBAClC,UAAU,EAAE,CAAC;wBACb,YAAY,EAAE,GAAG;wBACjB,qBAAqB,EAAE,cAAc;wBACrC,aAAa,EAAE,CAAC;wBAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qBACpC;iBACF;gBACD,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACtC,SAAS,EAAE;oBACT,GAAG,KAAK,CAAC,SAAS;oBAClB,mBAAmB,EAAE,eAAe;iBACrC;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAElD,iEAAiE;YACjE,MAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;YAEzD,OAAO;gBACL,sBAAsB,EAAE;oBACtB,mBAAmB,EAAE,EAAE;oBACvB,gBAAgB,EAAE,EAAE;oBACpB,cAAc,EAAE,EAAE;oBAClB,eAAe,EAAE,qCAAqC;iBACvD;gBACD,YAAY,EAAE,+BAA+B;gBAC7C,QAAQ,EAAE,EAAE;gBACZ,QAAQ,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,IAAI,EAAE,CAAC,EAAE,wEAAwE,CAAC;gBAC/G,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACvC,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,uBAAuB,CACnC,KAA6B,EAC7B,KAAU,EACV,MAAW;QAEX,MAAM,CAAC,IAAI,CAAC,yCAAyC,EAAE;YACrD,cAAc,EAAE,KAAK,CAAC,4BAA4B,CAAC,MAAM;YACzD,kBAAkB,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,MAAM;SACpE,CAAC,CAAC;QAEH,IAAI,CAAC;YACH,0CAA0C;YAC1C,MAAM,YAAY,GAAG,MAAM,KAAK,CAAC,aAAa,CAAC,oBAAoB,CACjE,KAAK,CAAC,qBAAqB,EAC3B,KAAK,CAAC,OAAO,EACb,KAAK,CAAC,4BAA4B,CACnC,CAAC;YAEF,yBAAyB;YACzB,MAAM,mBAAmB,GAAG,YAAY,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,MAAW,EAAE,KAAa,EAAE,EAAE,CAAC,CAAC;gBAC1F,EAAE,EAAE,MAAM,CAAC,UAAU;gBACrB,KAAK,EAAE,eAAe,KAAK,GAAG,CAAC,KAAK,MAAM,CAAC,WAAW,EAAE;gBACxD,IAAI,EAAE,iBAAiB;gBACvB,OAAO,EAAE,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;gBAC9E,IAAI,EAAE,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,KAAa,EAAE,KAAU,EAAE,EAAE,CAAC,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;gBAClG,gBAAgB,EAAE,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC;gBAC1F,YAAY,EAAE,MAAM,CAAC,cAAc,CAAC,MAAM;gBAC1C,gBAAgB,EAAE,MAAM,CAAC,WAAW;aACrC,CAAC,CAAC,CAAC;YAEJ,yBAAyB;YACzB,MAAM,eAAe,GAAG,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,EAAE,YAAY,CAAC,CAAC;YAErF,MAAM,iBAAiB,GAAG;gBACxB,eAAe,EAAE,mBAAmB,CAAC,MAAM;gBAC3C,oBAAoB,EAAE,YAAY,CAAC,eAAe,CAAC,MAAM;gBACzD,uBAAuB,EAAE,IAAI,CAAC,yBAAyB,CAAC,mBAAmB,CAAC;gBAC5E,cAAc,EAAE,CAAC,CAAC,2CAA2C;aAC9D,CAAC;YAEF,MAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE;gBAC3C,mBAAmB,EAAE,mBAAmB,CAAC,MAAM;gBAC/C,oBAAoB,EAAE,YAAY,CAAC,eAAe,CAAC,MAAM;aAC1D,CAAC,CAAC;YAEH,OAAO;gBACL,mBAAmB;gBACnB,gBAAgB,EAAE,YAAY,CAAC,eAAe;gBAC9C,cAAc,EAAE,YAAY,CAAC,aAAa;gBAC1C,eAAe;gBACf,iBAAiB;aAClB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAClC,KAA6B,EAC7B,KAAU,EACV,MAAW;QAEX,MAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QAEtD,IAAI,CAAC;YACH,yBAAyB;YACzB,MAAM,aAAa,GAAG,MAAM,KAAK,CAAC,aAAa,CAAC,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAEhF,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC/B,MAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;gBAC3C,OAAO;oBACL,mBAAmB,EAAE,EAAE;oBACvB,gBAAgB,EAAE,EAAE;oBACpB,cAAc,EAAE,EAAE;oBAClB,eAAe,EAAE,0CAA0C;oBAC3D,iBAAiB,EAAE;wBACjB,eAAe,EAAE,CAAC;wBAClB,oBAAoB,EAAE,CAAC;wBACvB,uBAAuB,EAAE,CAAC;wBAC1B,cAAc,EAAE,CAAC;qBAClB;iBACF,CAAC;YACJ,CAAC;YAED,+DAA+D;YAC/D,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;YAEzD,0BAA0B;YAC1B,MAAM,YAAY,GAAG,MAAM,KAAK,CAAC,aAAa,CAAC,oBAAoB,CACjE,EAAE,OAAO,EAAE,WAAW,EAAE,EACxB,KAAK,CAAC,OAAO,CACd,CAAC;YAEF,MAAM,mBAAmB,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAQ,EAAE,KAAa,EAAE,EAAE,CAAC,CAAC;gBACtF,EAAE,EAAE,GAAG,CAAC,EAAE;gBACV,KAAK,EAAE,GAAG,CAAC,KAAK,IAAI,YAAY,KAAK,GAAG,CAAC,EAAE;gBAC3C,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,UAAU;gBAC5B,OAAO,EAAE,GAAG,CAAC,OAAO,IAAI,EAAE;gBAC1B,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC;gBACnB,gBAAgB,EAAE,CAAC,EAAE,CAAC,EAAE,0CAA0C;gBAClE,YAAY,EAAE,CAAC;gBACf,gBAAgB,EAAE,yBAAyB;aAC5C,CAAC,CAAC,CAAC;YAEJ,MAAM,gBAAgB,GAAG,KAAK,CAAC,aAAa,CAAC,oBAAoB,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC7F,MAAM,eAAe,GAAG,aAAa,mBAAmB,CAAC,MAAM,wCAAwC,CAAC;YAExG,MAAM,iBAAiB,GAAG;gBACxB,eAAe,EAAE,mBAAmB,CAAC,MAAM;gBAC3C,oBAAoB,EAAE,gBAAgB,CAAC,MAAM;gBAC7C,uBAAuB,EAAE,EAAE;gBAC3B,cAAc,EAAE,CAAC;aAClB,CAAC;YAEF,MAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE;gBAC1C,mBAAmB,EAAE,mBAAmB,CAAC,MAAM;gBAC/C,oBAAoB,EAAE,gBAAgB,CAAC,MAAM;aAC9C,CAAC,CAAC;YAEH,OAAO;gBACL,mBAAmB;gBACnB,gBAAgB;gBAChB,cAAc,EAAE,CAAC,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,cAAc,EAAE,EAAE,EAAE,CAAC;gBACzF,eAAe;gBACf,iBAAiB;aAClB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACjD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,wBAAwB,CAAC,KAA6B;QAC5D,MAAM,UAAU,GAAG,EAAE,CAAC;QAEtB,IAAI,KAAK,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC;YAC3B,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,EAAE,WAAW,EAAE,CAAC;YAC/B,6CAA6C;YAC7C,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW;iBACvC,KAAK,CAAC,KAAK,CAAC;iBACZ,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;iBAC/B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACf,UAAU,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC;QAC/B,CAAC;QAED,IAAI,KAAK,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC;YAC7B,wCAAwC;YACxC,UAAU,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC;QAED,OAAO,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC9B,CAAC;IAEO,oBAAoB,CAAC,SAAgB,EAAE,YAAiB;QAC9D,MAAM,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC;QACnC,MAAM,WAAW,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,YAAY,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACrF,MAAM,YAAY,GAAG,IAAI,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAC;QAC/D,MAAM,oBAAoB,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,MAAM,CAAC;QAElF,OAAO,aAAa,SAAS,mBAAmB,WAAW,oBAAoB;YACxE,sBAAsB,YAAY,KAAK;YACvC,GAAG,oBAAoB,8CAA8C;YACrE,kBAAkB,YAAY,CAAC,eAAe,EAAE,MAAM,IAAI,CAAC,cAAc,CAAC;IACnF,CAAC;IAEO,yBAAyB,CAAC,SAAgB;QAChD,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAErC,MAAM,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,gBAAgB,IAAI,EAAE,CAAC,CAAC;QACvE,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAErC,MAAM,GAAG,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,KAAK,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC;QACjE,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;IAC5C,CAAC;IAED,qCAAqC;IAC7B,sBAAsB,CAAC,OAAe,EAAE,YAAoB,GAAG;QACrE,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,SAAS,EAAE,CAAC;YAClD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,4EAA4E;QAC5E,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QAC3E,OAAO,WAAW,GAAG,EAAE,CAAC;IAC1B,CAAC;IAED,kCAAkC;IAC1B,gBAAgB,CAAC,OAAe,EAAE,YAAoB,EAAE;QAC9D,IAAI,CAAC,OAAO;YAAE,OAAO,EAAE,CAAC;QAExB,wEAAwE;QACxE,MAAM,KAAK,GAAG,OAAO;aAClB,WAAW,EAAE;aACb,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC;aACxB,KAAK,CAAC,KAAK,CAAC;aACZ,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAEnC,MAAM,UAAU,GAAG,IAAI,GAAG,EAAkB,CAAC;QAC7C,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACnB,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;aACpC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;aAC3B,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC;aACnB,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;CACF;AAzSD,wDAySC"}