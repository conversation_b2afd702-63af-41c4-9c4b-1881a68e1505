"use strict";
// =====================================================
// BACKLINK ANALYSIS AGENT - DOMAIN AUTHORITY & BACKLINKS
// =====================================================
Object.defineProperty(exports, "__esModule", { value: true });
exports.BacklinkAnalysisAgent = void 0;
const BaseAgent_1 = require("../core/BaseAgent");
const ProviderConfigService_1 = require("../../services/external/ProviderConfigService");
class BacklinkAnalysisAgent extends BaseAgent_1.BaseAgent {
    constructor() {
        super('BacklinkAnalysisAgent', 'Analyzes website backlink profile and authority using premium providers', ['backlink_analysis', 'domain_authority_check']);
    }
    async execute(context) {
        return this.executeWithMetrics(context, async () => {
            // Validate configuration and tools
            this.validateConfig(context);
            this.checkRequiredTools(context);
            const input = context.job.result_data;
            if (!this.validateInput(input)) {
                return this.createErrorResult('Invalid input parameters');
            }
            const website = context.website;
            const params = input.parameters;
            context.logger.info('Starting backlink analysis', {
                website_id: website.id,
                domain: website.domain,
                data_sources: params.data_sources
            });
            // Get configured provider for backlink analysis
            const providerConfig = ProviderConfigService_1.providerConfigService.getProviderForFunction('backlink');
            if (!providerConfig) {
                context.logger.warn('No backlink provider configured', {
                    message: 'Configure a backlink provider (Semrush, Ahrefs) by setting SEO_BACKLINK_PROVIDER and SEO_BACKLINK_API_KEY environment variables'
                });
                return this.createSuccessResult({
                    backlinks: [],
                    domain_metrics: {
                        domain_authority: 0,
                        page_authority: 0,
                        spam_score: 0,
                        total_backlinks: 0,
                        referring_domains: 0
                    },
                    competitor_backlinks: [],
                    link_opportunities: [],
                    total_backlinks: 0,
                    analysis_errors: ['No backlink provider configured'],
                    provider_used: 'none'
                }, {
                    data_points_processed: 0,
                    api_calls_made: 0
                });
            }
            context.logger.info(`Using ${providerConfig.provider} for backlink analysis`);
            const backlinkData = {};
            const analysisErrors = [];
            let totalBacklinks = 0;
            try {
                // Step 1: Domain Authority Analysis
                await this.reportProgress(context, 10, 'Analyzing domain authority');
                const domainMetrics = await this.analyzeDomainAuthority(website.domain, providerConfig, context);
                backlinkData.domain_metrics = domainMetrics;
                await this.reportProgress(context, 25, 'Domain authority analysis completed');
                // Step 2: Backlink Profile Analysis
                await this.reportProgress(context, 30, 'Analyzing backlink profile');
                const backlinkProfile = await this.analyzeBacklinkProfile(website.domain, params.max_backlinks || 1000, providerConfig, context);
                backlinkData.backlinks = backlinkProfile.backlinks;
                totalBacklinks = backlinkProfile.total_backlinks || 0;
                await this.reportProgress(context, 60, `Found ${totalBacklinks} backlinks`);
                // Step 3: Competitor Analysis
                if (params.include_competitor_analysis && params.competitor_domains) {
                    await this.reportProgress(context, 65, 'Analyzing competitor backlinks');
                    const competitorBacklinks = await this.analyzeCompetitorBacklinks(website.domain, params.competitor_domains, providerConfig, context);
                    backlinkData.competitor_backlinks = competitorBacklinks;
                    await this.reportProgress(context, 85, 'Competitor analysis completed');
                }
                // Step 4: Link Building Opportunities
                await this.reportProgress(context, 90, 'Identifying link building opportunities');
                const linkOpportunities = await this.identifyLinkOpportunities(website.domain, backlinkData, context);
                backlinkData.link_opportunities = linkOpportunities;
                // Step 5: Save to Database
                await this.reportProgress(context, 95, 'Saving backlink analysis');
                await this.saveBacklinkAnalysis(website.id, backlinkData, context);
                await this.reportProgress(context, 100, 'Backlink analysis completed');
                return this.createSuccessResult({
                    backlinks: backlinkData.backlinks || [],
                    domain_metrics: backlinkData.domain_metrics,
                    competitor_backlinks: backlinkData.competitor_backlinks || [],
                    link_opportunities: backlinkData.link_opportunities || [],
                    total_backlinks: totalBacklinks,
                    analysis_errors: analysisErrors,
                    provider_used: providerConfig.provider
                }, {
                    data_points_processed: totalBacklinks,
                    api_calls_made: this.calculateApiCalls(params.data_sources, params.competitor_domains?.length || 0)
                });
            }
            catch (error) {
                const agentError = this.handleError(error, 'Backlink analysis failed');
                return this.createErrorResult(agentError.message);
            }
        });
    }
    validateInput(input) {
        this.validateCommonInput(input);
        const params = input.parameters;
        if (!params.data_sources || params.data_sources.length === 0) {
            throw new Error('At least one data source must be specified');
        }
        const validSources = ['moz', 'majestic', 'ahrefs'];
        const invalidSources = params.data_sources.filter(s => !validSources.includes(s));
        if (invalidSources.length > 0) {
            throw new Error(`Invalid data sources: ${invalidSources.join(', ')}`);
        }
        if (params.max_backlinks && params.max_backlinks <= 0) {
            throw new Error('max_backlinks must be a positive number');
        }
        if (params.competitor_domains && params.competitor_domains.some(c => !this.isValidUrl(c))) {
            throw new Error('All competitor domains must be valid URLs');
        }
        return true;
    }
    getRequiredTools() {
        return ['http', 'database', 'cache'];
    }
    // =====================================================
    // BACKLINK ANALYSIS METHODS
    // =====================================================
    async analyzeDomainAuthority(domain, providerConfig, context) {
        try {
            if (providerConfig.provider === 'semrush') {
                return await this.getSemrushDomainAuthority(domain, providerConfig.config, context);
            }
            else if (providerConfig.provider === 'ahrefs') {
                return await this.getAhrefsDomainAuthority(domain, providerConfig.config, context);
            }
            else {
                throw new Error(`Provider ${providerConfig.provider} not supported for domain authority`);
            }
        }
        catch (error) {
            context.logger.error('Failed to analyze domain authority', error, { domain });
            throw error;
        }
    }
    async analyzeBacklinkProfile(domain, maxBacklinks, providerConfig, context) {
        try {
            if (providerConfig.provider === 'semrush') {
                return await this.getSemrushBacklinks(domain, maxBacklinks, providerConfig.config, context);
            }
            else if (providerConfig.provider === 'ahrefs') {
                return await this.getAhrefsBacklinks(domain, maxBacklinks, providerConfig.config, context);
            }
            else {
                throw new Error(`Provider ${providerConfig.provider} not supported for backlink analysis`);
            }
        }
        catch (error) {
            context.logger.error('Failed to analyze backlink profile', error, { domain });
            throw error;
        }
    }
    async analyzeCompetitorBacklinks(domain, competitors, providerConfig, context) {
        const competitorData = [];
        for (const competitor of competitors) {
            try {
                const competitorDomain = this.extractDomain(competitor);
                const competitorBacklinks = await this.analyzeBacklinkProfile(competitorDomain, 500, // Limit competitor analysis
                providerConfig, context);
                competitorData.push(...competitorBacklinks.backlinks);
            }
            catch (error) {
                context.logger.warn(`Failed to analyze competitor: ${competitor}`, { error });
            }
        }
        return competitorData;
    }
    async identifyLinkOpportunities(domain, backlinkData, context) {
        // Analyze existing data to identify opportunities
        const opportunities = [];
        // Identify high-authority domains without backlinks
        if (backlinkData.competitor_backlinks && backlinkData.competitor_backlinks.length > 0) {
            const competitorDomains = new Set();
            backlinkData.competitor_backlinks.forEach((link) => {
                competitorDomains.add(this.extractDomain(link.source_url));
            });
            const ownReferringDomains = new Set(backlinkData.backlinks?.map((link) => this.extractDomain(link.source_url)) || []);
            // Find domains that link to competitors but not to us
            competitorDomains.forEach((compDomain) => {
                if (!ownReferringDomains.has(compDomain)) {
                    opportunities.push({
                        source_domain: compDomain,
                        opportunity_type: 'competitor_link',
                        target_page: '/',
                        anchor_text_suggestion: domain,
                        difficulty_score: 50, // Default score
                        estimated_value: 75
                    });
                }
            });
        }
        return opportunities;
    }
    // =====================================================
    // PROVIDER-SPECIFIC METHODS
    // =====================================================
    async getSemrushDomainAuthority(domain, config, context) {
        const url = `https://api.semrush.com/analytics/v1/?type=domain_overview&key=${config.apiKey}&domain=${domain}&database=us`;
        const response = await context.tools.http.get(url, {
            timeout: config.timeout
        });
        // Parse Semrush response and extract domain authority metrics
        const data = response.data;
        return {
            domain_authority: data.authority_score || 0,
            page_authority: 0, // Not available in domain overview
            spam_score: 0, // Not available in Semrush
            total_backlinks: data.backlinks || 0,
            referring_domains: data.referring_domains || 0
        };
    }
    async getSemrushBacklinks(domain, maxBacklinks, config, context) {
        const url = `https://api.semrush.com/analytics/v1/?type=backlinks&key=${config.apiKey}&target=${domain}&target_type=root_domain&limit=${Math.min(maxBacklinks, 10000)}`;
        const response = await context.tools.http.get(url, {
            timeout: config.timeout
        });
        // Parse and structure backlink data
        const data = response.data;
        const backlinks = data.map((link) => ({
            source_domain: this.extractDomain(link.source_url),
            source_url: link.source_url,
            target_url: link.target_url,
            anchor_text: link.anchor_text,
            link_type: 'unknown',
            domain_authority: link.authority_score,
            page_authority: 0,
            spam_score: 0,
            first_seen: link.crawl_date,
            last_seen: link.crawl_date,
            status: 'active',
            data_source: 'ahrefs' // Map to expected enum value
        }));
        return {
            backlinks,
            total_backlinks: backlinks.length
        };
    }
    async getAhrefsDomainAuthority(domain, config, context) {
        // Placeholder for Ahrefs implementation
        throw new Error('Ahrefs integration not yet implemented');
    }
    async getAhrefsBacklinks(domain, maxBacklinks, config, context) {
        // Placeholder for Ahrefs implementation
        throw new Error('Ahrefs integration not yet implemented');
    }
    // =====================================================
    // UTILITY METHODS
    // =====================================================
    calculateApiCalls(dataSources, competitorCount) {
        let calls = 1; // Domain authority call
        calls += 1; // Backlink profile call
        calls += competitorCount; // Competitor analysis calls
        return calls;
    }
    async saveBacklinkAnalysis(websiteId, backlinkData, context) {
        try {
            // Save to pseo_backlink_analysis table
            await context.tools.database.query(`
        INSERT INTO pseo_backlink_analysis (
          website_id,
          analysis_data,
          total_backlinks,
          domain_authority,
          referring_domains,
          created_at,
          updated_at
        ) VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
        ON CONFLICT (website_id) 
        DO UPDATE SET 
          analysis_data = $2,
          total_backlinks = $3,
          domain_authority = $4,
          referring_domains = $5,
          updated_at = NOW()
      `, [
                websiteId,
                JSON.stringify(backlinkData),
                backlinkData.backlinks?.length || 0,
                backlinkData.domain_metrics?.domain_authority || 0,
                backlinkData.domain_metrics?.referring_domains || 0
            ]);
            context.logger.info('Backlink analysis saved to database', {
                website_id: websiteId,
                total_backlinks: backlinkData.backlinks?.length || 0
            });
        }
        catch (error) {
            context.logger.error('Failed to save backlink analysis', error, { website_id: websiteId });
            throw error;
        }
    }
}
exports.BacklinkAnalysisAgent = BacklinkAnalysisAgent;
//# sourceMappingURL=BacklinkAnalysisAgent.js.map