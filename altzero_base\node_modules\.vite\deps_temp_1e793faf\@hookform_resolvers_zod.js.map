{"version": 3, "sources": ["../../@hookform/resolvers/src/validateFieldsNatively.ts", "../../@hookform/resolvers/src/toNestErrors.ts", "../../@hookform/resolvers/zod/src/zod.ts"], "sourcesContent": ["import {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  Ref,\n  ResolverOptions,\n  get,\n} from 'react-hook-form';\n\nconst setCustomValidity = (\n  ref: Ref,\n  fieldPath: string,\n  errors: FieldErrors,\n) => {\n  if (ref && 'reportValidity' in ref) {\n    const error = get(errors, fieldPath) as FieldError | undefined;\n    ref.setCustomValidity((error && error.message) || '');\n\n    ref.reportValidity();\n  }\n};\n\n// Native validation (web only)\nexport const validateFieldsNatively = <TFieldValues extends FieldValues>(\n  errors: FieldErrors,\n  options: ResolverOptions<TFieldValues>,\n): void => {\n  for (const fieldPath in options.fields) {\n    const field = options.fields[fieldPath];\n    if (field && field.ref && 'reportValidity' in field.ref) {\n      setCustomValidity(field.ref, fieldPath, errors);\n    } else if (field && field.refs) {\n      field.refs.forEach((ref: HTMLInputElement) =>\n        setCustomValidity(ref, fieldPath, errors),\n      );\n    }\n  }\n};\n", "import {\n  Field,\n  FieldErrors,\n  FieldValues,\n  InternalFieldName,\n  ResolverOptions,\n  get,\n  set,\n} from 'react-hook-form';\nimport { validateFieldsNatively } from './validateFieldsNatively';\n\nexport const toNestErrors = <TFieldValues extends FieldValues>(\n  errors: FieldErrors,\n  options: ResolverOptions<TFieldValues>,\n): FieldErrors<TFieldValues> => {\n  options.shouldUseNativeValidation && validateFieldsNatively(errors, options);\n\n  const fieldErrors = {} as FieldErrors<TFieldValues>;\n  for (const path in errors) {\n    const field = get(options.fields, path) as Field['_f'] | undefined;\n    const error = Object.assign(errors[path] || {}, {\n      ref: field && field.ref,\n    });\n\n    if (isNameInFieldArray(options.names || Object.keys(errors), path)) {\n      const fieldArrayErrors = Object.assign({}, get(fieldErrors, path));\n\n      set(fieldArrayErrors, 'root', error);\n      set(fieldErrors, path, fieldArrayErrors);\n    } else {\n      set(fieldErrors, path, error);\n    }\n  }\n\n  return fieldErrors;\n};\n\nconst isNameInFieldArray = (\n  names: InternalFieldName[],\n  name: InternalFieldName,\n) => {\n  const path = escapeBrackets(name);\n  return names.some((n) => escapeBrackets(n).match(`^${path}\\\\.\\\\d+`));\n};\n\n/**\n * Escapes special characters in a string to be used in a regex pattern.\n * it removes the brackets from the string to match the `set` method.\n *\n * @param input - The input string to escape.\n * @returns The escaped string.\n */\nfunction escapeBrackets(input: string): string {\n  return input.replace(/\\]|\\[/g, '');\n}\n", "import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  Resolver,\n  ResolverError,\n  ResolverSuccess,\n  appendErrors,\n} from 'react-hook-form';\nimport { ZodError, z } from 'zod';\n\nconst isZodError = (error: any): error is ZodError =>\n  Array.isArray(error?.errors);\n\nfunction parseErrorSchema(\n  zodErrors: z.ZodIssue[],\n  validateAllFieldCriteria: boolean,\n) {\n  const errors: Record<string, FieldError> = {};\n  for (; zodErrors.length; ) {\n    const error = zodErrors[0];\n    const { code, message, path } = error;\n    const _path = path.join('.');\n\n    if (!errors[_path]) {\n      if ('unionErrors' in error) {\n        const unionError = error.unionErrors[0].errors[0];\n\n        errors[_path] = {\n          message: unionError.message,\n          type: unionError.code,\n        };\n      } else {\n        errors[_path] = { message, type: code };\n      }\n    }\n\n    if ('unionErrors' in error) {\n      error.unionErrors.forEach((unionError) =>\n        unionError.errors.forEach((e) => zodErrors.push(e)),\n      );\n    }\n\n    if (validateAllFieldCriteria) {\n      const types = errors[_path].types;\n      const messages = types && types[error.code];\n\n      errors[_path] = appendErrors(\n        _path,\n        validateAllFieldCriteria,\n        errors,\n        code,\n        messages\n          ? ([] as string[]).concat(messages as string[], error.message)\n          : error.message,\n      ) as FieldError;\n    }\n\n    zodErrors.shift();\n  }\n\n  return errors;\n}\n\nexport function zodResolver<Input extends FieldValues, Context, Output>(\n  schema: z.ZodSchema<Output, any, Input>,\n  schemaOptions?: Partial<z.ParseParams>,\n  resolverOptions?: {\n    mode?: 'async' | 'sync';\n    raw?: false;\n  },\n): Resolver<Input, Context, Output>;\n\nexport function zodResolver<Input extends FieldValues, Context, Output>(\n  schema: z.ZodSchema<Output, any, Input>,\n  schemaOptions: Partial<z.ParseParams> | undefined,\n  resolverOptions: {\n    mode?: 'async' | 'sync';\n    raw: true;\n  },\n): Resolver<Input, Context, Input>;\n\n/**\n * Creates a resolver function for react-hook-form that validates form data using a Zod schema\n * @param {z.ZodSchema<Input>} schema - The Zod schema used to validate the form data\n * @param {Partial<z.ParseParams>} [schemaOptions] - Optional configuration options for Zod parsing\n * @param {Object} [resolverOptions] - Optional resolver-specific configuration\n * @param {('async'|'sync')} [resolverOptions.mode='async'] - Validation mode. Use 'sync' for synchronous validation\n * @param {boolean} [resolverOptions.raw=false] - If true, returns the raw form values instead of the parsed data\n * @returns {Resolver<z.output<typeof schema>>} A resolver function compatible with react-hook-form\n * @throws {Error} Throws if validation fails with a non-Zod error\n * @example\n * const schema = z.object({\n *   name: z.string().min(2),\n *   age: z.number().min(18)\n * });\n *\n * useForm({\n *   resolver: zodResolver(schema)\n * });\n */\nexport function zodResolver<Input extends FieldValues, Context, Output>(\n  schema: z.ZodSchema<Output, any, Input>,\n  schemaOptions?: Partial<z.ParseParams>,\n  resolverOptions: {\n    mode?: 'async' | 'sync';\n    raw?: boolean;\n  } = {},\n): Resolver<Input, Context, Output | Input> {\n  return async (values: Input, _, options) => {\n    try {\n      const data = await schema[\n        resolverOptions.mode === 'sync' ? 'parse' : 'parseAsync'\n      ](values, schemaOptions);\n\n      options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n      return {\n        errors: {} as FieldErrors,\n        values: resolverOptions.raw ? Object.assign({}, values) : data,\n      } satisfies ResolverSuccess<Output | Input>;\n    } catch (error) {\n      if (isZodError(error)) {\n        return {\n          values: {},\n          errors: toNestErrors(\n            parseErrorSchema(\n              error.errors,\n              !options.shouldUseNativeValidation &&\n                options.criteriaMode === 'all',\n            ),\n            options,\n          ),\n        } satisfies ResolverError<Input>;\n      }\n\n      throw error;\n    }\n  };\n}\n"], "mappings": ";;;;;;;;;AASA,IAAMA,IAAoBA,CACxBC,GACAC,IACAC,OAAAA;AAEA,MAAIF,KAAO,oBAAoBA,GAAK;AAClC,UAAMG,KAAQC,IAAIF,IAAQD,EAAAA;AAC1BD,MAAID,kBAAmBI,MAASA,GAAME,WAAY,EAAA,GAElDL,EAAIM,eAAAA;EACN;AAAA;AAVF,IAcaC,IAAyBA,CACpCL,GACAM,MAAAA;AAEA,aAAWP,MAAaO,EAAQC,QAAQ;AACtC,UAAMC,KAAQF,EAAQC,OAAOR,EAAAA;AACzBS,IAAAA,MAASA,GAAMV,OAAO,oBAAoBU,GAAMV,MAClDD,EAAkBW,GAAMV,KAAKC,IAAWC,CAAAA,IAC/BQ,MAASA,GAAMC,QACxBD,GAAMC,KAAKC,QAASZ,CAAAA,OAClBD,EAAkBC,IAAKC,IAAWC,CAAAA,CAAAA;EAGxC;AAAA;AA3BF,ICEaW,IAAeA,CAC1BX,IACAM,OAAAA;AAEAA,EAAAA,GAAQM,6BAA6BP,EAAuBL,IAAQM,EAAAA;AAEpE,QAAMO,KAAc,CAAA;AACpB,aAAWC,MAAQd,IAAQ;AACzB,UAAMQ,IAAQN,IAAII,GAAQC,QAAQO,EAAAA,GAC5Bb,IAAQc,OAAOC,OAAOhB,GAAOc,EAAAA,KAAS,CAAA,GAAI,EAC9ChB,KAAKU,KAASA,EAAMV,IAAAA,CAAAA;AAGtB,QAAImB,EAAmBX,GAAQY,SAASH,OAAOI,KAAKnB,EAAAA,GAASc,EAAAA,GAAO;AAClE,YAAMM,KAAmBL,OAAOC,OAAO,CAAA,GAAId,IAAIW,IAAaC,EAAAA,CAAAA;AAE5DO,UAAID,IAAkB,QAAQnB,CAAAA,GAC9BoB,IAAIR,IAAaC,IAAMM,EAAAA;IACzB;AACEC,UAAIR,IAAaC,IAAMb,CAAAA;EAE3B;AAEA,SAAOY;AAAAA;ADzBT,IC4BMI,IAAqBA,CACzBC,GACAI,MAAAA;AAEA,QAAMR,KAAOS,EAAeD,CAAAA;AAC5B,SAAOJ,EAAMM,KAAMC,CAAAA,OAAMF,EAAeE,EAAAA,EAAGC,MAAM,IAAIZ,EAAAA,SAAAA,CAAAA;AAAc;AAUrE,SAASS,EAAeI,GAAAA;AACtB,SAAOA,EAAMC,QAAQ,UAAU,EAAA;AACjC;;;ACvCA,SAASC,GACPC,IACAC,GAAAA;AAGA,WADMC,KAAqC,CAAE,GACtCF,GAAUG,UAAU;AACzB,QAAMC,KAAQJ,GAAU,CAAA,GAChBK,IAAwBD,GAAxBC,MAAMC,KAAkBF,GAAlBE,SACRC,IAD0BH,GAATI,KACJC,KAAK,GAAA;AAExB,QAAA,CAAKP,GAAOK,CAAAA;AACV,UAAI,iBAAiBH,IAAO;AAC1B,YAAMM,IAAaN,GAAMO,YAAY,CAAA,EAAGT,OAAO,CAAA;AAE/CA,QAAAA,GAAOK,CAAAA,IAAS,EACdD,SAASI,EAAWJ,SACpBM,MAAMF,EAAWL,KAAAA;MAErB;AACEH,QAAAA,GAAOK,CAAAA,IAAS,EAAED,SAAAA,IAASM,MAAMP,EAAAA;AAUrC,QANI,iBAAiBD,MACnBA,GAAMO,YAAYE,QAAQ,SAACH,IAAAA;AACzB,aAAAA,GAAWR,OAAOW,QAAQ,SAACC,IAAAA;AAAC,eAAKd,GAAUe,KAAKD,EAAAA;MAAE,CAAA;IAAC,CAAA,GAInDb,GAA0B;AAC5B,UAAMe,IAAQd,GAAOK,CAAAA,EAAOS,OACtBC,IAAWD,KAASA,EAAMZ,GAAMC,IAAAA;AAEtCH,MAAAA,GAAOK,CAAAA,IAASW,aACdX,GACAN,GACAC,IACAG,GACAY,IACK,CAAA,EAAgBE,OAAOF,GAAsBb,GAAME,OAAAA,IACpDF,GAAME,OAAAA;IAEd;AAEAN,IAAAA,GAAUoB,MAAAA;EACZ;AAEA,SAAOlB;AACT;AAuCM,SAAUmB,GACdC,IACAC,IACAC,GAAAA;AAKA,SAAA,WALAA,MAAAA,IAGI,CAAE,IAEQC,SAAAA,IAAeC,GAAGC,GAAAA;AAAW,QAAA;AAAA,aAAAC,QAAAC,QAAAA,SAAAA,GAAAA,IAAAA;AAAAA,YAAAA;AAAAA,cAAAA,KACrCD,QAAAC,QACiBP,GACQ,WAAzBE,EAAgBM,OAAkB,UAAU,YAAA,EAC5CL,IAAQF,EAAAA,CAAAA,EAAcQ,KAFlBC,SAAAA,IAAAA;AAMN,mBAFAL,EAAQM,6BAA6BC,EAAuB,CAAA,GAAIP,CAAAA,GAEzD,EACLzB,QAAQ,CAAA,GACRuB,QAAQD,EAAgBW,MAAMC,OAAOC,OAAO,CAAE,GAAEZ,EAAAA,IAAUO,GAAAA;UAChB,CAAA;QAAA,SAAAM,IAAA;AAAA,iBAAAC,GAAAD,EAAA;QAAA;AAAA,eAAAE,MAAAA,GAAA,OAAAA,GAAA,KAAA,QAAAD,EAAA,IAAAC;MAAA,EAXL,GAYhCpC,SAAAA,IAAAA;AACP,YA/Ga,SAACA,IAAAA;AAClB,iBAAAqC,MAAMC,QAAa,QAALtC,KAAAA,SAAAA,GAAOF,MAAAA;QAAO,EA8GTE,EAAAA;AACb,iBAAO,EACLqB,QAAQ,CAAA,GACRvB,QAAQyC,EACN5C,GACEK,GAAMF,QAAAA,CACLyB,EAAQM,6BACkB,UAAzBN,EAAQiB,YAAAA,GAEZjB,CAAAA,EAAAA;AAKN,cAAMvB;MACR,CAAA,CAAA;IACF,SAACU,IAAAA;AAAAc,aAAAA,QAAAiB,OAAA/B,EAAAA;IACH;EAAA;AAAA;", "names": ["setCustomValidity", "ref", "fieldPath", "errors", "error", "get", "message", "reportValidity", "validateFieldsNatively", "options", "fields", "field", "refs", "for<PERSON>ach", "toNestErrors", "shouldUseNativeValidation", "fieldErrors", "path", "Object", "assign", "isNameInFieldArray", "names", "keys", "fieldArrayErrors", "set", "name", "escapeBrackets", "some", "n", "match", "input", "replace", "parseErrorSchema", "zodErrors", "validateAllFieldCriteria", "errors", "length", "error", "code", "message", "_path", "path", "join", "unionError", "unionErrors", "type", "for<PERSON>ach", "e", "push", "types", "messages", "appendErrors", "concat", "shift", "zodResolver", "schema", "schemaOptions", "resolverOptions", "values", "_", "options", "Promise", "resolve", "mode", "then", "data", "shouldUseNativeValidation", "validateFieldsNatively", "raw", "Object", "assign", "r", "n", "a", "Array", "isArray", "toNestErrors", "criteriaMode", "reject"]}