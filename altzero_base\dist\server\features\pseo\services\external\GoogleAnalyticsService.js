"use strict";
// =====================================================
// GOOGLE ANALYTICS INTEGRATION SERVICE
// Phase 5: External API Integration
// =====================================================
Object.defineProperty(exports, "__esModule", { value: true });
exports.GoogleAnalyticsService = void 0;
const googleapis_1 = require("googleapis");
class GoogleAnalyticsService {
    constructor(config) {
        this.isAuthenticated = false;
        // Initialize OAuth2 client
        this.oauth2Client = new googleapis_1.google.auth.OAuth2(config.clientId, config.clientSecret, config.redirectUri);
        this.propertyId = config.propertyId;
        // Set refresh token if available
        if (config.refreshToken) {
            this.oauth2Client.setCredentials({
                refresh_token: config.refreshToken
            });
            this.isAuthenticated = true;
        }
        // Initialize Analytics APIs
        this.analytics = googleapis_1.google.analytics({
            version: 'v3',
            auth: this.oauth2Client
        });
        this.analyticsData = googleapis_1.google.analyticsdata({
            version: 'v1beta',
            auth: this.oauth2Client
        });
    }
    /**
     * Generate OAuth2 authorization URL
     */
    generateAuthUrl() {
        const scopes = [
            'https://www.googleapis.com/auth/analytics.readonly',
            'https://www.googleapis.com/auth/analytics'
        ];
        return this.oauth2Client.generateAuthUrl({
            access_type: 'offline',
            scope: scopes,
            prompt: 'consent'
        });
    }
    /**
     * Exchange authorization code for tokens
     */
    async exchangeCodeForTokens(authCode) {
        try {
            const { tokens } = await this.oauth2Client.getToken(authCode);
            this.oauth2Client.setCredentials(tokens);
            this.isAuthenticated = true;
            console.log('✅ Google Analytics authentication successful');
        }
        catch (error) {
            console.error('❌ Failed to exchange code for tokens:', error);
            throw new Error('Analytics authentication failed');
        }
    }
    /**
     * Get site overview metrics
     */
    async getSiteOverview(startDate, endDate) {
        if (!this.isAuthenticated) {
            throw new Error('Not authenticated with Google Analytics');
        }
        try {
            const response = await this.analyticsData.properties.runReport({
                property: `properties/${this.propertyId}`,
                requestBody: {
                    dateRanges: [{ startDate, endDate }],
                    metrics: [
                        { name: 'sessions' },
                        { name: 'totalUsers' },
                        { name: 'screenPageViews' },
                        { name: 'bounceRate' },
                        { name: 'averageSessionDuration' },
                        { name: 'conversions' }
                    ]
                }
            });
            const row = response.data.rows?.[0];
            const metrics = row?.metricValues || [];
            const conversionRate = parseFloat(metrics[0]?.value || '0') > 0
                ? (parseFloat(metrics[5]?.value || '0') / parseFloat(metrics[0]?.value || '1')) * 100
                : 0;
            return {
                totalSessions: parseInt(metrics[0]?.value || '0'),
                totalUsers: parseInt(metrics[1]?.value || '0'),
                totalPageviews: parseInt(metrics[2]?.value || '0'),
                averageBounceRate: parseFloat(metrics[3]?.value || '0'),
                averageSessionDuration: parseFloat(metrics[4]?.value || '0'),
                totalConversions: parseInt(metrics[5]?.value || '0'),
                conversionRate,
                dateRange: { start: startDate, end: endDate }
            };
        }
        catch (error) {
            console.error('Failed to fetch Analytics site overview:', error);
            throw error;
        }
    }
    /**
     * Get top performing pages
     */
    async getTopPages(startDate, endDate, limit = 100) {
        if (!this.isAuthenticated) {
            throw new Error('Not authenticated with Google Analytics');
        }
        try {
            const response = await this.analyticsData.properties.runReport({
                property: `properties/${this.propertyId}`,
                requestBody: {
                    dateRanges: [{ startDate, endDate }],
                    dimensions: [
                        { name: 'pagePath' },
                        { name: 'pageTitle' }
                    ],
                    metrics: [
                        { name: 'sessions' },
                        { name: 'screenPageViews' },
                        { name: 'bounceRate' },
                        { name: 'averageSessionDuration' }
                    ],
                    orderBys: [{ metric: { metricName: 'sessions' }, desc: true }],
                    limit
                }
            });
            return response.data.rows?.map((row) => {
                const dimensions = row.dimensionValues || [];
                const metrics = row.metricValues || [];
                return {
                    pagePath: dimensions[0]?.value || '',
                    pageTitle: dimensions[1]?.value || '',
                    sessions: parseInt(metrics[0]?.value || '0'),
                    pageviews: parseInt(metrics[1]?.value || '0'),
                    uniquePageviews: parseInt(metrics[1]?.value || '0'), // Approximation
                    avgTimeOnPage: parseFloat(metrics[3]?.value || '0'),
                    bounceRate: parseFloat(metrics[2]?.value || '0'),
                    exitRate: parseFloat(metrics[2]?.value || '0'), // Approximation
                    entrances: parseInt(metrics[0]?.value || '0') // Approximation
                };
            }) || [];
        }
        catch (error) {
            console.error('Failed to fetch top pages:', error);
            throw error;
        }
    }
    /**
     * Get traffic sources breakdown
     */
    async getTrafficSources(startDate, endDate, limit = 50) {
        if (!this.isAuthenticated) {
            throw new Error('Not authenticated with Google Analytics');
        }
        try {
            const response = await this.analyticsData.properties.runReport({
                property: `properties/${this.propertyId}`,
                requestBody: {
                    dateRanges: [{ startDate, endDate }],
                    dimensions: [
                        { name: 'sessionSource' },
                        { name: 'sessionMedium' },
                        { name: 'sessionCampaignName' }
                    ],
                    metrics: [
                        { name: 'sessions' },
                        { name: 'totalUsers' },
                        { name: 'newUsers' },
                        { name: 'bounceRate' },
                        { name: 'averageSessionDuration' }
                    ],
                    orderBys: [{ metric: { metricName: 'sessions' }, desc: true }],
                    limit
                }
            });
            return response.data.rows?.map((row) => {
                const dimensions = row.dimensionValues || [];
                const metrics = row.metricValues || [];
                return {
                    source: dimensions[0]?.value || '',
                    medium: dimensions[1]?.value || '',
                    campaign: dimensions[2]?.value || undefined,
                    sessions: parseInt(metrics[0]?.value || '0'),
                    users: parseInt(metrics[1]?.value || '0'),
                    newUsers: parseInt(metrics[2]?.value || '0'),
                    bounceRate: parseFloat(metrics[3]?.value || '0'),
                    avgSessionDuration: parseFloat(metrics[4]?.value || '0')
                };
            }) || [];
        }
        catch (error) {
            console.error('Failed to fetch traffic sources:', error);
            throw error;
        }
    }
    /**
     * Get device breakdown
     */
    async getDeviceData(startDate, endDate) {
        if (!this.isAuthenticated) {
            throw new Error('Not authenticated with Google Analytics');
        }
        try {
            const response = await this.analyticsData.properties.runReport({
                property: `properties/${this.propertyId}`,
                requestBody: {
                    dateRanges: [{ startDate, endDate }],
                    dimensions: [{ name: 'deviceCategory' }],
                    metrics: [
                        { name: 'sessions' },
                        { name: 'totalUsers' },
                        { name: 'screenPageViews' },
                        { name: 'bounceRate' },
                        { name: 'averageSessionDuration' }
                    ]
                }
            });
            return response.data.rows?.map((row) => {
                const dimensions = row.dimensionValues || [];
                const metrics = row.metricValues || [];
                return {
                    deviceCategory: dimensions[0]?.value,
                    sessions: parseInt(metrics[0]?.value || '0'),
                    users: parseInt(metrics[1]?.value || '0'),
                    pageviews: parseInt(metrics[2]?.value || '0'),
                    bounceRate: parseFloat(metrics[3]?.value || '0'),
                    avgSessionDuration: parseFloat(metrics[4]?.value || '0')
                };
            }) || [];
        }
        catch (error) {
            console.error('Failed to fetch device data:', error);
            throw error;
        }
    }
    /**
     * Get goal completions data
     */
    async getGoalData(startDate, endDate) {
        if (!this.isAuthenticated) {
            throw new Error('Not authenticated with Google Analytics');
        }
        try {
            const response = await this.analyticsData.properties.runReport({
                property: `properties/${this.propertyId}`,
                requestBody: {
                    dateRanges: [{ startDate, endDate }],
                    dimensions: [{ name: 'eventName' }],
                    metrics: [
                        { name: 'conversions' },
                        { name: 'totalRevenue' }
                    ],
                    dimensionFilter: {
                        filter: {
                            fieldName: 'eventName',
                            stringFilter: {
                                matchType: 'CONTAINS',
                                value: 'goal'
                            }
                        }
                    }
                }
            });
            return response.data.rows?.map((row) => {
                const dimensions = row.dimensionValues || [];
                const metrics = row.metricValues || [];
                const conversions = parseInt(metrics[0]?.value || '0');
                const totalSessions = 1000; // Would need to fetch this separately
                return {
                    goalName: dimensions[0]?.value || '',
                    goalCompletions: conversions,
                    goalConversionRate: totalSessions > 0 ? (conversions / totalSessions) * 100 : 0,
                    goalValue: parseFloat(metrics[1]?.value || '0')
                };
            }) || [];
        }
        catch (error) {
            console.error('Failed to fetch goal data:', error);
            throw error;
        }
    }
    /**
     * Get page-specific analytics data
     */
    async getPageAnalytics(pagePath, startDate, endDate) {
        if (!this.isAuthenticated) {
            throw new Error('Not authenticated with Google Analytics');
        }
        try {
            const response = await this.analyticsData.properties.runReport({
                property: `properties/${this.propertyId}`,
                requestBody: {
                    dateRanges: [{ startDate, endDate }],
                    dimensions: [
                        { name: 'pagePath' },
                        { name: 'pageTitle' }
                    ],
                    metrics: [
                        { name: 'sessions' },
                        { name: 'screenPageViews' },
                        { name: 'bounceRate' },
                        { name: 'averageSessionDuration' }
                    ],
                    dimensionFilter: {
                        filter: {
                            fieldName: 'pagePath',
                            stringFilter: {
                                matchType: 'EXACT',
                                value: pagePath
                            }
                        }
                    }
                }
            });
            const row = response.data.rows?.[0];
            if (!row) {
                throw new Error(`No analytics data found for page: ${pagePath}`);
            }
            const dimensions = row.dimensionValues || [];
            const metrics = row.metricValues || [];
            return {
                pagePath: dimensions[0]?.value || pagePath,
                pageTitle: dimensions[1]?.value || '',
                sessions: parseInt(metrics[0]?.value || '0'),
                pageviews: parseInt(metrics[1]?.value || '0'),
                uniquePageviews: parseInt(metrics[1]?.value || '0'),
                avgTimeOnPage: parseFloat(metrics[3]?.value || '0'),
                bounceRate: parseFloat(metrics[2]?.value || '0'),
                exitRate: parseFloat(metrics[2]?.value || '0'),
                entrances: parseInt(metrics[0]?.value || '0')
            };
        }
        catch (error) {
            console.error('Failed to fetch page analytics:', error);
            throw error;
        }
    }
    /**
     * Get comprehensive analytics data for pSEO analysis
     */
    async getDataForPSEOAnalysis(targetPages) {
        const endDate = new Date().toISOString().split('T')[0];
        const startDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
        try {
            // Get all analytics data in parallel
            const [siteOverview, topPages, trafficSources, deviceBreakdown, goalData] = await Promise.all([
                this.getSiteOverview(startDate, endDate),
                this.getTopPages(startDate, endDate, 50),
                this.getTrafficSources(startDate, endDate, 20),
                this.getDeviceData(startDate, endDate),
                this.getGoalData(startDate, endDate)
            ]);
            // Get analytics for specific target pages
            const pageAnalytics = [];
            const limitedPages = targetPages.slice(0, 10); // Limit for performance
            for (const pagePath of limitedPages) {
                try {
                    const pageData = await this.getPageAnalytics(pagePath, startDate, endDate);
                    pageAnalytics.push(pageData);
                }
                catch (error) {
                    console.warn(`Failed to get analytics for page ${pagePath}:`, error);
                }
            }
            return {
                siteOverview,
                topPages,
                trafficSources,
                deviceBreakdown,
                goalData,
                pageAnalytics
            };
        }
        catch (error) {
            console.error('Failed to get Analytics data for pSEO analysis:', error);
            throw error;
        }
    }
    /**
     * Check if service is authenticated
     */
    isAuthenticatedStatus() {
        return this.isAuthenticated;
    }
    /**
     * Get refresh token (for storage)
     */
    getRefreshToken() {
        return this.oauth2Client.credentials.refresh_token || null;
    }
}
exports.GoogleAnalyticsService = GoogleAnalyticsService;
//# sourceMappingURL=GoogleAnalyticsService.js.map