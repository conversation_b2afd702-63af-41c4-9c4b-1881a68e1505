"use strict";
// =====================================================
// UBERSUGGEST INTEGRATION SERVICE
// Phase 5: External API Integration
// =====================================================
Object.defineProperty(exports, "__esModule", { value: true });
exports.UbersuggestService = void 0;
class UbersuggestService {
    constructor(config) {
        this.apiKey = config.apiKey;
        this.baseUrl = config.baseUrl || 'https://app.neilpatel.com/api';
    }
    /**
     * Get keyword data including volume, CPC, and difficulty
     */
    async getKeywordData(keyword, location = 'US', language = 'en') {
        try {
            const response = await this.makeRequest('/keyword/data', {
                keyword,
                location,
                language
            });
            return {
                keyword: response.keyword || keyword,
                volume: response.search_volume || 0,
                cpc: response.cost_per_click || 0,
                paid_difficulty: response.paid_difficulty || 0,
                seo_difficulty: response.seo_difficulty || 0,
                trend: response.trend || [],
                related_keywords: response.related_keywords || []
            };
        }
        catch (error) {
            console.error('Failed to fetch keyword data from Ubersuggest:', error);
            throw error;
        }
    }
    /**
     * Get keyword suggestions and ideas
     */
    async getKeywordIdeas(seedKeyword, location = 'US', language = 'en', limit = 100) {
        try {
            const response = await this.makeRequest('/keyword/suggestions', {
                keyword: seedKeyword,
                location,
                language,
                limit
            });
            const suggestions = response.suggestions?.map((item) => ({
                keyword: item.keyword || '',
                volume: item.search_volume || 0,
                cpc: item.cost_per_click || 0,
                paid_difficulty: item.paid_difficulty || 0,
                seo_difficulty: item.seo_difficulty || 0,
                trend: item.trend || []
            })) || [];
            return {
                suggestions,
                questions: response.questions || [],
                prepositions: response.prepositions || [],
                comparisons: response.comparisons || [],
                total_suggestions: response.total_suggestions || suggestions.length
            };
        }
        catch (error) {
            console.error('Failed to fetch keyword ideas from Ubersuggest:', error);
            throw error;
        }
    }
    /**
     * Analyze competitor domains
     */
    async getCompetitorAnalysis(domain, location = 'US') {
        try {
            const response = await this.makeRequest('/domain/overview', {
                domain,
                location
            });
            return {
                domain: response.domain || domain,
                organic_keywords: response.organic_keywords || 0,
                organic_traffic: response.organic_traffic || 0,
                paid_keywords: response.paid_keywords || 0,
                paid_traffic: response.paid_traffic || 0,
                backlinks: response.backlinks || 0,
                domain_score: response.domain_score || 0
            };
        }
        catch (error) {
            console.error('Failed to fetch competitor analysis from Ubersuggest:', error);
            throw error;
        }
    }
    /**
     * Get backlink data for a domain
     */
    async getBacklinkData(domain, limit = 100) {
        try {
            const response = await this.makeRequest('/domain/backlinks', {
                domain,
                limit
            });
            return response.backlinks?.map((link) => ({
                url: link.url || '',
                domain_score: link.domain_score || 0,
                page_score: link.page_score || 0,
                backlinks: link.backlinks || 0,
                referring_domains: link.referring_domains || 0,
                anchor_text: link.anchor_text || '',
                link_type: link.link_type || 'dofollow'
            })) || [];
        }
        catch (error) {
            console.error('Failed to fetch backlink data from Ubersuggest:', error);
            throw error;
        }
    }
    /**
     * Get top organic keywords for a domain
     */
    async getTopKeywords(domain, location = 'US', limit = 100) {
        try {
            const response = await this.makeRequest('/domain/keywords', {
                domain,
                location,
                limit
            });
            return response.keywords?.map((item) => ({
                keyword: item.keyword || '',
                volume: item.search_volume || 0,
                cpc: item.cost_per_click || 0,
                paid_difficulty: item.paid_difficulty || 0,
                seo_difficulty: item.seo_difficulty || 0,
                trend: item.trend || []
            })) || [];
        }
        catch (error) {
            console.error('Failed to fetch top keywords from Ubersuggest:', error);
            throw error;
        }
    }
    /**
     * Get content gap analysis between domains
     */
    async getContentGapAnalysis(targetDomain, competitorDomains, location = 'US') {
        try {
            // Get keywords for target domain
            const targetKeywords = await this.getTopKeywords(targetDomain, location, 500);
            // Get keywords for each competitor
            const competitorKeywords = [];
            for (const competitor of competitorDomains.slice(0, 3)) { // Limit for API quotas
                try {
                    const keywords = await this.getTopKeywords(competitor, location, 200);
                    competitorKeywords.push(keywords);
                    // Add delay to respect rate limits
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
                catch (error) {
                    console.warn(`Failed to get keywords for competitor ${competitor}:`, error);
                }
            }
            // Analyze gaps and opportunities
            const allCompetitorKeywords = competitorKeywords.flat();
            const targetKeywordSet = new Set(targetKeywords.map(k => k.keyword.toLowerCase()));
            const opportunities = allCompetitorKeywords.filter(keyword => !targetKeywordSet.has(keyword.keyword.toLowerCase()) && keyword.volume > 100);
            const competitiveKeywords = targetKeywords.filter(keyword => allCompetitorKeywords.some(ck => ck.keyword.toLowerCase() === keyword.keyword.toLowerCase()));
            const uniqueKeywords = targetKeywords.filter(keyword => !allCompetitorKeywords.some(ck => ck.keyword.toLowerCase() === keyword.keyword.toLowerCase()));
            return {
                opportunities: opportunities.slice(0, 50),
                competitive_keywords: competitiveKeywords.slice(0, 50),
                unique_keywords: uniqueKeywords.slice(0, 50)
            };
        }
        catch (error) {
            console.error('Failed to perform content gap analysis:', error);
            throw error;
        }
    }
    /**
     * Get comprehensive data for pSEO analysis
     */
    async getDataForPSEOAnalysis(domain, targetKeywords, competitors = []) {
        try {
            // Get domain overview
            const domainOverview = await this.getCompetitorAnalysis(domain);
            // Analyze target keywords (limit for API quotas)
            const keywordAnalysis = [];
            const limitedKeywords = targetKeywords.slice(0, 20);
            for (const keyword of limitedKeywords) {
                try {
                    const keywordData = await this.getKeywordData(keyword);
                    keywordAnalysis.push(keywordData);
                    // Rate limiting
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
                catch (error) {
                    console.warn(`Failed to analyze keyword "${keyword}":`, error);
                }
            }
            // Get keyword ideas based on top target keyword
            const seedKeyword = targetKeywords[0] || domain.split('.')[0];
            const keywordIdeas = await this.getKeywordIdeas(seedKeyword);
            // Analyze competitors
            const competitorAnalysis = [];
            const limitedCompetitors = competitors.slice(0, 3);
            for (const competitor of limitedCompetitors) {
                try {
                    const competitorData = await this.getCompetitorAnalysis(competitor);
                    competitorAnalysis.push(competitorData);
                    // Rate limiting
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
                catch (error) {
                    console.warn(`Failed to analyze competitor "${competitor}":`, error);
                }
            }
            // Content gap analysis (if competitors provided)
            let contentGaps;
            if (competitors.length > 0) {
                try {
                    contentGaps = await this.getContentGapAnalysis(domain, competitors);
                }
                catch (error) {
                    console.warn('Failed to perform content gap analysis:', error);
                }
            }
            return {
                domainOverview,
                keywordAnalysis,
                competitorAnalysis,
                keywordIdeas,
                contentGaps
            };
        }
        catch (error) {
            console.error('Failed to get Ubersuggest data for pSEO analysis:', error);
            throw error;
        }
    }
    /**
     * Make API request to Ubersuggest
     */
    async makeRequest(endpoint, params) {
        const url = new URL(endpoint, this.baseUrl);
        // Add API key and parameters
        url.searchParams.append('token', this.apiKey);
        Object.entries(params).forEach(([key, value]) => {
            if (value !== undefined && value !== null) {
                url.searchParams.append(key, String(value));
            }
        });
        try {
            const response = await fetch(url.toString(), {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'User-Agent': 'pSEO-Platform/1.0'
                }
            });
            if (!response.ok) {
                throw new Error(`Ubersuggest API error: ${response.status} ${response.statusText}`);
            }
            const data = await response.json();
            if (data.error) {
                throw new Error(`Ubersuggest API error: ${data.error}`);
            }
            return data;
        }
        catch (error) {
            console.error('Ubersuggest API request failed:', error);
            throw error;
        }
    }
    /**
     * Check if service has valid API key
     */
    isConfigured() {
        return !!this.apiKey;
    }
}
exports.UbersuggestService = UbersuggestService;
//# sourceMappingURL=UbersuggestService.js.map