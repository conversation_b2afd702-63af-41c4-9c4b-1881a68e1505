{"version": 3, "file": "registry.js", "sourceRoot": "", "sources": ["../../../server/plugins/registry.ts"], "names": [], "mappings": ";;;AA8BA,kEAAkE;AACrD,QAAA,uBAAuB,GAAwC;IAC1E,SAAS,EAAE;QACT,OAAO,EAAE,IAAI;QACb,IAAI,EAAE,oBAAoB;QAC1B,SAAS,EAAE,gBAAgB;QAC3B,OAAO,EAAE,OAAO;QAChB,WAAW,EAAE,yCAAyC;QACtD,UAAU,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC;KAClC;IACD,IAAI,EAAE;QACJ,OAAO,EAAE,IAAI;QACb,IAAI,EAAE,UAAU;QAChB,SAAS,EAAE,WAAW;QACtB,OAAO,EAAE,OAAO;QAChB,WAAW,EAAE,2CAA2C;QACxD,UAAU,EAAE,CAAC,MAAM,CAAC;QACpB,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,oBAAoB;gBAC1B,SAAS,EAAE,oBAAoB;gBAC/B,WAAW,EACT,8DAA8D;gBAChE,YAAY,EAAE,CAAC,gBAAgB,EAAE,iBAAiB,EAAE,cAAc,CAAC;gBACnE,YAAY,EAAE,EAAE;gBAChB,oBAAoB,EAAE;oBACpB,SAAS,EAAE,GAAG;oBACd,SAAS,EAAE,CAAC;oBACZ,eAAe,EAAE,GAAG;oBACpB,mBAAmB,EAAE,CAAC;iBACvB;gBACD,KAAK,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,CAAC;gBACzD,sBAAsB,EAAE,CAAC,gBAAgB,EAAE,iBAAiB,CAAC;gBAC7D,QAAQ,EAAE,CAAC;aACZ;YACD;gBACE,IAAI,EAAE,sBAAsB;gBAC5B,SAAS,EAAE,sBAAsB;gBACjC,WAAW,EACT,oFAAoF;gBACtF,YAAY,EAAE;oBACZ,kBAAkB;oBAClB,wBAAwB;oBACxB,sBAAsB;iBACvB;gBACD,YAAY,EAAE,EAAE;gBAChB,oBAAoB,EAAE;oBACpB,SAAS,EAAE,GAAG;oBACd,SAAS,EAAE,CAAC;oBACZ,eAAe,EAAE,GAAG;oBACpB,mBAAmB,EAAE,CAAC;iBACvB;gBACD,KAAK,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC;gBAC1C,sBAAsB,EAAE,CAAC,kBAAkB,EAAE,iBAAiB,CAAC;gBAC/D,QAAQ,EAAE,CAAC;aACZ;YACD;gBACE,IAAI,EAAE,wBAAwB;gBAC9B,SAAS,EAAE,wBAAwB;gBACnC,WAAW,EACT,iFAAiF;gBACnF,YAAY,EAAE,CAAC,oBAAoB,EAAE,kBAAkB,CAAC;gBACxD,YAAY,EAAE,CAAC,oBAAoB,EAAE,sBAAsB,CAAC;gBAC5D,oBAAoB,EAAE;oBACpB,SAAS,EAAE,IAAI;oBACf,SAAS,EAAE,CAAC;oBACZ,eAAe,EAAE,GAAG;oBACpB,mBAAmB,EAAE,CAAC;iBACvB;gBACD,KAAK,EAAE,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,CAAC;gBAC1C,sBAAsB,EAAE,CAAC,oBAAoB,EAAE,iBAAiB,CAAC;gBACjE,QAAQ,EAAE,CAAC;aACZ;YACD;gBACE,IAAI,EAAE,uBAAuB;gBAC7B,SAAS,EAAE,uBAAuB;gBAClC,WAAW,EACT,uEAAuE;gBACzE,YAAY,EAAE;oBACZ,mBAAmB;oBACnB,2BAA2B;oBAC3B,qBAAqB;iBACtB;gBACD,YAAY,EAAE,EAAE;gBAChB,oBAAoB,EAAE;oBACpB,SAAS,EAAE,GAAG;oBACd,SAAS,EAAE,CAAC;oBACZ,eAAe,EAAE,GAAG;oBACpB,mBAAmB,EAAE,CAAC;iBACvB;gBACD,KAAK,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,eAAe,CAAC;gBACrD,sBAAsB,EAAE,CAAC,mBAAmB,EAAE,iBAAiB,CAAC;gBAChE,QAAQ,EAAE,CAAC;aACZ;YACD;gBACE,IAAI,EAAE,eAAe;gBACrB,SAAS,EAAE,eAAe;gBAC1B,WAAW,EACT,8EAA8E;gBAChF,YAAY,EAAE;oBACZ,qBAAqB;oBACrB,sBAAsB;oBACtB,qBAAqB;iBACtB;gBACD,YAAY,EAAE,CAAC,oBAAoB,CAAC;gBACpC,oBAAoB,EAAE;oBACpB,SAAS,EAAE,GAAG;oBACd,SAAS,EAAE,CAAC;oBACZ,eAAe,EAAE,GAAG;oBACpB,mBAAmB,EAAE,CAAC;iBACvB;gBACD,KAAK,EAAE;oBACL,MAAM;oBACN,YAAY;oBACZ,UAAU;oBACV,OAAO;oBACP,sBAAsB;iBACvB;gBACD,sBAAsB,EAAE,CAAC,iBAAiB,EAAE,iBAAiB,CAAC;gBAC9D,QAAQ,EAAE,CAAC;aACZ;YACD;gBACE,IAAI,EAAE,mBAAmB;gBACzB,SAAS,EAAE,mBAAmB;gBAC9B,WAAW,EAAE,iDAAiD;gBAC9D,YAAY,EAAE;oBACZ,0BAA0B;oBAC1B,qBAAqB;oBACrB,uBAAuB;iBACxB;gBACD,YAAY,EAAE;oBACZ,oBAAoB;oBACpB,sBAAsB;oBACtB,wBAAwB;iBACzB;gBACD,oBAAoB,EAAE;oBACpB,SAAS,EAAE,GAAG;oBACd,SAAS,EAAE,CAAC;oBACZ,eAAe,EAAE,IAAI;oBACrB,mBAAmB,EAAE,CAAC;iBACvB;gBACD,KAAK,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC;gBACjD,sBAAsB,EAAE,CAAC,iBAAiB,CAAC;gBAC3C,QAAQ,EAAE,CAAC;aACZ;YACD;gBACE,IAAI,EAAE,2BAA2B;gBACjC,SAAS,EAAE,2BAA2B;gBACtC,WAAW,EACT,6EAA6E;gBAC/E,YAAY,EAAE;oBACZ,uBAAuB;oBACvB,0BAA0B;oBAC1B,oBAAoB;iBACrB;gBACD,YAAY,EAAE;oBACZ,oBAAoB;oBACpB,sBAAsB;oBACtB,wBAAwB;iBACzB;gBACD,oBAAoB,EAAE;oBACpB,SAAS,EAAE,GAAG;oBACd,SAAS,EAAE,CAAC;oBACZ,eAAe,EAAE,IAAI;oBACrB,mBAAmB,EAAE,CAAC;iBACvB;gBACD,KAAK,EAAE;oBACL,IAAI;oBACJ,UAAU;oBACV,OAAO;oBACP,eAAe;oBACf,WAAW;oBACX,gBAAgB;iBACjB;gBACD,sBAAsB,EAAE,CAAC,gBAAgB,EAAE,iBAAiB,CAAC;gBAC7D,QAAQ,EAAE,CAAC;aACZ;SACF;KACF;IACD,MAAM,EAAE;QACN,OAAO,EAAE,IAAI;QACb,IAAI,EAAE,aAAa;QACnB,SAAS,EAAE,aAAa;QACxB,OAAO,EAAE,OAAO;QAChB,WAAW,EAAE,qCAAqC;QAClD,UAAU,EAAE,CAAC,MAAM,CAAC;KACrB;IACD,GAAG,EAAE;QACH,OAAO,EAAE,IAAI;QACb,IAAI,EAAE,SAAS;QACf,SAAS,EAAE,UAAU;QACrB,OAAO,EAAE,OAAO;QAChB,WAAW,EACT,8FAA8F;QAChG,UAAU,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC;KAClC;IACD,SAAS,EAAE;QACT,OAAO,EAAE,IAAI;QACb,IAAI,EAAE,eAAe;QACrB,SAAS,EAAE,gBAAgB;QAC3B,OAAO,EAAE,OAAO;QAChB,WAAW,EAAE,yDAAyD;QACtE,UAAU,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC;KAClC;CACO,CAAC;AAEX,mBAAmB;AACZ,MAAM,wBAAwB,GAAG,GAAa,EAAE;IACrD,OAAO,MAAM,CAAC,OAAO,CAAC,+BAAuB,CAAC;SAC3C,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC;SACvC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;AAC9B,CAAC,CAAC;AAJW,QAAA,wBAAwB,4BAInC;AAEK,MAAM,sBAAsB,GAAG,CACpC,UAAkB,EACe,EAAE;IACnC,OAAO,+BAAuB,CAAC,UAAU,CAAC,CAAC;AAC7C,CAAC,CAAC;AAJW,QAAA,sBAAsB,0BAIjC;AAEK,MAAM,sBAAsB,GAAG,CAAC,UAAkB,EAAW,EAAE;IACpE,OAAO,+BAAuB,CAAC,UAAU,CAAC,EAAE,OAAO,IAAI,KAAK,CAAC;AAC/D,CAAC,CAAC;AAFW,QAAA,sBAAsB,0BAEjC;AAEK,MAAM,oBAAoB,GAAG,GAAwC,EAAE;IAC5E,OAAO,+BAAuB,CAAC;AACjC,CAAC,CAAC;AAFW,QAAA,oBAAoB,wBAE/B;AAEF,kCAAkC;AAC3B,MAAM,eAAe,GAAG,CAAC,UAAkB,EAAiB,EAAE;IACnE,OAAO,+BAAuB,CAAC,UAAU,CAAC,EAAE,MAAM,IAAI,EAAE,CAAC;AAC3D,CAAC,CAAC;AAFW,QAAA,eAAe,mBAE1B;AAEK,MAAM,cAAc,GAAG,CAC5B,UAAkB,EAClB,SAAiB,EACQ,EAAE;IAC3B,MAAM,MAAM,GAAG,IAAA,uBAAe,EAAC,UAAU,CAAC,CAAC;IAC3C,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC;AAC1D,CAAC,CAAC;AANW,QAAA,cAAc,kBAMzB;AAEK,MAAM,qBAAqB,GAAG,CAAC,UAAkB,EAAiB,EAAE;IACzE,MAAM,SAAS,GAAkB,EAAE,CAAC;IAEpC,MAAM,CAAC,MAAM,CAAC,+BAAuB,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;QACxD,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAClB,SAAS,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;QACnC,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;AAC9E,CAAC,CAAC;AAVW,QAAA,qBAAqB,yBAUhC;AAEK,MAAM,uBAAuB,GAAG,CACrC,YAAoB,EACL,EAAE;IACjB,MAAM,SAAS,GAAkB,EAAE,CAAC;IAEpC,MAAM,CAAC,MAAM,CAAC,+BAAuB,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;QACxD,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAClB,SAAS,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;QACnC,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAChC,KAAK,CAAC,sBAAsB,CAAC,QAAQ,CAAC,YAAY,CAAC,CACpD,CAAC;AACJ,CAAC,CAAC;AAdW,QAAA,uBAAuB,2BAclC;AAEK,MAAM,yBAAyB,GAAG,CACvC,SAAiB,EACkC,EAAE;IACrD,MAAM,SAAS,GAAkB,EAAE,CAAC;IAEpC,MAAM,CAAC,MAAM,CAAC,+BAAuB,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;QACxD,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAClB,SAAS,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;QACnC,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,MAAM,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC;IAC1D,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,OAAO;YACL,KAAK,EAAE,KAAK;YACZ,mBAAmB,EAAE,CAAC,SAAS,SAAS,YAAY,CAAC;SACtD,CAAC;IACJ,CAAC;IAED,MAAM,eAAe,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IACrD,MAAM,mBAAmB,GAAG,KAAK,CAAC,YAAY,CAAC,MAAM,CACnD,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC,CACxC,CAAC;IAEF,OAAO;QACL,KAAK,EAAE,mBAAmB,CAAC,MAAM,KAAK,CAAC;QACvC,mBAAmB;KACpB,CAAC;AACJ,CAAC,CAAC;AA5BW,QAAA,yBAAyB,6BA4BpC;AAEK,MAAM,sBAAsB,GAAG,CAAC,eAAyB,EAAY,EAAE;IAC5E,MAAM,SAAS,GAAkB,EAAE,CAAC;IAEpC,MAAM,CAAC,MAAM,CAAC,+BAAuB,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;QACxD,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAClB,SAAS,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;QACnC,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;IACxE,MAAM,QAAQ,GAAa,EAAE,CAAC;IAC9B,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAU,CAAC;IAEnC,MAAM,KAAK,GAAG,CAAC,SAAiB,EAAE,EAAE;QAClC,IAAI,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,OAAO;QACzC,IAAI,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,0CAA0C,SAAS,EAAE,CAAC,CAAC;QACzE,CAAC;QAED,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACxB,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAEtC,IAAI,KAAK,EAAE,CAAC;YACV,2BAA2B;YAC3B,KAAK,MAAM,UAAU,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;gBAC5C,KAAK,CAAC,UAAU,CAAC,CAAC;YACpB,CAAC;YACD,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC3B,CAAC;QAED,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IAC7B,CAAC,CAAC;IAEF,6BAA6B;IAC7B,KAAK,MAAM,SAAS,IAAI,eAAe,EAAE,CAAC;QACxC,KAAK,CAAC,SAAS,CAAC,CAAC;IACnB,CAAC;IAED,oDAAoD;IACpD,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAC5B,MAAM,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/B,MAAM,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/B,OAAO,CAAC,MAAM,EAAE,QAAQ,IAAI,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,IAAI,GAAG,CAAC,CAAC;IAC/D,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AA5CW,QAAA,sBAAsB,0BA4CjC"}