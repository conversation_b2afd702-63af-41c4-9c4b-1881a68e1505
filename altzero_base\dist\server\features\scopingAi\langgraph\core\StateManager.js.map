{"version": 3, "file": "StateManager.js", "sourceRoot": "", "sources": ["../../../../../../server/features/scopingAi/langgraph/core/StateManager.ts"], "names": [], "mappings": ";AAAA,wDAAwD;AACxD,kDAAkD;AAClD,wDAAwD;;;AAIxD,MAAa,YAAY;IAAzB;QAEU,mBAAc,GAAwC,IAAI,GAAG,EAAE,CAAC;QAChE,qBAAgB,GAAuB,IAAI,GAAG,EAAE,CAAC;QACjD,mBAAc,GAA0C,IAAI,GAAG,EAAE,CAAC;QAClE,sBAAiB,GAGrB,IAAI,GAAG,EAAE,CAAC;IA6UhB,CAAC;IA3UC,kEAAkE;IAC3D,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;YAC3B,YAAY,CAAC,QAAQ,GAAG,IAAI,YAAY,EAAE,CAAC;QAC7C,CAAC;QACD,OAAO,YAAY,CAAC,QAAQ,CAAC;IAC/B,CAAC;IAED,qDAAqD;IAC9C,wBAAwB,CAC7B,UAAkB,EAClB,QAAiD;QAEjD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IACnD,CAAC;IAED,+BAA+B;IACxB,0BAA0B,CAAC,UAAkB;QAClD,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;IAC5C,CAAC;IAED,sBAAsB;IACtB,KAAK,CAAC,iBAAiB,CACrB,UAAkB,EAClB,KAA6B;QAE7B,IAAI,CAAC;YACH,4DAA4D;YAC5D,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,GAAG,KAAK,EAAE,CAAC,CAAC;YAElD,4BAA4B;YAC5B,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;YAC5D,SAAS,CAAC,IAAI,CAAC,EAAE,GAAG,KAAK,EAAE,CAAC,CAAC;YAC7B,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YAE/C,mBAAmB;YACnB,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE;gBACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,MAAM,EAAE,aAAa;gBACrB,IAAI,EAAE,KAAK,CAAC,YAAY;gBACxB,QAAQ,EAAE,KAAK,CAAC,QAAQ;gBACxB,MAAM,EAAE,KAAK,CAAC,MAAM;aACrB,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CACT,gCAAgC,UAAU,YAAY,KAAK,CAAC,YAAY,KAAK,KAAK,CAAC,QAAQ,IAAI,CAChG,CAAC;YAEF,kDAAkD;YAClD,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YACxD,IAAI,QAAQ,EAAE,CAAC;gBACb,IAAI,CAAC;oBACH,QAAQ,CAAC,EAAE,GAAG,KAAK,EAAE,CAAC,CAAC;gBACzB,CAAC;gBAAC,OAAO,aAAa,EAAE,CAAC;oBACvB,OAAO,CAAC,KAAK,CACX,iCAAiC,UAAU,GAAG,EAC9C,aAAa,CACd,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CACX,uCAAuC,UAAU,GAAG,EACpD,KAAK,CACN,CAAC;YACF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,qBAAqB;IACrB,KAAK,CAAC,gBAAgB,CACpB,UAAkB;QAElB,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAClD,OAAO,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QACrC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CACX,sCAAsC,UAAU,GAAG,EACnD,KAAK,CACN,CAAC;YACF,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,yCAAyC;IACzC,KAAK,CAAC,iBAAiB,CAAC,UAAkB;QACxC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAElD,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,OAAO;oBACL,MAAM,EAAE,WAAW;oBACnB,OAAO,EAAE,YAAY,UAAU,YAAY;iBAC5C,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,WAAW,EAAE,KAAK,CAAC,WAAW;gBAC9B,MAAM,EAAE,KAAK,CAAC,MAAM;gBACpB,QAAQ,EAAE,KAAK,CAAC,QAAQ;gBACxB,YAAY,EAAE,KAAK,CAAC,YAAY;gBAChC,UAAU,EAAE,KAAK,CAAC,UAAU;gBAC5B,YAAY,EAAE,KAAK,CAAC,YAAY;gBAChC,YAAY,EAAE,KAAK,CAAC,YAAY;gBAChC,eAAe,EAAE,KAAK,CAAC,eAAe;gBACtC,MAAM,EAAE,KAAK,CAAC,MAAM;gBACpB,QAAQ,EAAE,KAAK,CAAC,QAAQ;aACzB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CACX,uCAAuC,UAAU,GAAG,EACpD,KAAK,CACN,CAAC;YACF,OAAO;gBACL,MAAM,EAAE,OAAO;gBACf,OAAO,EAAE,oCAAoC;aAC9C,CAAC;QACJ,CAAC;IACH,CAAC;IAED,kBAAkB;IAClB,KAAK,CAAC,cAAc,CAAC,UAAkB;QACrC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAElD,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IACE,KAAK,CAAC,MAAM,KAAK,WAAW;gBAC5B,KAAK,CAAC,MAAM,KAAK,QAAQ;gBACzB,KAAK,CAAC,MAAM,KAAK,WAAW,EAC5B,CAAC;gBACD,OAAO,KAAK,CAAC,CAAC,2CAA2C;YAC3D,CAAC;YAED,4BAA4B;YAC5B,MAAM,cAAc,GAA2B;gBAC7C,GAAG,KAAK;gBACR,MAAM,EAAE,WAAW;gBACnB,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACtC,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACtC,YAAY,EAAE,WAAW;aAC1B,CAAC;YAEF,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;YAEzD,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE;gBACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,MAAM,EAAE,oBAAoB;gBAC5B,IAAI,EAAE,KAAK,CAAC,YAAY;gBACxB,QAAQ,EAAE,KAAK,CAAC,QAAQ;aACzB,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,0BAA0B,UAAU,EAAE,CAAC,CAAC;YACpD,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YACnE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,uBAAuB;IACvB,kBAAkB;QAIhB,MAAM,eAAe,GAGhB,EAAE,CAAC;QAER,KAAK,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,CAAC;YAChE,IAAI,KAAK,CAAC,MAAM,KAAK,SAAS,IAAI,KAAK,CAAC,MAAM,KAAK,cAAc,EAAE,CAAC;gBAClE,eAAe,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,EAAE,GAAG,KAAK,EAAE,EAAE,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED,iDAAiD;IACjD,eAAe;QAIb,MAAM,YAAY,GAGb,EAAE,CAAC;QAER,KAAK,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,CAAC;YAChE,YAAY,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,EAAE,GAAG,KAAK,EAAE,EAAE,CAAC,CAAC;QACzD,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,8BAA8B;IACtB,mBAAmB,CAAC,UAAkB,EAAE,KAAU;QACxD,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;QAC5D,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;IACjD,CAAC;IAED,wBAAwB;IACxB,mBAAmB,CAAC,UAAkB;QACpC,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;IACrD,CAAC;IAED,sBAAsB;IACtB,iBAAiB,CAAC,UAAkB;QAClC,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;IACnD,CAAC;IAED,0BAA0B;IAC1B,gBAAgB;QACd,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;QAChD,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC,MAAM,CAAC;QAEzD,IAAI,kBAAkB,GAAG,CAAC,CAAC;QAC3B,IAAI,eAAe,GAAG,CAAC,CAAC;QACxB,IAAI,kBAAkB,GAAG,CAAC,CAAC;QAE3B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,EAAE,CAAC;YACjD,QAAQ,KAAK,CAAC,MAAM,EAAE,CAAC;gBACrB,KAAK,WAAW;oBACd,kBAAkB,EAAE,CAAC;oBACrB,MAAM;gBACR,KAAK,QAAQ;oBACX,eAAe,EAAE,CAAC;oBAClB,MAAM;gBACR,KAAK,WAAW;oBACd,kBAAkB,EAAE,CAAC;oBACrB,MAAM;YACV,CAAC;QACH,CAAC;QAED,OAAO;YACL,eAAe,EAAE,cAAc;YAC/B,gBAAgB,EAAE,eAAe;YACjC,mBAAmB,EAAE,kBAAkB;YACvC,gBAAgB,EAAE,eAAe;YACjC,mBAAmB,EAAE,kBAAkB;YACvC,YAAY,EACV,cAAc,GAAG,CAAC;gBAChB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,kBAAkB,GAAG,cAAc,CAAC,GAAG,GAAG,CAAC;gBACzD,CAAC,CAAC,CAAC;SACR,CAAC;IACJ,CAAC;IAED,4DAA4D;IAC5D,mBAAmB,CAAC,SAAiB,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;QACtD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,iBAAiB,GAAa,EAAE,CAAC;QAEvC,KAAK,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,CAAC;YAChE,MAAM,QAAQ,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,OAAO,EAAE,CAAC;YAE5D,IACE,QAAQ,GAAG,MAAM;gBACjB,CAAC,KAAK,CAAC,MAAM,KAAK,WAAW;oBAC3B,KAAK,CAAC,MAAM,KAAK,QAAQ;oBACzB,KAAK,CAAC,MAAM,KAAK,WAAW,CAAC,EAC/B,CAAC;gBACD,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACrC,CAAC;QACH,CAAC;QAED,KAAK,MAAM,UAAU,IAAI,iBAAiB,EAAE,CAAC;YAC3C,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACvC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACzC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjC,OAAO,CAAC,GAAG,CAAC,iBAAiB,iBAAiB,CAAC,MAAM,gBAAgB,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAED,2BAA2B;IAC3B,KAAK,CAAC,cAAc,CAClB,UAAkB,EAClB,QAAgB,EAChB,WAAmB;QAEnB,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAElD,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,YAAY,GAA2B;gBAC3C,GAAG,KAAK;gBACR,QAAQ;gBACR,YAAY,EAAE,WAAW;gBACzB,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACvC,CAAC;YAEF,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAED,wBAAwB;IACxB,KAAK,CAAC,QAAQ,CAAC,UAAkB,EAAE,KAAU;QAC3C,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAElD,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,YAAY,GAA2B;gBAC3C,GAAG,KAAK;gBACR,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC;gBAChC,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACvC,CAAC;YAEF,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAED,0BAA0B;IAC1B,KAAK,CAAC,UAAU,CAAC,UAAkB,EAAE,OAAe;QAClD,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAElD,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,YAAY,GAA2B;gBAC3C,GAAG,KAAK;gBACR,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,QAAQ,EAAE,OAAO,CAAC;gBACtC,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACvC,CAAC;YAEF,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;CACF;AArVD,oCAqVC"}