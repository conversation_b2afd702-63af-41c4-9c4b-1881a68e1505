"use strict";
// =====================================================
// BASE EXTERNAL SERVICE - BACKEND PROVIDER INTERFACE
// =====================================================
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseExternalService = void 0;
class BaseExternalService {
    constructor(name, tier, config) {
        this.name = name;
        this.tier = tier;
        this.config = config;
    }
    /**
     * Get the service name
     */
    getName() {
        return this.name;
    }
    /**
     * Get the service tier (free/premium)
     */
    getTier() {
        return this.tier;
    }
    /**
     * Check if service is enabled and configured
     */
    isEnabled() {
        return this.config.enabled && this.isConfigured();
    }
    /**
     * Get service specific rate limits
     */
    getRateLimit() {
        return this.config.rateLimit || 60; // Default 60 requests per minute
    }
    /**
     * Get service cost tier info
     */
    getCostInfo() {
        return {
            tier: this.tier,
            monthlyLimit: this.tier === 'free' ? this.getFreeTierLimit() : undefined,
            costPerRequest: this.tier === 'premium' ? this.getPremiumCost() : 0
        };
    }
    /**
     * Handle rate limiting
     */
    async handleRateLimit() {
        // Basic rate limiting implementation
        const delay = (60 / this.getRateLimit()) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
    }
    /**
     * Handle API errors with retries
     */
    async withRetry(operation, maxRetries = this.config.retries || 3) {
        let lastError;
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return await operation();
            }
            catch (error) {
                lastError = error;
                if (attempt === maxRetries) {
                    throw lastError;
                }
                // Exponential backoff
                const delay = Math.pow(2, attempt) * 1000;
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
        throw lastError;
    }
    /**
     * Create error result
     */
    createErrorResult(error) {
        return {
            provider: this.name,
            metrics: {
                overall: 0,
                technical: 0,
                content: 0,
                performance: 0,
                accessibility: 0,
                seo: 0
            },
            issues: [{
                    category: 'Service Error',
                    severity: 'critical',
                    title: `${this.name} Analysis Failed`,
                    description: error,
                    recommendation: `Check ${this.name} configuration and API status`,
                    impact: 'high'
                }],
            timestamp: new Date().toISOString(),
            success: false,
            error
        };
    }
}
exports.BaseExternalService = BaseExternalService;
//# sourceMappingURL=BaseExternalService.js.map