"use strict";
// =====================================================
// CLIENT ANALYSIS NODE - SCOPINGAI LANGGRAPH
// =====================================================
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientAnalysisNode = void 0;
class ClientAnalysisNode {
    constructor() {
        this.name = 'client_analysis';
        this.description = 'Analyzes client information, industry context, and business environment';
    }
    async execute(context) {
        const { state, tools, logger } = context;
        logger.info('Starting client analysis', {
            workflow_id: state.workflow_id,
            client_name: state.client?.name,
            industry: state.client?.industry
        });
        try {
            const startTime = Date.now();
            // Perform comprehensive client analysis
            const analysisResult = await this.performClientAnalysis(state, tools, logger);
            const processingTime = Date.now() - startTime;
            logger.info('Client analysis completed', {
                industry_insights: analysisResult.industry_insights.length,
                opportunities: analysisResult.opportunities.length,
                challenges: analysisResult.key_challenges.length,
                confidence: analysisResult.analysis_confidence
            });
            return {
                client_analysis: {
                    industry_insights: analysisResult.industry_insights,
                    market_position: analysisResult.market_position,
                    key_challenges: analysisResult.key_challenges,
                    opportunities: analysisResult.opportunities,
                    competitive_landscape: analysisResult.competitive_landscape,
                    recommendations: analysisResult.recommendations
                },
                current_step: 'client_analysis_completed',
                progress: 35,
                processing_time: (state.processing_time || 0) + processingTime,
                api_calls_made: [
                    ...(state.api_calls_made || []),
                    {
                        provider: 'ai_analysis',
                        endpoint: 'client_analysis',
                        calls_made: 3, // Industry, competitive, recommendations
                        success_rate: 1.0,
                        average_response_time: processingTime / 3,
                        cost_estimate: 0.15,
                        timestamp: new Date().toISOString()
                    }
                ],
                last_updated: new Date().toISOString(),
                node_data: {
                    ...state.node_data,
                    client_analysis: analysisResult
                }
            };
        }
        catch (error) {
            logger.error('Client analysis failed', error);
            // Provide fallback analysis
            const fallbackAnalysis = this.createFallbackAnalysis(state);
            return {
                client_analysis: fallbackAnalysis,
                current_step: 'client_analysis_completed',
                progress: 35,
                warnings: [...(state.warnings || []), 'Client analysis used fallback data due to processing error'],
                last_updated: new Date().toISOString()
            };
        }
    }
    async performClientAnalysis(state, tools, logger) {
        // Step 1: Analyze industry context
        const industryInsights = await this.analyzeIndustry(state, tools, logger);
        // Step 2: Determine market position
        const marketPosition = await this.analyzeMarketPosition(state, tools, logger);
        // Step 3: Identify challenges and opportunities
        const challengesAndOpportunities = await this.identifyChallengesAndOpportunities(state, tools, logger);
        // Step 4: Analyze competitive landscape
        const competitiveLandscape = await this.analyzeCompetitiveLandscape(state, tools, logger);
        // Step 5: Generate strategic recommendations
        const recommendations = await this.generateRecommendations(state, industryInsights, challengesAndOpportunities, tools, logger);
        // Calculate confidence score
        const analysisConfidence = this.calculateConfidenceScore(state, industryInsights, competitiveLandscape);
        return {
            industry_insights: industryInsights,
            market_position: marketPosition,
            key_challenges: challengesAndOpportunities.challenges,
            opportunities: challengesAndOpportunities.opportunities,
            competitive_landscape: competitiveLandscape,
            recommendations: recommendations,
            analysis_confidence: analysisConfidence
        };
    }
    async analyzeIndustry(state, tools, logger) {
        try {
            const industry = state.client?.industry || 'General Business';
            const clientSize = state.client?.size || 'Unknown';
            const location = state.client?.location || 'Unknown';
            const prompt = `Analyze the ${industry} industry for a ${clientSize} company in ${location}. 
      
      Provide insights on:
      1. Current market trends and dynamics
      2. Key growth drivers and challenges
      3. Technology adoption patterns
      4. Regulatory environment
      5. Future outlook and opportunities
      
      Format as JSON array with objects containing: category, insight, impact_level, timeframe`;
            const response = await tools.ai.generateStructuredData(prompt, {
                type: 'array',
                items: {
                    type: 'object',
                    properties: {
                        category: { type: 'string' },
                        insight: { type: 'string' },
                        impact_level: { type: 'string', enum: ['high', 'medium', 'low'] },
                        timeframe: { type: 'string' }
                    }
                }
            });
            return Array.isArray(response) ? response : response.insights || [];
        }
        catch (error) {
            logger.warn('Industry analysis failed, using fallback', error);
            return this.getFallbackIndustryInsights(state.client?.industry);
        }
    }
    async analyzeMarketPosition(state, tools, logger) {
        try {
            const prompt = `Based on the following client information, determine their likely market position:
      
      Client: ${state.client?.name || 'Unknown'}
      Industry: ${state.client?.industry || 'Unknown'}
      Size: ${state.client?.size || 'Unknown'}
      Description: ${state.client?.description || 'No description provided'}
      
      Classify their market position as one of: Market Leader, Strong Competitor, Emerging Player, Niche Specialist, Startup/New Entrant
      
      Provide a brief explanation of why this classification fits.`;
            const response = await tools.ai.generateText(prompt, { temperature: 0.3 });
            // Extract position from response
            const positions = ['Market Leader', 'Strong Competitor', 'Emerging Player', 'Niche Specialist', 'Startup/New Entrant'];
            const foundPosition = positions.find(pos => response.includes(pos));
            return foundPosition || 'Emerging Player';
        }
        catch (error) {
            logger.warn('Market position analysis failed, using default', error);
            return 'Emerging Player';
        }
    }
    async identifyChallengesAndOpportunities(state, tools, logger) {
        try {
            const prompt = `Identify key business challenges and opportunities for this client:
      
      Client: ${state.client?.name || 'Unknown'}
      Industry: ${state.client?.industry || 'Unknown'}
      Project: ${state.project?.description || 'Unknown'}
      
      Provide 3-5 key challenges and 3-5 opportunities as JSON:
      {
        "challenges": ["challenge1", "challenge2", ...],
        "opportunities": ["opportunity1", "opportunity2", ...]
      }`;
            const response = await tools.ai.generateStructuredData(prompt, {
                type: 'object',
                properties: {
                    challenges: { type: 'array', items: { type: 'string' } },
                    opportunities: { type: 'array', items: { type: 'string' } }
                }
            });
            return {
                challenges: response.challenges || [],
                opportunities: response.opportunities || []
            };
        }
        catch (error) {
            logger.warn('Challenges and opportunities analysis failed, using fallback', error);
            return {
                challenges: ['Digital transformation needs', 'Market competition', 'Resource constraints'],
                opportunities: ['Technology adoption', 'Market expansion', 'Process optimization']
            };
        }
    }
    async analyzeCompetitiveLandscape(state, tools, logger) {
        try {
            // Try to get competitor data from CRM tools
            const competitorData = await tools.crm.getCompetitorData(state.client?.industry || 'Unknown', state.client?.location);
            if (competitorData && competitorData.length > 0) {
                return competitorData;
            }
            // Fallback to AI analysis
            const prompt = `Analyze the competitive landscape for a company in the ${state.client?.industry || 'business'} industry.
      
      Provide information about:
      1. Major competitors and market leaders
      2. Competitive advantages and differentiators
      3. Market share dynamics
      4. Competitive threats and opportunities
      
      Format as JSON array with competitor objects containing: name, market_share, strengths, weaknesses, threat_level`;
            const response = await tools.ai.generateStructuredData(prompt, {
                type: 'array',
                items: {
                    type: 'object',
                    properties: {
                        name: { type: 'string' },
                        market_share: { type: 'string' },
                        strengths: { type: 'array', items: { type: 'string' } },
                        weaknesses: { type: 'array', items: { type: 'string' } },
                        threat_level: { type: 'string', enum: ['high', 'medium', 'low'] }
                    }
                }
            });
            return Array.isArray(response) ? response : [];
        }
        catch (error) {
            logger.warn('Competitive analysis failed, using fallback', error);
            return [];
        }
    }
    async generateRecommendations(state, industryInsights, challengesAndOpportunities, tools, logger) {
        try {
            const prompt = `Based on the following analysis, generate 5-7 strategic recommendations for the client:
      
      Client: ${state.client?.name}
      Industry: ${state.client?.industry}
      Project: ${state.project?.description}
      
      Industry Insights: ${JSON.stringify(industryInsights)}
      Challenges: ${JSON.stringify(challengesAndOpportunities.challenges)}
      Opportunities: ${JSON.stringify(challengesAndOpportunities.opportunities)}
      
      Provide actionable, specific recommendations as a JSON array of strings.`;
            const response = await tools.ai.generateStructuredData(prompt, {
                type: 'array',
                items: { type: 'string' }
            });
            return Array.isArray(response) ? response : response.recommendations || [];
        }
        catch (error) {
            logger.warn('Recommendations generation failed, using fallback', error);
            return [
                'Focus on digital transformation initiatives',
                'Invest in customer experience improvements',
                'Develop competitive differentiation strategies',
                'Optimize operational efficiency',
                'Explore new market opportunities'
            ];
        }
    }
    calculateConfidenceScore(state, industryInsights, competitiveLandscape) {
        let score = 50; // Base score
        // Increase confidence based on available data
        if (state.client?.industry && state.client.industry !== 'Unknown')
            score += 15;
        if (state.client?.size && state.client.size !== 'Unknown')
            score += 10;
        if (state.client?.description && state.client.description.length > 50)
            score += 10;
        if (state.client?.location && state.client.location !== 'Unknown')
            score += 5;
        if (industryInsights.length > 3)
            score += 10;
        if (competitiveLandscape.length > 0)
            score += 10;
        return Math.min(score, 95); // Cap at 95%
    }
    createFallbackAnalysis(state) {
        return {
            industry_insights: this.getFallbackIndustryInsights(state.client?.industry),
            market_position: 'Emerging Player',
            key_challenges: ['Digital transformation needs', 'Market competition', 'Resource optimization'],
            opportunities: ['Technology adoption', 'Market expansion', 'Process improvement'],
            competitive_landscape: [],
            recommendations: [
                'Focus on core competencies and differentiation',
                'Invest in technology and digital capabilities',
                'Develop strategic partnerships',
                'Optimize operational efficiency',
                'Enhance customer experience'
            ]
        };
    }
    getFallbackIndustryInsights(industry) {
        const genericInsights = [
            {
                category: 'Technology',
                insight: 'Digital transformation is accelerating across all industries',
                impact_level: 'high',
                timeframe: '2024-2026'
            },
            {
                category: 'Market',
                insight: 'Increased focus on customer experience and personalization',
                impact_level: 'medium',
                timeframe: '2024-2025'
            },
            {
                category: 'Operations',
                insight: 'Automation and AI adoption driving efficiency gains',
                impact_level: 'high',
                timeframe: '2024-2027'
            }
        ];
        return genericInsights;
    }
}
exports.ClientAnalysisNode = ClientAnalysisNode;
//# sourceMappingURL=ClientAnalysisNode.js.map