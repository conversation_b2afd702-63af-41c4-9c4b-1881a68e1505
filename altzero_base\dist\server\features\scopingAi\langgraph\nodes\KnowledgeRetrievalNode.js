"use strict";
// =====================================================
// KNOWLEDGE RETRIEVAL NODE - SCOPINGAI LANGGRAPH
// =====================================================
Object.defineProperty(exports, "__esModule", { value: true });
exports.KnowledgeRetrievalNode = void 0;
class KnowledgeRetrievalNode {
    constructor() {
        this.name = 'knowledge_retrieval';
        this.description = 'Retrieves relevant knowledge base documents and content for proposal generation';
    }
    async execute(context) {
        const { state, tools, logger } = context;
        logger.info('Starting knowledge base retrieval', {
            workflow_id: state.workflow_id,
            selected_documents: state.selected_knowledge_documents?.length || 0,
            has_requirements: Object.keys(state.document_requirements || {}).length > 0
        });
        try {
            const startTime = Date.now();
            let retrievalResult;
            if (state.selected_knowledge_documents && state.selected_knowledge_documents.length > 0) {
                // Use targeted retrieval with user requirements
                retrievalResult = await this.retrieveTargetedContent(state, tools, logger);
            }
            else {
                // No specific documents selected - use general search
                retrievalResult = await this.retrieveGeneralContent(state, tools, logger);
            }
            const processingTime = Date.now() - startTime;
            logger.info('Knowledge retrieval completed', {
                documents_found: retrievalResult.retrieved_documents.length,
                content_length: retrievalResult.relevant_content.length,
                processing_time: processingTime
            });
            return {
                knowledge_base_content: {
                    retrieved_documents: retrievalResult.retrieved_documents,
                    relevant_content: retrievalResult.relevant_content,
                    search_results: retrievalResult.search_results,
                    content_summary: retrievalResult.content_summary
                },
                current_step: 'knowledge_retrieval_completed',
                progress: 20,
                processing_time: (state.processing_time || 0) + processingTime,
                data_sources_used: [...(state.data_sources_used || []), 'knowledge_base'],
                api_calls_made: [
                    ...(state.api_calls_made || []),
                    {
                        provider: 'knowledge_base',
                        endpoint: 'search_by_requirements',
                        calls_made: 1,
                        success_rate: 1.0,
                        average_response_time: processingTime,
                        cost_estimate: 0,
                        timestamp: new Date().toISOString()
                    }
                ],
                last_updated: new Date().toISOString(),
                node_data: {
                    ...state.node_data,
                    knowledge_retrieval: retrievalResult
                }
            };
        }
        catch (error) {
            logger.error('Knowledge retrieval failed', error);
            // Continue with empty knowledge base content rather than failing
            logger.warn('Continuing without knowledge base content');
            return {
                knowledge_base_content: {
                    retrieved_documents: [],
                    relevant_content: '',
                    search_results: [],
                    content_summary: 'No knowledge base content available'
                },
                current_step: 'knowledge_retrieval_completed',
                progress: 20,
                warnings: [...(state.warnings || []), 'Knowledge base retrieval failed - continuing without reference content'],
                last_updated: new Date().toISOString()
            };
        }
    }
    async retrieveTargetedContent(state, tools, logger) {
        logger.info('Performing targeted knowledge retrieval', {
            document_count: state.selected_knowledge_documents.length,
            requirements_count: Object.keys(state.document_requirements).length
        });
        try {
            // Use the enhanced search by requirements
            const searchResult = await tools.knowledgeBase.searchByRequirements(state.document_requirements, state.user_id, state.selected_knowledge_documents);
            // Process search results
            const retrieved_documents = searchResult.searchResults.map((result, index) => ({
                id: result.documentId,
                title: `Requirement ${index + 1}: ${result.requirement}`,
                type: 'targeted_search',
                content: result.relevantChunks.map((chunk) => chunk.content).join('\n\n'),
                size: result.relevantChunks.reduce((total, chunk) => total + chunk.content.length, 0),
                relevance_scores: result.relevantChunks.map((chunk) => Math.round(chunk.score * 100)),
                chunks_found: result.relevantChunks.length,
                user_requirement: result.requirement
            }));
            // Create content summary
            const content_summary = this.createContentSummary(retrieved_documents, searchResult);
            const retrieval_metrics = {
                documents_found: retrieved_documents.length,
                total_content_length: searchResult.relevantContent.length,
                average_relevance_score: this.calculateAverageRelevance(retrieved_documents),
                search_time_ms: 0 // Would be measured in real implementation
            };
            logger.info('Targeted retrieval successful', {
                documents_retrieved: retrieved_documents.length,
                total_content_length: searchResult.relevantContent.length
            });
            return {
                retrieved_documents,
                relevant_content: searchResult.relevantContent,
                search_results: searchResult.searchResults,
                content_summary,
                retrieval_metrics
            };
        }
        catch (error) {
            logger.error('Targeted retrieval failed:', error);
            throw error;
        }
    }
    async retrieveGeneralContent(state, tools, logger) {
        logger.info('Performing general knowledge retrieval');
        try {
            // Get all user documents
            const userDocuments = await tools.knowledgeBase.getUserDocuments(state.user_id);
            if (userDocuments.length === 0) {
                logger.warn('No documents found for user');
                return {
                    retrieved_documents: [],
                    relevant_content: '',
                    search_results: [],
                    content_summary: 'No documents available in knowledge base',
                    retrieval_metrics: {
                        documents_found: 0,
                        total_content_length: 0,
                        average_relevance_score: 0,
                        search_time_ms: 0
                    }
                };
            }
            // Create general search query based on project and client info
            const searchQuery = this.createGeneralSearchQuery(state);
            // Perform semantic search
            const searchResult = await tools.knowledgeBase.searchByRequirements({ general: searchQuery }, state.user_id);
            const retrieved_documents = userDocuments.slice(0, 5).map((doc, index) => ({
                id: doc.id,
                title: doc.title || `Document ${index + 1}`,
                type: doc.type || 'document',
                content: doc.content || '',
                size: doc.size || 0,
                relevance_scores: [75], // Default relevance for general retrieval
                chunks_found: 1,
                user_requirement: 'General project context'
            }));
            const relevant_content = tools.knowledgeBase.formatDocumentsForAI(userDocuments.slice(0, 5));
            const content_summary = `Retrieved ${retrieved_documents.length} documents for general project context`;
            const retrieval_metrics = {
                documents_found: retrieved_documents.length,
                total_content_length: relevant_content.length,
                average_relevance_score: 75,
                search_time_ms: 0
            };
            logger.info('General retrieval successful', {
                documents_retrieved: retrieved_documents.length,
                total_content_length: relevant_content.length
            });
            return {
                retrieved_documents,
                relevant_content,
                search_results: [{ documentId: 'general', requirement: searchQuery, relevantChunks: [] }],
                content_summary,
                retrieval_metrics
            };
        }
        catch (error) {
            logger.error('General retrieval failed:', error);
            throw error;
        }
    }
    createGeneralSearchQuery(state) {
        const queryParts = [];
        if (state.client?.industry) {
            queryParts.push(state.client.industry);
        }
        if (state.project?.description) {
            // Extract key terms from project description
            const keyTerms = state.project.description
                .split(/\s+/)
                .filter(word => word.length > 3)
                .slice(0, 5);
            queryParts.push(...keyTerms);
        }
        if (state.template?.sections) {
            // Add template section names as context
            queryParts.push(...state.template.sections.slice(0, 3));
        }
        return queryParts.join(' ');
    }
    createContentSummary(documents, searchResult) {
        const totalDocs = documents.length;
        const totalChunks = documents.reduce((sum, doc) => sum + (doc.chunks_found || 0), 0);
        const avgRelevance = this.calculateAverageRelevance(documents);
        const docsWithRequirements = documents.filter(doc => doc.user_requirement).length;
        return `Retrieved ${totalDocs} documents with ${totalChunks} relevant chunks. ` +
            `Average relevance: ${avgRelevance}%. ` +
            `${docsWithRequirements} documents have specific user requirements. ` +
            `Total content: ${searchResult.relevantContent?.length || 0} characters.`;
    }
    calculateAverageRelevance(documents) {
        if (documents.length === 0)
            return 0;
        const allScores = documents.flatMap(doc => doc.relevance_scores || []);
        if (allScores.length === 0)
            return 0;
        const sum = allScores.reduce((total, score) => total + score, 0);
        return Math.round(sum / allScores.length);
    }
    // Validate retrieved content quality
    validateContentQuality(content, minLength = 100) {
        if (!content || content.trim().length < minLength) {
            return false;
        }
        // Check for meaningful content (not just whitespace or repeated characters)
        const uniqueChars = new Set(content.toLowerCase().replace(/\s/g, '')).size;
        return uniqueChars > 10;
    }
    // Extract key topics from content
    extractKeyTopics(content, maxTopics = 10) {
        if (!content)
            return [];
        // Simple keyword extraction (in production, use more sophisticated NLP)
        const words = content
            .toLowerCase()
            .replace(/[^\w\s]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length > 4);
        const wordCounts = new Map();
        words.forEach(word => {
            wordCounts.set(word, (wordCounts.get(word) || 0) + 1);
        });
        return Array.from(wordCounts.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, maxTopics)
            .map(([word]) => word);
    }
}
exports.KnowledgeRetrievalNode = KnowledgeRetrievalNode;
//# sourceMappingURL=KnowledgeRetrievalNode.js.map