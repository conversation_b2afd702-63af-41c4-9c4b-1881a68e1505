{"version": 3, "file": "ProviderConfigService.js", "sourceRoot": "", "sources": ["../../../../../../server/features/pseo/services/external/ProviderConfigService.ts"], "names": [], "mappings": ";AAAA,wDAAwD;AACxD,2CAA2C;AAC3C,wDAAwD;;;AA6DxD,MAAM,qBAAqB;IAqEzB;QApEQ,cAAS,GAAyB,EAAE,CAAC;QAE5B,kBAAa,GAA0C;YACtE,UAAU,EAAE;gBACV,IAAI,EAAE,mBAAmB;gBACzB,IAAI,EAAE,MAAM;gBACZ,WAAW,EAAE,wDAAwD;gBACrE,gBAAgB,EAAE,EAAE;gBACpB,cAAc,EAAE,KAAK;gBACrB,QAAQ,EAAE,mDAAmD;gBAC7D,YAAY,EAAE,KAAK;gBACnB,cAAc,EAAE,CAAC;gBACjB,YAAY,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;aACzC;YACD,QAAQ,EAAE;gBACR,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,MAAM;gBACZ,WAAW,EAAE,uCAAuC;gBACpD,gBAAgB,EAAE,CAAC;gBACnB,cAAc,EAAE,MAAM;gBACtB,QAAQ,EAAE,2BAA2B;gBACrC,YAAY,EAAE,EAAE;gBAChB,cAAc,EAAE,CAAC;gBACjB,YAAY,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;aACzC;YACD,WAAW,EAAE;gBACX,IAAI,EAAE,aAAa;gBACnB,IAAI,EAAE,MAAM;gBACZ,WAAW,EAAE,kCAAkC;gBAC/C,gBAAgB,EAAE,CAAC;gBACnB,cAAc,EAAE,MAAM;gBACtB,QAAQ,EAAE,iCAAiC;gBAC3C,YAAY,EAAE,GAAG;gBACjB,cAAc,EAAE,CAAC;gBACjB,YAAY,EAAE,CAAC,WAAW,CAAC;aAC5B;YACD,OAAO,EAAE;gBACP,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,2DAA2D;gBACxE,gBAAgB,EAAE,GAAG;gBACrB,cAAc,EAAE,KAAK;gBACrB,QAAQ,EAAE,4CAA4C;gBACtD,cAAc,EAAE,KAAK;gBACrB,YAAY,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,SAAS,CAAC;aACnD;YACD,MAAM,EAAE;gBACN,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,wCAAwC;gBACrD,gBAAgB,EAAE,GAAG;gBACrB,cAAc,EAAE,KAAK;gBACrB,QAAQ,EAAE,wBAAwB;gBAClC,cAAc,EAAE,KAAK;gBACrB,YAAY,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC;aACtC;YACD,cAAc,EAAE;gBACd,IAAI,EAAE,gBAAgB;gBACtB,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,oCAAoC;gBACjD,gBAAgB,EAAE,EAAE;gBACpB,cAAc,EAAE,KAAK;gBACrB,QAAQ,EAAE,kCAAkC;gBAC5C,cAAc,EAAE,KAAK;gBACrB,YAAY,EAAE,CAAC,WAAW,CAAC;aAC5B;SACF,CAAC;QAGA,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAC/B,CAAC;IAED;;OAEG;IACK,qBAAqB;QAC3B,mCAAmC;QACnC,IAAI,CAAC,SAAS,GAAG;YACf,sBAAsB,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAyC;YAC7E,qBAAqB,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB;YAExD,sBAAsB,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAyC;YAC7E,qBAAqB,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB;YAExD,qBAAqB,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAwC;YAC3E,oBAAoB,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB;YAEtD,oBAAoB,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAuC;YACzE,mBAAmB,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB;YAEpD,cAAc,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc;YAC1C,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW;YACpC,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW;SACrC,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,sCAAsC,EAAE;YAClD,mBAAmB,EAAE,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC;iBAChD,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC;iBAC5D,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;SACvB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,IAAqB;QAC1C,MAAM,MAAM,GAAG,OAAO,IAAI,CAAC,WAAW,EAAE,WAAyC,CAAC;QAClF,MAAM,SAAS,GAAG,OAAO,IAAI,CAAC,WAAW,EAAE,UAAwC,CAAC;QAEpF,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAoB,CAAC;QAC3D,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAW,CAAC;QAEnD,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAClD,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9C,OAAO,CAAC,IAAI,CAAC,YAAY,QAAQ,qBAAqB,IAAI,WAAW,CAAC,CAAC;YACvE,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO;YACL,QAAQ;YACR,MAAM,EAAE;gBACN,MAAM;gBACN,OAAO,EAAE,CAAC,CAAC,MAAM;gBACjB,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE,YAAY,CAAC,gBAAgB,CAAC;gBAChF,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,YAAY,CAAC,cAAc,CAAC;gBACzE,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC,CAAC;aAChD;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,yBAAyB;QACvB,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC;YACnD,SAAS,EAAE,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC;YACnD,QAAQ,EAAE,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC;YACjD,OAAO,EAAE,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC;SAChD,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,gCAAgC,CAAC,IAAqB;QACpD,OAAO,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC;aACtC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;aACvD,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,QAAyB;QACvC,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,qBAAqB;QAMnB,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,WAAW,GAAa,EAAE,CAAC;QAEjC,+CAA+C;QAC/C,MAAM,mBAAmB,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAC7D,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,KAAK,IAAI,CAAC,CAAC;QAE1F,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,MAAM,CAAC,IAAI,CAAC,+GAA+G,CAAC,CAAC;QAC/H,CAAC;QAED,oCAAoC;QACpC,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,EAAE;YAC7D,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAC3D,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAuB,CAAC,EAAE,CAAC;oBACjE,MAAM,CAAC,IAAI,CAAC,YAAY,MAAM,CAAC,QAAQ,qBAAqB,IAAI,WAAW,CAAC,CAAC;gBAC/E,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,sCAAsC;QACtC,MAAM,SAAS,GAAsB,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;QACvF,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACvB,MAAM,WAAW,GAAG,OAAO,IAAI,CAAC,WAAW,EAAE,WAAyC,CAAC;YACvF,MAAM,MAAM,GAAG,OAAO,IAAI,CAAC,WAAW,EAAE,UAAwC,CAAC;YAEjF,MAAM,WAAW,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YAClD,MAAM,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAExC,IAAI,WAAW,IAAI,CAAC,MAAM,EAAE,CAAC;gBAC3B,QAAQ,CAAC,IAAI,CAAC,GAAG,WAAW,eAAe,MAAM,aAAa,CAAC,CAAC;YAClE,CAAC;YAED,IAAI,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;gBAC3B,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,eAAe,WAAW,aAAa,CAAC,CAAC;YAClE,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,2BAA2B;QAC3B,IAAI,IAAI,CAAC,SAAS,CAAC,cAAc,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC;YACpF,MAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC;YAC9E,MAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC;YAC9E,MAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QACpD,CAAC;QAED,wCAAwC;QACxC,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,CAAC;YACnC,WAAW,CAAC,IAAI,CAAC,yGAAyG,CAAC,CAAC;QAC9H,CAAC;QAED,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,CAAC;YACnC,WAAW,CAAC,IAAI,CAAC,gGAAgG,CAAC,CAAC;QACrH,CAAC;QAED,IAAI,CAAC,mBAAmB,CAAC,QAAQ,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;YAClE,WAAW,CAAC,IAAI,CAAC,2FAA2F,CAAC,CAAC;QAChH,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;YACN,QAAQ;YACR,WAAW;SACZ,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,MAAM,oBAAoB,GAAG;YAC3B,SAAS,EAAE,mDAAmD;YAC9D,SAAS,EAAE,qCAAqC;YAChD,QAAQ,EAAE,wCAAwC;YAClD,OAAO,EAAE,uCAAuC;SACjD,CAAC;QAEF,OAAO;;;;;;;;gBAQK,oBAAoB,CAAC,SAAS;;;;;gBAK9B,oBAAoB,CAAC,SAAS;;;;;eAK/B,oBAAoB,CAAC,QAAQ;;;;;cAK9B,oBAAoB,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;EAqBxC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC;aACjC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,KAAK,GAAG,KAAK,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;aACpF,IAAI,CAAC,IAAI,CAAC;CACZ,CAAC,IAAI,EAAE,CAAC;IACP,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,GAA+B,EAAE,YAAoB;QAC3E,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAClC,OAAO,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;IAChD,CAAC;CACF;AAED,4CAA4C;AAC/B,QAAA,qBAAqB,GAAG,IAAI,qBAAqB,EAAE,CAAC"}