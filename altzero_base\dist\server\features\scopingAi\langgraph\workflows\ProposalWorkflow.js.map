{"version": 3, "file": "ProposalWorkflow.js", "sourceRoot": "", "sources": ["../../../../../../server/features/scopingAi/langgraph/workflows/ProposalWorkflow.ts"], "names": [], "mappings": ";AAAA,wDAAwD;AACxD,+CAA+C;AAC/C,wDAAwD;;;AAExD,uDAAoD;AAMpD,4DAAyD;AACzD,4EAAyE;AACzE,oEAAiE;AACjE,wEAAqE;AACrE,wEAAqE;AACrE,0EAAuE;AACvE,kEAA+D;AAC/D,gEAA6D;AAE7D,MAAa,gBAAiB,SAAQ,2BAAY;IAWhD,YAAY,MAAsB;QAChC,KAAK,CAAC,MAAM,CAAC,CAAC;QAHR,sBAAiB,GAAkB,IAAI,CAAC;QAK9C,mBAAmB;QACnB,IAAI,CAAC,cAAc,GAAG,IAAI,+BAAc,EAAE,CAAC;QAC3C,IAAI,CAAC,sBAAsB,GAAG,IAAI,+CAAsB,EAAE,CAAC;QAC3D,IAAI,CAAC,kBAAkB,GAAG,IAAI,uCAAkB,EAAE,CAAC;QACnD,IAAI,CAAC,oBAAoB,GAAG,IAAI,2CAAoB,EAAE,CAAC;QACvD,IAAI,CAAC,oBAAoB,GAAG,IAAI,2CAAoB,EAAE,CAAC;QACvD,IAAI,CAAC,qBAAqB,GAAG,IAAI,6CAAqB,EAAE,CAAC;QACzD,IAAI,CAAC,iBAAiB,GAAG,IAAI,qCAAiB,EAAE,CAAC;QACjD,IAAI,CAAC,gBAAgB,GAAG,IAAI,mCAAgB,EAAE,CAAC;QAE/C,kDAAkD;QAClD,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED,eAAe;QACb,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IAED,mCAAmC;IACnC,WAAW;QACT,2BAA2B;QAC3B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,EAAE;YACrC,SAAS,EAAE,YAAY;YACvB,eAAe,EAAE,CAAC;YAClB,YAAY,EAAE,EAAE;YAChB,QAAQ,EAAE,KAAK;YACf,eAAe,EAAE,EAAE;YACnB,cAAc,EAAE,CAAC;YACjB,qBAAqB,EAAE;gBACrB,SAAS,EAAE,EAAE;gBACb,SAAS,EAAE,CAAC;aACb;SACF,CAAC,CAAC;QAEH,oCAAoC;QACpC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,sBAAsB,EAAE;YAC7C,SAAS,EAAE,qBAAqB;YAChC,eAAe,EAAE,CAAC;YAClB,YAAY,EAAE,CAAC,YAAY,CAAC;YAC5B,QAAQ,EAAE,KAAK;YACf,eAAe,EAAE,GAAG;YACpB,cAAc,EAAE,CAAC;YACjB,qBAAqB,EAAE;gBACrB,SAAS,EAAE,GAAG;gBACd,SAAS,EAAE,CAAC;aACb;SACF,CAAC,CAAC;QAEH,gCAAgC;QAChC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,EAAE;YACzC,SAAS,EAAE,iBAAiB;YAC5B,eAAe,EAAE,CAAC;YAClB,YAAY,EAAE,CAAC,qBAAqB,CAAC;YACrC,QAAQ,EAAE,IAAI,EAAE,yCAAyC;YACzD,eAAe,EAAE,GAAG;YACpB,cAAc,EAAE,CAAC;YACjB,qBAAqB,EAAE;gBACrB,SAAS,EAAE,GAAG;gBACd,SAAS,EAAE,CAAC;aACb;SACF,CAAC,CAAC;QAEH,kCAAkC;QAClC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC3C,SAAS,EAAE,mBAAmB;YAC9B,eAAe,EAAE,CAAC;YAClB,YAAY,EAAE,CAAC,iBAAiB,CAAC;YACjC,QAAQ,EAAE,KAAK;YACf,eAAe,EAAE,GAAG;YACpB,cAAc,EAAE,CAAC;YACjB,qBAAqB,EAAE;gBACrB,SAAS,EAAE,GAAG;gBACd,SAAS,EAAE,CAAC;aACb;SACF,CAAC,CAAC;QAEH,kCAAkC;QAClC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC3C,SAAS,EAAE,mBAAmB;YAC9B,eAAe,EAAE,CAAC;YAClB,YAAY,EAAE,CAAC,mBAAmB,CAAC;YACnC,QAAQ,EAAE,KAAK;YACf,eAAe,EAAE,GAAG;YACpB,cAAc,EAAE,CAAC;YACjB,qBAAqB,EAAE;gBACrB,SAAS,EAAE,GAAG;gBACd,SAAS,EAAE,CAAC;aACb;SACF,CAAC,CAAC;QAEH,mCAAmC;QACnC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAC5C,SAAS,EAAE,oBAAoB;YAC/B,eAAe,EAAE,CAAC;YAClB,YAAY,EAAE,CAAC,mBAAmB,CAAC;YACnC,QAAQ,EAAE,KAAK;YACf,eAAe,EAAE,GAAG,EAAE,uCAAuC;YAC7D,cAAc,EAAE,CAAC;YACjB,qBAAqB,EAAE;gBACrB,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE,CAAC;aACb;SACF,CAAC,CAAC;QAEH,+BAA+B;QAC/B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACxC,SAAS,EAAE,gBAAgB;YAC3B,eAAe,EAAE,CAAC;YAClB,YAAY,EAAE,CAAC,oBAAoB,CAAC;YACpC,QAAQ,EAAE,IAAI,EAAE,4CAA4C;YAC5D,eAAe,EAAE,GAAG;YACpB,cAAc,EAAE,CAAC;YACjB,qBAAqB,EAAE;gBACrB,SAAS,EAAE,GAAG;gBACd,SAAS,EAAE,CAAC;aACb;SACF,CAAC,CAAC;QAEH,6BAA6B;QAC7B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACvC,SAAS,EAAE,cAAc;YACzB,eAAe,EAAE,CAAC;YAClB,YAAY,EAAE,CAAC,gBAAgB,CAAC;YAChC,QAAQ,EAAE,KAAK;YACf,eAAe,EAAE,GAAG;YACpB,cAAc,EAAE,CAAC;YACjB,qBAAqB,EAAE;gBACrB,SAAS,EAAE,GAAG;gBACd,SAAS,EAAE,CAAC;aACb;SACF,CAAC,CAAC;IACL,CAAC;IAED,+BAA+B;IAC/B,WAAW;QACT,kBAAkB;QAClB,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;QAEjC,kDAAkD;QAClD,IAAI,CAAC,kBAAkB,CACrB,YAAY,EACZ,CAAC,KAA6B,EAAE,EAAE;YAChC,IAAI,KAAK,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;gBAC9B,OAAO,KAAK,CAAC;YACf,CAAC;YACD,OAAO,qBAAqB,CAAC;QAC/B,CAAC,EACD;YACE,mBAAmB,EAAE,qBAAqB;YAC1C,GAAG,EAAE,SAAS;SACf,CACF,CAAC;QAEF,IAAI,CAAC,kBAAkB,CACrB,qBAAqB,EACrB,CAAC,KAA6B,EAAE,EAAE;YAChC,IAAI,KAAK,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;gBAC9B,OAAO,KAAK,CAAC;YACf,CAAC;YACD,sEAAsE;YACtE,OAAO,iBAAiB,CAAC;QAC3B,CAAC,EACD;YACE,eAAe,EAAE,iBAAiB;YAClC,GAAG,EAAE,SAAS;SACf,CACF,CAAC;QAEF,IAAI,CAAC,kBAAkB,CACrB,iBAAiB,EACjB,CAAC,KAA6B,EAAE,EAAE;YAChC,IAAI,KAAK,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;gBAC9B,OAAO,KAAK,CAAC;YACf,CAAC;YACD,OAAO,mBAAmB,CAAC;QAC7B,CAAC,EACD;YACE,iBAAiB,EAAE,mBAAmB;YACtC,GAAG,EAAE,SAAS;SACf,CACF,CAAC;QAEF,IAAI,CAAC,kBAAkB,CACrB,mBAAmB,EACnB,CAAC,KAA6B,EAAE,EAAE;YAChC,IAAI,KAAK,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;gBAC9B,OAAO,KAAK,CAAC;YACf,CAAC;YACD,OAAO,mBAAmB,CAAC;QAC7B,CAAC,EACD;YACE,iBAAiB,EAAE,mBAAmB;YACtC,GAAG,EAAE,SAAS;SACf,CACF,CAAC;QAEF,IAAI,CAAC,kBAAkB,CACrB,mBAAmB,EACnB,CAAC,KAA6B,EAAE,EAAE;YAChC,IAAI,KAAK,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;gBAC9B,OAAO,KAAK,CAAC;YACf,CAAC;YACD,OAAO,oBAAoB,CAAC;QAC9B,CAAC,EACD;YACE,kBAAkB,EAAE,oBAAoB;YACxC,GAAG,EAAE,SAAS;SACf,CACF,CAAC;QAEF,IAAI,CAAC,kBAAkB,CACrB,oBAAoB,EACpB,CAAC,KAA6B,EAAE,EAAE;YAChC,IAAI,KAAK,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;gBAC9B,OAAO,KAAK,CAAC;YACf,CAAC;YACD,qCAAqC;YACrC,IAAI,KAAK,CAAC,MAAM,CAAC,iBAAiB,GAAG,CAAC,EAAE,CAAC;gBACvC,OAAO,gBAAgB,CAAC;YAC1B,CAAC;YACD,OAAO,cAAc,CAAC;QACxB,CAAC,EACD;YACE,cAAc,EAAE,gBAAgB;YAChC,YAAY,EAAE,cAAc;YAC5B,GAAG,EAAE,SAAS;SACf,CACF,CAAC;QAEF,IAAI,CAAC,kBAAkB,CACrB,gBAAgB,EAChB,CAAC,KAA6B,EAAE,EAAE;YAChC,IAAI,KAAK,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;gBAC9B,OAAO,KAAK,CAAC;YACf,CAAC;YACD,0EAA0E;YAC1E,MAAM,WAAW,GAAG,KAAK,CAAC,SAAS,EAAE,cAAc,CAAC;YACpD,IACE,WAAW;gBACX,CAAC,WAAW,CAAC,wBAAwB;gBACrC,WAAW,CAAC,WAAW,GAAG,CAAC,EAC3B,CAAC;gBACD,OAAO,oBAAoB,CAAC,CAAC,2BAA2B;YAC1D,CAAC;YACD,OAAO,cAAc,CAAC;QACxB,CAAC,EACD;YACE,kBAAkB,EAAE,oBAAoB;YACxC,YAAY,EAAE,cAAc;YAC5B,GAAG,EAAE,SAAS;SACf,CACF,CAAC;QAEF,iBAAiB;QACjB,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;IACpC,CAAC;IAED,2CAA2C;IACpC,MAAM,CAAC,gBAAgB;QAC5B,OAAO;YACL,eAAe,EAAE,GAAG;YACpB,cAAc,EAAE,CAAC;YACjB,cAAc,EAAE,IAAI;YACpB,iBAAiB,EAAE,GAAG;YACtB,YAAY,EAAE,EAAE;YAChB,sBAAsB,EAAE,IAAI;YAC5B,2BAA2B,EAAE,IAAI;YACjC,cAAc,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc;YAC1C,gBAAgB,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB;YAC9C,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY;SACvC,CAAC;IACJ,CAAC;IAED,kCAAkC;IAC3B,MAAM,CAAC,cAAc,CAAC,MAAsB;QAIjD,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;YAC3B,MAAM,CAAC,IAAI,CACT,+DAA+D,CAChE,CAAC;QACJ,CAAC;QAED,IAAI,MAAM,CAAC,eAAe,GAAG,EAAE,EAAE,CAAC;YAChC,MAAM,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,MAAM,CAAC,iBAAiB,GAAG,CAAC,IAAI,MAAM,CAAC,iBAAiB,GAAG,CAAC,EAAE,CAAC;YACjE,MAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,MAAM,CAAC,YAAY,GAAG,CAAC,IAAI,MAAM,CAAC,YAAY,GAAG,EAAE,EAAE,CAAC;YACxD,MAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QACzD,CAAC;QAED,OAAO;YACL,KAAK,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC1B,MAAM;SACP,CAAC;IACJ,CAAC;IAED,2CAA2C;IACpC,MAAM,CAAC,KAAK,CAAC,MAAM,CACxB,MAAgC;QAEhC,MAAM,UAAU,GAAG,EAAE,GAAG,gBAAgB,CAAC,gBAAgB,EAAE,EAAE,GAAG,MAAM,EAAE,CAAC;QACzE,MAAM,UAAU,GAAG,gBAAgB,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAE/D,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CACb,mCAAmC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAClE,CAAC;QACJ,CAAC;QAED,OAAO,IAAI,gBAAgB,CAAC,UAAU,CAAC,CAAC;IAC1C,CAAC;IAED,oCAAoC;IAC7B,KAAK,CAAC,eAAe,CAC1B,KAA8B,EAC9B,gBAAyB;QAEzB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;QAC3D,IAAI,CAAC,iBAAiB,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,wBAAwB;QACrE,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,6DAA6D;IAE7D,2BAA2B;IACpB,KAAK,CAAC,gBAAgB,CAC3B,UAAkB;QAElB,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;QAC9D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACtD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;CACF;AArWD,4CAqWC"}