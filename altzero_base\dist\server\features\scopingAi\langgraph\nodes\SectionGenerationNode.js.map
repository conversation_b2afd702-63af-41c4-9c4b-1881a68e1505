{"version": 3, "file": "SectionGenerationNode.js", "sourceRoot": "", "sources": ["../../../../../../server/features/scopingAi/langgraph/nodes/SectionGenerationNode.ts"], "names": [], "mappings": ";AAAA,wDAAwD;AACxD,gDAAgD;AAChD,wDAAwD;;;AAKxD,MAAa,qBAAqB;IAAlC;QACE,SAAI,GAAG,oBAAoB,CAAC;QAC5B,gBAAW,GAAG,yEAAyE,CAAC;IAmX1F,CAAC;IAjXC,KAAK,CAAC,OAAO,CAAC,OAAwB;QACpC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;QAEzC,MAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE;YACzC,WAAW,EAAE,KAAK,CAAC,WAAW;YAC9B,oBAAoB,EAAE,KAAK,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,IAAI,CAAC;YAC3D,YAAY,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,iBAAiB,EAAE,OAAO,CAAC;SACnD,CAAC,CAAC;QAEH,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,iCAAiC;YACjC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;YAE3E,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE9C,MAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE;gBAC1C,kBAAkB,EAAE,aAAa,CAAC,QAAQ,CAAC,MAAM;gBACjD,WAAW,EAAE,aAAa,CAAC,gBAAgB;gBAC3C,WAAW,EAAE,aAAa,CAAC,qBAAqB;aACjD,CAAC,CAAC;YAEH,OAAO;gBACL,iBAAiB,EAAE,aAAa,CAAC,QAAQ;gBACzC,YAAY,EAAE,8BAA8B;gBAC5C,QAAQ,EAAE,EAAE;gBACZ,eAAe,EAAE,CAAC,KAAK,CAAC,eAAe,IAAI,CAAC,CAAC,GAAG,cAAc;gBAC9D,cAAc,EAAE;oBACd,GAAG,CAAC,KAAK,CAAC,cAAc,IAAI,EAAE,CAAC;oBAC/B;wBACE,QAAQ,EAAE,eAAe;wBACzB,QAAQ,EAAE,oBAAoB;wBAC9B,UAAU,EAAE,aAAa,CAAC,QAAQ,CAAC,MAAM;wBACzC,YAAY,EAAE,GAAG;wBACjB,qBAAqB,EAAE,cAAc,GAAG,aAAa,CAAC,QAAQ,CAAC,MAAM;wBACrE,aAAa,EAAE,aAAa,CAAC,kBAAkB,CAAC,UAAU;wBAC1D,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qBACpC;iBACF;gBACD,UAAU,EAAE,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC,GAAG,aAAa,CAAC,kBAAkB,CAAC,UAAU;gBACjF,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACtC,SAAS,EAAE;oBACT,GAAG,KAAK,CAAC,SAAS;oBAClB,kBAAkB,EAAE,aAAa;iBAClC;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACjD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAC/B,KAA6B,EAC7B,KAAU,EACV,MAAW;QAGX,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,EAAE,QAAQ,IAAI,CAAC,cAAc,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;QAC7F,MAAM,iBAAiB,GAAU,EAAE,CAAC;QACpC,IAAI,eAAe,GAAG,CAAC,CAAC;QACxB,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,wBAAwB;QACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACzC,MAAM,YAAY,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAEjC,IAAI,CAAC;gBACH,MAAM,CAAC,IAAI,CAAC,uBAAuB,YAAY,KAAK,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;gBAEjF,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBACpC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;gBACnF,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,gBAAgB,CAAC;gBAElD,iBAAiB,CAAC,IAAI,CAAC;oBACrB,KAAK,EAAE,YAAY;oBACnB,OAAO,EAAE,WAAW,CAAC,OAAO;oBAC5B,UAAU,EAAE,WAAW,CAAC,UAAU;oBAClC,aAAa,EAAE,WAAW,CAAC,aAAa;oBACxC,kBAAkB,EAAE,WAAW;iBAChC,CAAC,CAAC;gBAEH,eAAe,IAAI,WAAW,CAAC,WAAW,IAAI,CAAC,CAAC;gBAChD,SAAS,IAAI,WAAW,CAAC,aAAa,IAAI,CAAC,CAAC;gBAC5C,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAElC,MAAM,CAAC,KAAK,CAAC,sBAAsB,YAAY,EAAE,EAAE;oBACjD,UAAU,EAAE,WAAW,CAAC,UAAU;oBAClC,aAAa,EAAE,WAAW,CAAC,aAAa;oBACxC,eAAe,EAAE,WAAW;iBAC7B,CAAC,CAAC;YAEL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,+BAA+B,YAAY,EAAE,EAAE,KAAK,CAAC,CAAC;gBAEnE,uBAAuB;gBACvB,iBAAiB,CAAC,IAAI,CAAC;oBACrB,KAAK,EAAE,YAAY;oBACnB,OAAO,EAAE,IAAI,CAAC,yBAAyB,CAAC,YAAY,EAAE,KAAK,CAAC;oBAC5D,UAAU,EAAE,GAAG;oBACf,aAAa,EAAE,EAAE;oBACjB,kBAAkB,EAAE,CAAC;iBACtB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,MAAM,cAAc,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,CAAC,GAAG,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;QAC/F,MAAM,mBAAmB,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,CAAC,GAAG,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC,GAAG,iBAAiB,CAAC,MAAM,CAAC;QAElI,OAAO;YACL,QAAQ,EAAE,iBAAiB;YAC3B,gBAAgB,EAAE,cAAc;YAChC,qBAAqB,EAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC;YACtD,kBAAkB,EAAE;gBAClB,iBAAiB,EAAE,eAAe;gBAClC,UAAU,EAAE,SAAS;gBACrB,uBAAuB,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,GAAG,eAAe,CAAC,MAAM;aACvG;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,eAAe,CAC3B,YAAoB,EACpB,KAA6B,EAC7B,KAAU,EACV,MAAW;QAGX,iCAAiC;QACjC,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;QAEpE,2BAA2B;QAC3B,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,aAAa,EAAE;YACzD,KAAK,EAAE,aAAa;YACpB,WAAW,EAAE,GAAG;YAChB,UAAU,EAAE,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC;SACnD,CAAC,CAAC;QAEH,oBAAoB;QACpB,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;QAC9C,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,YAAY,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QAE1F,OAAO;YACL,OAAO,EAAE,OAAO,CAAC,IAAI,EAAE;YACvB,UAAU,EAAE,SAAS;YACrB,aAAa,EAAE,YAAY;YAC3B,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,EAAE,iBAAiB;YAC1D,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC,sBAAsB;SAC3E,CAAC;IACJ,CAAC;IAEO,mBAAmB,CAAC,YAAoB,EAAE,KAA6B;QAC7E,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QAC/C,MAAM,mBAAmB,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC;QACtE,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAEvD,OAAO,GAAG,WAAW;;EAEvB,eAAe;;uBAEM,YAAY;;EAEjC,mBAAmB;;;;;;;;mBAQF,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC;;;kBAG1C,KAAK,CAAC,UAAU,EAAE,cAAc,IAAI,8CAA8C;;iBAEnF,KAAK,CAAC,UAAU,EAAE,aAAa,IAAI,6CAA6C;;eAElF,YAAY,uBAAuB,CAAC;IACjD,CAAC;IAEO,cAAc,CAAC,KAA6B;QAClD,OAAO;YACC,KAAK,CAAC,MAAM,EAAE,IAAI,IAAI,SAAS;cAC7B,KAAK,CAAC,MAAM,EAAE,QAAQ,IAAI,SAAS;aACpC,KAAK,CAAC,OAAO,EAAE,KAAK,IAAI,SAAS;iBAC7B,KAAK,CAAC,OAAO,EAAE,WAAW,IAAI,yBAAyB;cAC1D,KAAK,CAAC,OAAO,EAAE,QAAQ,IAAI,kBAAkB;YAC/C,KAAK,CAAC,OAAO,EAAE,YAAY,IAAI,iBAAiB;;;EAG1D,KAAK,CAAC,iBAAiB,EAAE,OAAO,IAAI,iCAAiC;;;EAGrE,KAAK,CAAC,iBAAiB,EAAE,iBAAiB,IAAI,+BAA+B,EAAE,CAAC;IAChF,CAAC;IAEO,kBAAkB,CAAC,KAA6B;QACtD,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;YAC7B,OAAO,mDAAmD,CAAC;QAC7D,CAAC;QAED,OAAO;EACT,KAAK,CAAC,iBAAiB,CAAC,OAAO;;;EAG/B,KAAK,CAAC,iBAAiB,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,gBAAgB;;;EAGvF,KAAK,CAAC,iBAAiB,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,gBAAgB;;;EAG1F,KAAK,CAAC,sBAAsB,EAAE,eAAe,IAAI,qCAAqC,EAAE,CAAC;IACzF,CAAC;IAEO,sBAAsB,CAAC,YAAoB;QACjD,MAAM,YAAY,GAA2B;YAC3C,cAAc,EAAE;;;;;iDAK2B;YAE3C,OAAO,EAAE;;;;;uCAKwB;YAEjC,UAAU,EAAE;;;;;0CAKwB;YAEpC,QAAQ,EAAE;;;;;8CAK8B;YAExC,aAAa,EAAE;;;;;oCAKe;YAE9B,MAAM,EAAE;;;;;2CAK6B;YAErC,UAAU,EAAE;;;;;+CAK6B;SAC1C,CAAC;QAEF,OAAO,YAAY,CAAC,YAAY,CAAC,IAAI,GAAG,YAAY,CAAC,WAAW,EAAE;sCAChC,YAAY,CAAC,WAAW,EAAE;;;;mDAIb,CAAC;IAClD,CAAC;IAEO,sBAAsB,CAAC,YAAoB;QACjD,MAAM,OAAO,GAA2B;YACtC,cAAc,EAAE,GAAG;YACnB,mBAAmB,EAAE,GAAG;YACxB,OAAO,EAAE,GAAG;YACZ,UAAU,EAAE,GAAG;YACf,QAAQ,EAAE,GAAG;YACb,aAAa,EAAE,GAAG;YAClB,MAAM,EAAE,GAAG;YACX,UAAU,EAAE,GAAG;YACf,YAAY,EAAE,GAAG;SAClB,CAAC;QAEF,OAAO,OAAO,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC;IACtC,CAAC;IAEO,mBAAmB,CAAC,YAAoB;QAC9C,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,CAAC;IACpE,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAChC,YAAoB,EACpB,OAAe,EACf,KAA6B,EAC7B,KAAU;QAEV,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,8BAA8B,YAAY;;;EAG7D,OAAO;;;;;;;;;;sCAU6B,CAAC;YAEjC,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE;gBACnD,WAAW,EAAE,GAAG;gBAChB,UAAU,EAAE,EAAE;aACf,CAAC,CAAC;YAEH,MAAM,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC;YAC3D,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,8BAA8B;YAC9B,OAAO,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IAEO,wBAAwB,CAAC,OAAe;QAC9C,IAAI,KAAK,GAAG,EAAE,CAAC,CAAC,aAAa;QAE7B,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;QAC9C,IAAI,SAAS,GAAG,GAAG;YAAE,KAAK,IAAI,EAAE,CAAC;QACjC,IAAI,SAAS,GAAG,GAAG;YAAE,KAAK,IAAI,EAAE,CAAC;QAEjC,oCAAoC;QACpC,MAAM,aAAa,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;QACzF,MAAM,UAAU,GAAG,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAC7C,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CACrC,CAAC,MAAM,CAAC;QACT,KAAK,IAAI,UAAU,GAAG,CAAC,CAAC;QAExB,sBAAsB;QACtB,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC;YAAE,KAAK,IAAI,CAAC,CAAC;QAC/D,IAAI,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC;YAAE,KAAK,IAAI,CAAC,CAAC;QAEvC,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;IAC3C,CAAC;IAEO,yBAAyB,CAAC,YAAoB,EAAE,KAA6B;QACnF,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,EAAE,IAAI,IAAI,mBAAmB,CAAC;QACzD,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,EAAE,KAAK,IAAI,cAAc,CAAC;QAEvD,MAAM,eAAe,GAA2B;YAC9C,cAAc,EAAE,+CAA+C,OAAO,SAAS,MAAM,qPAAqP;YAE1U,OAAO,EAAE,gBAAgB,OAAO,mUAAmU;YAEnW,UAAU,EAAE,mCAAmC,OAAO,sSAAsS;YAE5V,QAAQ,EAAE,mBAAmB,OAAO,6SAA6S;SAClV,CAAC;QAEF,OAAO,eAAe,CAAC,YAAY,CAAC,IAAI,6DAA6D,YAAY,CAAC,WAAW,EAAE,QAAQ,OAAO,6BAA6B,MAAM,6BAA6B,YAAY,CAAC,WAAW,EAAE,sEAAsE,CAAC;IACjT,CAAC;CACF;AArXD,sDAqXC"}