{"version": 3, "file": "htmlAnalysisService.js", "sourceRoot": "", "sources": ["../../../../../../server/features/pseo/services/analysis/htmlAnalysisService.ts"], "names": [], "mappings": ";;;AAAA,iCAA8B;AA8B9B,MAAM,yBAAyB;IAC7B,KAAK,CAAC,WAAW,CAAC,WAAmB;QACnC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QAEpD,IAAI,CAAC;YACH,kDAAkD;YAClD,MAAM,GAAG,GAAG,IAAI,aAAK,CAAC,WAAW,CAAC,CAAC;YACnC,MAAM,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC;YAErC,MAAM,MAAM,GAAgB,EAAE,CAAC;YAE/B,eAAe;YACf,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAElC,yBAAyB;YACzB,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAE5C,uBAAuB;YACvB,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAE1C,oBAAoB;YACpB,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC;YACzE,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC;YAClE,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;YAE3D,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE5C,OAAO,CAAC,GAAG,CAAC,0CAA0C,MAAM,CAAC,MAAM,oBAAoB,YAAY,IAAI,CAAC,CAAC;YAEzG,OAAO;gBACL,WAAW,EAAE,MAAM,CAAC,MAAM;gBAC1B,cAAc,EAAE,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,6BAA6B;gBAC1E,QAAQ,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;gBAC/B,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;gBACtB,kBAAkB,EAAE;oBAClB,YAAY;oBACZ,YAAY,EAAE,QAAQ,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,MAAM;oBACnD,YAAY,EAAE,WAAW,CAAC,MAAM;iBACjC;gBACD,OAAO,EAAE;oBACP,QAAQ,EAAE,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;oBACxC,kBAAkB,EAAE,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC;oBAC5D,gBAAgB,EAAE,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC;iBACzD;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC3D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,UAAU,CAAC,QAAkB,EAAE,MAAmB;QACxD,qBAAqB;QACrB,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAC9C,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,UAAU;gBAChB,QAAQ,EAAE,KAAK;gBACf,OAAO,EAAE,mBAAmB;gBAC5B,QAAQ,EAAE,MAAM;gBAChB,cAAc,EAAE,4CAA4C;aAC7D,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YAC9D,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,SAAS;gBACf,QAAQ,EAAE,KAAK;gBACf,OAAO,EAAE,yCAAyC;gBAClD,QAAQ,EAAE,OAAO;gBACjB,cAAc,EAAE,wDAAwD;aACzE,CAAC,CAAC;QACL,CAAC;QAED,mBAAmB;QACnB,MAAM,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,0BAA0B,CAAC,CAAC;QAC3E,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,UAAU;gBAChB,QAAQ,EAAE,KAAK;gBACf,OAAO,EAAE,0BAA0B;gBACnC,QAAQ,EAAE,MAAM;gBAChB,cAAc,EAAE,uDAAuD;aACxE,CAAC,CAAC;QACL,CAAC;QAED,oBAAoB;QACpB,MAAM,MAAM,GAAG,QAAQ,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,UAAU;gBAChB,QAAQ,EAAE,KAAK;gBACf,OAAO,EAAE,gBAAgB;gBACzB,QAAQ,EAAE,MAAM;gBAChB,cAAc,EAAE,sCAAsC;aACvD,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,SAAS;gBACf,QAAQ,EAAE,KAAK;gBACf,OAAO,EAAE,2BAA2B,MAAM,CAAC,MAAM,GAAG;gBACpD,QAAQ,EAAE,IAAI;gBACd,cAAc,EAAE,8BAA8B;aAC/C,CAAC,CAAC;QACL,CAAC;QAED,uBAAuB;QACvB,MAAM,MAAM,GAAG,QAAQ,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAChD,IAAI,gBAAgB,GAAG,CAAC,CAAC;QACzB,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;YAC5B,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC7B,gBAAgB,EAAE,CAAC;gBACnB,IAAI,gBAAgB,IAAI,CAAC,EAAE,CAAC,CAAC,6BAA6B;oBACxD,MAAM,CAAC,IAAI,CAAC;wBACV,IAAI,EAAE,SAAS;wBACf,QAAQ,EAAE,KAAK;wBACf,OAAO,EAAE,6BAA6B;wBACtC,OAAO,EAAE,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK;wBAChD,QAAQ,EAAE,iBAAiB,KAAK,GAAG,CAAC,GAAG;wBACvC,cAAc,EAAE,2DAA2D;qBAC5E,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,oBAAoB,CAAC,QAAkB,EAAE,MAAmB;QAClE,2BAA2B;QAC3B,MAAM,IAAI,GAAG,QAAQ,CAAC,eAAe,CAAC;QACtC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC;YAC/B,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,SAAS;gBACf,QAAQ,EAAE,eAAe;gBACzB,OAAO,EAAE,wCAAwC;gBACjD,QAAQ,EAAE,MAAM;gBAChB,cAAc,EAAE,4CAA4C;aAC7D,CAAC,CAAC;QACL,CAAC;QAED,uBAAuB;QACvB,MAAM,SAAS,GAAG,QAAQ,CAAC,gBAAgB,CAAC,uCAAuC,CAAC,CAAC;QACrF,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3B,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,MAAM;gBACZ,QAAQ,EAAE,eAAe;gBACzB,OAAO,EAAE,gCAAgC;gBACzC,QAAQ,EAAE,MAAM;gBAChB,cAAc,EAAE,wCAAwC;aACzD,CAAC,CAAC;QACL,CAAC;QAED,wBAAwB;QACxB,MAAM,MAAM,GAAG,QAAQ,CAAC,gBAAgB,CAAC,yBAAyB,CAAC,CAAC;QACpE,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YAC9B,MAAM,EAAE,GAAG,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YACpC,MAAM,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACvE,MAAM,SAAS,GAAG,KAAK,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;YAEnD,IAAI,CAAC,KAAK,IAAI,CAAC,SAAS,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,eAAe;gBACtD,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,SAAS;oBACf,QAAQ,EAAE,eAAe;oBACzB,OAAO,EAAE,0BAA0B;oBACnC,OAAO,EAAE,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK;oBAClD,QAAQ,EAAE,mBAAmB,KAAK,GAAG,CAAC,GAAG;oBACzC,cAAc,EAAE,gDAAgD;iBACjE,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,kBAAkB,CAAC,QAAkB,EAAE,MAAmB;QAChE,0BAA0B;QAC1B,MAAM,wBAAwB,GAAG,QAAQ,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QACtE,IAAI,wBAAwB,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACzC,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,MAAM;gBACZ,QAAQ,EAAE,aAAa;gBACvB,OAAO,EAAE,2BAA2B,wBAAwB,CAAC,MAAM,GAAG;gBACtE,QAAQ,EAAE,SAAS;gBACnB,cAAc,EAAE,oDAAoD;aACrE,CAAC,CAAC;QACL,CAAC;QAED,6BAA6B;QAC7B,MAAM,eAAe,GAAG,QAAQ,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;QACjE,IAAI,eAAe,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YAChC,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,SAAS;gBACf,QAAQ,EAAE,aAAa;gBACvB,OAAO,EAAE,0BAA0B,eAAe,CAAC,MAAM,GAAG;gBAC5D,QAAQ,EAAE,aAAa;gBACvB,cAAc,EAAE,mDAAmD;aACpE,CAAC,CAAC;QACL,CAAC;QAED,8CAA8C;QAC9C,MAAM,MAAM,GAAG,QAAQ,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAChD,IAAI,wBAAwB,GAAG,CAAC,CAAC;QACjC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;YACrB,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;gBAClE,wBAAwB,EAAE,CAAC;YAC7B,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,wBAAwB,GAAG,CAAC,EAAE,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,MAAM;gBACZ,QAAQ,EAAE,aAAa;gBACvB,OAAO,EAAE,GAAG,wBAAwB,8BAA8B;gBAClE,QAAQ,EAAE,KAAK;gBACf,cAAc,EAAE,6CAA6C;aAC9D,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,iBAAiB,CAAC,MAAmB;QAC3C,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC,CAAC;QACnE,MAAM,WAAW,GAAG,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC,MAAM,CAAC;QAChF,MAAM,UAAU,GAAG,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,MAAM,CAAC;QAE9E,IAAI,KAAK,GAAG,GAAG,CAAC;QAChB,KAAK,IAAI,WAAW,GAAG,EAAE,CAAC;QAC1B,KAAK,IAAI,UAAU,GAAG,CAAC,CAAC;QAExB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IAC5B,CAAC;IAEO,2BAA2B,CAAC,MAAmB;QACrD,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,KAAK,eAAe,CAAC,CAAC;QAC9E,MAAM,YAAY,GAAG,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC,MAAM,CAAC;QAClF,MAAM,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,MAAM,CAAC;QAEhF,IAAI,KAAK,GAAG,GAAG,CAAC;QAChB,KAAK,IAAI,YAAY,GAAG,EAAE,CAAC;QAC3B,KAAK,IAAI,WAAW,GAAG,CAAC,CAAC;QAEzB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IAC5B,CAAC;IAEO,yBAAyB,CAAC,MAAmB;QACnD,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,KAAK,aAAa,CAAC,CAAC;QAC5E,MAAM,YAAY,GAAG,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC,MAAM,CAAC;QAClF,MAAM,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,MAAM,CAAC;QAEhF,IAAI,KAAK,GAAG,GAAG,CAAC;QAChB,KAAK,IAAI,YAAY,GAAG,EAAE,CAAC;QAC3B,KAAK,IAAI,WAAW,GAAG,EAAE,CAAC;QAE1B,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IAC5B,CAAC;CACF;AAEY,QAAA,yBAAyB,GAAG,IAAI,yBAAyB,EAAE,CAAC"}