{"version": 3, "file": "LLMService.js", "sourceRoot": "", "sources": ["../../../../../../server/features/scopingAi/services/ai/LLMService.ts"], "names": [], "mappings": ";;;AAAA,mCAAgC;AA2BhC,MAAa,UAAU;IAIrB,YAAY,SAAoB,EAAE;QAChC,IAAI,CAAC,MAAM,GAAG;YACZ,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,EAAE;YACzD,KAAK,EAAE,MAAM,CAAC,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,eAAe;YAClE,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;YACtC,SAAS,EAAE,MAAM,CAAC,SAAS,IAAI,IAAI;YACnC,OAAO,EAAE,MAAM,CAAC,OAAO,IAAI,KAAK;YAChC,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,CAAC;SACnC,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAChD,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,IAAI,eAAM,CAAC;YACvB,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM;YAC1B,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;YAC5B,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;SACnC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,QAAuB,EAAE,OAKnC;QACC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACzD,KAAK,EAAE,OAAO,EAAE,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK;gBAC1C,QAAQ,EAAE,QAAQ;gBAClB,WAAW,EAAE,OAAO,EAAE,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW;gBAC5D,UAAU,EAAE,OAAO,EAAE,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS;gBACvD,MAAM,EAAE,OAAO,EAAE,MAAM,IAAI,KAAK;aACjC,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACnC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;YAC1C,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE;gBACrC,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;oBACtB,YAAY,EAAE,QAAQ,CAAC,KAAK,CAAC,aAAa;oBAC1C,gBAAgB,EAAE,QAAQ,CAAC,KAAK,CAAC,iBAAiB;oBAClD,WAAW,EAAE,QAAQ,CAAC,KAAK,CAAC,YAAY;iBACzC,CAAC,CAAC,CAAC,SAAS;gBACb,KAAK,EAAE,QAAQ,CAAC,KAAK;gBACrB,YAAY,EAAE,MAAM,CAAC,aAAa,IAAI,SAAS;aAChD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,cAAc,GAAG,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,oBAAoB,EAAE,WAAW,CAAC,CAAC;YACjF,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,cAAc,CAAC,CAAC;YACtD,MAAM,IAAI,KAAK,CAAC,uBAAuB,cAAc,EAAE,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,CAAC,UAAU,CAAC,QAAuB,EAAE,OAI1C;QACC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACvD,KAAK,EAAE,OAAO,EAAE,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK;gBAC1C,QAAQ,EAAE,QAAQ;gBAClB,WAAW,EAAE,OAAO,EAAE,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW;gBAC5D,UAAU,EAAE,OAAO,EAAE,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS;gBACvD,MAAM,EAAE,IAAI;aACb,CAAC,CAAC;YAEH,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBACjC,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC;gBACjD,IAAI,OAAO,EAAE,CAAC;oBACZ,MAAM,OAAO,CAAC;gBAChB,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,cAAc,GAAG,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,oBAAoB,EAAE,WAAW,CAAC,CAAC;YACjF,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,cAAc,CAAC,CAAC;YACrD,MAAM,IAAI,KAAK,CAAC,sBAAsB,cAAc,EAAE,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC;gBAC/B,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE;aAC7C,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;YAEtB,OAAO,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC;QAC5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACtD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,SAAS;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;IAED,YAAY,CAAC,SAA6B;QACxC,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,EAAE,CAAC;QAE/C,qCAAqC;QACrC,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;YACrB,IAAI,CAAC,MAAM,GAAG,IAAI,eAAM,CAAC;gBACvB,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM;gBAC1B,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;gBAC5B,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;aACnC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,+CAA+C;IAC/C,cAAc,CAAC,IAAY;QACzB,4DAA4D;QAC5D,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACpC,CAAC;IAED,qDAAqD;IACrD,oBAAoB,CAAC,IAAY,EAAE,SAAiB;QAClD,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAClD,IAAI,eAAe,IAAI,SAAS,EAAE,CAAC;YACjC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,KAAK,GAAG,SAAS,GAAG,eAAe,CAAC;QAC1C,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,aAAa;QAC5E,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,eAAe,CAAC,GAAG,KAAK,CAAC;IACpD,CAAC;IAED,yDAAyD;IACzD,iBAAiB,CAAC,IAAiD;QACjE,MAAM,OAAO,GAAG;YACd,QAAQ,EAAE;;6EAE6D;YAEvE,OAAO,EAAE;;yGAE0F;YAEnG,OAAO,EAAE;;kDAEmC;YAE5C,IAAI,EAAE;;8CAEkC;SACzC,CAAC;QAEF,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;CACF;AA/JD,gCA+JC;AAED,4BAA4B;AACf,QAAA,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC"}