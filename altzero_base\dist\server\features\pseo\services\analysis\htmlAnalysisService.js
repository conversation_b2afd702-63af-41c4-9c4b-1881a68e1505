"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.serverHTMLAnalysisService = void 0;
const jsdom_1 = require("jsdom");
class ServerHTMLAnalysisService {
    async analyzeHTML(htmlContent) {
        const startTime = Date.now();
        console.log('🔍 Server-side HTML analysis started');
        try {
            // Parse HTML using JSDOM for server-side analysis
            const dom = new jsdom_1.JSDOM(htmlContent);
            const document = dom.window.document;
            const issues = [];
            // SEO Analysis
            this.analyzeSEO(document, issues);
            // Accessibility Analysis
            this.analyzeAccessibility(document, issues);
            // Performance Analysis
            this.analyzePerformance(document, issues);
            // Categorize issues
            const criticalIssues = issues.filter(issue => issue.type === 'critical');
            const warnings = issues.filter(issue => issue.type === 'warning');
            const info = issues.filter(issue => issue.type === 'info');
            const analysisTime = Date.now() - startTime;
            console.log(`✅ Server-side HTML analysis completed: ${issues.length} issues found in ${analysisTime}ms`);
            return {
                totalIssues: issues.length,
                criticalIssues: criticalIssues.slice(0, 10), // Limit for token efficiency
                warnings: warnings.slice(0, 10),
                info: info.slice(0, 5),
                performanceMetrics: {
                    analysisTime,
                    elementCount: document.querySelectorAll('*').length,
                    documentSize: htmlContent.length,
                },
                summary: {
                    seoScore: this.calculateSEOScore(issues),
                    accessibilityScore: this.calculateAccessibilityScore(issues),
                    performanceScore: this.calculatePerformanceScore(issues),
                },
            };
        }
        catch (error) {
            console.error('❌ Server-side HTML analysis error:', error);
            throw error;
        }
    }
    analyzeSEO(document, issues) {
        // Title tag analysis
        const title = document.querySelector('title');
        if (!title) {
            issues.push({
                type: 'critical',
                category: 'SEO',
                message: 'Missing title tag',
                selector: 'head',
                recommendation: 'Add a descriptive title tag to improve SEO',
            });
        }
        else if (title.textContent && title.textContent.length > 60) {
            issues.push({
                type: 'warning',
                category: 'SEO',
                message: 'Title tag too long (over 60 characters)',
                selector: 'title',
                recommendation: 'Keep title under 60 characters for better SERP display',
            });
        }
        // Meta description
        const metaDescription = document.querySelector('meta[name="description"]');
        if (!metaDescription) {
            issues.push({
                type: 'critical',
                category: 'SEO',
                message: 'Missing meta description',
                selector: 'head',
                recommendation: 'Add a meta description to improve click-through rates',
            });
        }
        // Heading structure
        const h1Tags = document.querySelectorAll('h1');
        if (h1Tags.length === 0) {
            issues.push({
                type: 'critical',
                category: 'SEO',
                message: 'Missing H1 tag',
                selector: 'body',
                recommendation: 'Add an H1 tag with your main keyword',
            });
        }
        else if (h1Tags.length > 1) {
            issues.push({
                type: 'warning',
                category: 'SEO',
                message: `Multiple H1 tags found (${h1Tags.length})`,
                selector: 'h1',
                recommendation: 'Use only one H1 tag per page',
            });
        }
        // Image alt attributes
        const images = document.querySelectorAll('img');
        let imagesWithoutAlt = 0;
        images.forEach((img, index) => {
            if (!img.getAttribute('alt')) {
                imagesWithoutAlt++;
                if (imagesWithoutAlt <= 5) { // Limit issues to avoid spam
                    issues.push({
                        type: 'warning',
                        category: 'SEO',
                        message: 'Image missing alt attribute',
                        element: img.outerHTML.substring(0, 100) + '...',
                        selector: `img:nth-child(${index + 1})`,
                        recommendation: 'Add descriptive alt text for better accessibility and SEO',
                    });
                }
            }
        });
    }
    analyzeAccessibility(document, issues) {
        // Check for lang attribute
        const html = document.documentElement;
        if (!html.getAttribute('lang')) {
            issues.push({
                type: 'warning',
                category: 'Accessibility',
                message: 'Missing lang attribute on html element',
                selector: 'html',
                recommendation: 'Add lang="en" or appropriate language code',
            });
        }
        // Check for skip links
        const skipLinks = document.querySelectorAll('a[href^="#main"], a[href^="#content"]');
        if (skipLinks.length === 0) {
            issues.push({
                type: 'info',
                category: 'Accessibility',
                message: 'No skip navigation links found',
                selector: 'body',
                recommendation: 'Add skip links for keyboard navigation',
            });
        }
        // Check for form labels
        const inputs = document.querySelectorAll('input, textarea, select');
        inputs.forEach((input, index) => {
            const id = input.getAttribute('id');
            const label = id ? document.querySelector(`label[for="${id}"]`) : null;
            const ariaLabel = input.getAttribute('aria-label');
            if (!label && !ariaLabel && index < 5) { // Limit issues
                issues.push({
                    type: 'warning',
                    category: 'Accessibility',
                    message: 'Form input missing label',
                    element: input.outerHTML.substring(0, 100) + '...',
                    selector: `input:nth-child(${index + 1})`,
                    recommendation: 'Associate input with a label or add aria-label',
                });
            }
        });
    }
    analyzePerformance(document, issues) {
        // Check for inline styles
        const elementsWithInlineStyles = document.querySelectorAll('[style]');
        if (elementsWithInlineStyles.length > 10) {
            issues.push({
                type: 'info',
                category: 'Performance',
                message: `Too many inline styles (${elementsWithInlineStyles.length})`,
                selector: '[style]',
                recommendation: 'Move inline styles to CSS files for better caching',
            });
        }
        // Check for external scripts
        const externalScripts = document.querySelectorAll('script[src]');
        if (externalScripts.length > 15) {
            issues.push({
                type: 'warning',
                category: 'Performance',
                message: `Many external scripts (${externalScripts.length})`,
                selector: 'script[src]',
                recommendation: 'Consider bundling or removing unnecessary scripts',
            });
        }
        // Check for large images without lazy loading
        const images = document.querySelectorAll('img');
        let imagesWithoutLazyLoading = 0;
        images.forEach((img) => {
            if (!img.getAttribute('loading') && !img.getAttribute('data-src')) {
                imagesWithoutLazyLoading++;
            }
        });
        if (imagesWithoutLazyLoading > 5) {
            issues.push({
                type: 'info',
                category: 'Performance',
                message: `${imagesWithoutLazyLoading} images without lazy loading`,
                selector: 'img',
                recommendation: 'Add loading="lazy" to images below the fold',
            });
        }
    }
    calculateSEOScore(issues) {
        const seoIssues = issues.filter(issue => issue.category === 'SEO');
        const criticalSEO = seoIssues.filter(issue => issue.type === 'critical').length;
        const warningSEO = seoIssues.filter(issue => issue.type === 'warning').length;
        let score = 100;
        score -= criticalSEO * 15;
        score -= warningSEO * 5;
        return Math.max(0, score);
    }
    calculateAccessibilityScore(issues) {
        const a11yIssues = issues.filter(issue => issue.category === 'Accessibility');
        const criticalA11y = a11yIssues.filter(issue => issue.type === 'critical').length;
        const warningA11y = a11yIssues.filter(issue => issue.type === 'warning').length;
        let score = 100;
        score -= criticalA11y * 20;
        score -= warningA11y * 8;
        return Math.max(0, score);
    }
    calculatePerformanceScore(issues) {
        const perfIssues = issues.filter(issue => issue.category === 'Performance');
        const criticalPerf = perfIssues.filter(issue => issue.type === 'critical').length;
        const warningPerf = perfIssues.filter(issue => issue.type === 'warning').length;
        let score = 100;
        score -= criticalPerf * 25;
        score -= warningPerf * 10;
        return Math.max(0, score);
    }
}
exports.serverHTMLAnalysisService = new ServerHTMLAnalysisService();
//# sourceMappingURL=htmlAnalysisService.js.map