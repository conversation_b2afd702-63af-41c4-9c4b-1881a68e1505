"use strict";
// =====================================================
// MAIN PSEO WORKFLOW - LANGGRAPH ORCHESTRATION
// =====================================================
Object.defineProperty(exports, "__esModule", { value: true });
exports.PSEOWorkflow = void 0;
const BaseWorkflow_1 = require("../core/BaseWorkflow");
const ValidationNode_1 = require("../nodes/ValidationNode");
const KeywordResearchNode_1 = require("../nodes/KeywordResearchNode");
const CompetitorAnalysisNode_1 = require("../nodes/CompetitorAnalysisNode");
class PSEOWorkflow extends BaseWorkflow_1.BaseWorkflow {
    constructor(config) {
        super(config);
        // Initialize nodes
        this.validationNode = new ValidationNode_1.ValidationNode();
        this.keywordResearchNode = new KeywordResearchNode_1.KeywordResearchNode();
        this.competitorAnalysisNode = new CompetitorAnalysisNode_1.CompetitorAnalysisNode();
        // Now initialize the workflow after nodes are created
        this.initializeWorkflow();
    }
    getWorkflowName() {
        return 'PSEOWorkflow';
    }
    // Define all nodes in the workflow
    defineNodes() {
        // Register validation node
        this.registerNode(this.validationNode, {
            node_name: 'validation',
            execution_order: 1,
            dependencies: [],
            optional: false,
            timeout_seconds: 30,
            retry_attempts: 2,
            resource_requirements: {
                memory_mb: 64,
                cpu_cores: 1
            }
        });
        // Register keyword research node
        this.registerNode(this.keywordResearchNode, {
            node_name: 'keyword_research',
            execution_order: 2,
            dependencies: ['validation'],
            optional: false,
            timeout_seconds: 300,
            retry_attempts: 3,
            resource_requirements: {
                memory_mb: 256,
                cpu_cores: 1
            }
        });
        // Skip competitor analysis for now - causing issues and HTTP 429 errors
        // Will be re-enabled later when stable
        // Register completion node
        this.registerNode({
            name: 'completion',
            description: 'Finalizes workflow execution and prepares results',
            execute: async (context) => {
                const { state, logger } = context;
                logger.info('Finalizing workflow execution', {
                    workflow_id: state.workflow_id,
                    keywords_found: state.keywords.length,
                    clusters_created: state.keyword_clusters.length
                });
                // Save results to database
                await this.saveResultsToDatabase(state, context);
                return {
                    status: 'completed',
                    current_step: 'workflow_completed',
                    progress: 100,
                    completed_at: new Date().toISOString(),
                    last_updated: new Date().toISOString(),
                    // Include keywords and clusters in final state for results endpoint
                    keywords: state.keywords,
                    keyword_clusters: state.keyword_clusters,
                    data_sources_used: state.data_sources_used,
                    processing_time: Date.now() - new Date(state.started_at).getTime(),
                    api_calls_made: state.api_calls_made,
                    total_cost: state.total_cost
                };
            }
        }, {
            node_name: 'completion',
            execution_order: 3,
            dependencies: ['keyword_research'],
            optional: false,
            timeout_seconds: 60,
            retry_attempts: 2,
            resource_requirements: {
                memory_mb: 128,
                cpu_cores: 1
            }
        });
    }
    // Define edges (workflow flow)
    defineEdges() {
        // Set entry point
        this.setEntryPoint('validation');
        // Define the workflow flow
        this.addConditionalEdge('validation', (state) => {
            // Check if validation passed
            if (state.status === 'failed') {
                return 'end';
            }
            return 'keyword_research';
        }, {
            'keyword_research': 'keyword_research',
            'end': '__end__'
        });
        this.addConditionalEdge('keyword_research', (state) => {
            // Check if keyword research was successful
            if (state.status === 'failed') {
                return 'end';
            }
            // Skip competitor analysis - go directly to completion
            return 'completion';
        }, {
            'completion': 'completion',
            'end': '__end__'
        });
        // Competitor analysis removed - workflow goes directly from keyword_research to completion
        // Set exit point
        this.setExitPoint('completion');
    }
    // Save results to database
    async saveResultsToDatabase(state, context) {
        try {
            const { tools, logger } = context;
            // Save keywords directly to pseo_keywords table (pseo_keyword_research table doesn't exist)
            logger.info('Saving keywords to pseo_keywords table:', {
                workflow_id: state.workflow_id,
                keywords_count: (state.keywords || []).length,
                website_id: state.website_id
            });
            // Save individual keywords to pseo_keywords table
            if (state.keywords && state.keywords.length > 0 && state.website_id) {
                let savedCount = 0;
                let failedCount = 0;
                logger.info('Saving keywords to pseo_keywords table:', {
                    workflow_id: state.workflow_id,
                    keywords_count: state.keywords.length,
                    website_id: state.website_id
                });
                // Try batch save first
                try {
                    await tools.database.saveKeywordData(state.website_id, state.keywords.map(kw => ({
                        keyword: kw.keyword,
                        searchVolume: kw.search_volume,
                        keywordDifficulty: Math.min(Math.max(kw.keyword_difficulty || 0, 0), 100), // Ensure 0-100 range
                        cpc: kw.cpc,
                        competition: this.mapCompetitionToAllowedValue(kw.competition),
                        rankingPosition: kw.ranking_position,
                        rankingUrl: kw.ranking_url,
                        intent: this.mapIntentToAllowedValue(kw.intent),
                        dataSource: this.mapDataSourceToAllowedValue(kw.data_source)
                    })));
                    savedCount = state.keywords.length;
                    logger.info(`✅ Successfully saved all ${savedCount} keywords to pseo_keywords table`);
                }
                catch (batchError) {
                    logger.warn('❌ Batch save failed, trying individual saves:', batchError);
                    // Fallback: Save keywords individually to preserve partial results
                    for (const keyword of state.keywords) {
                        try {
                            await tools.database.saveKeywordData(state.website_id, [{
                                    keyword: keyword.keyword,
                                    searchVolume: keyword.search_volume,
                                    keywordDifficulty: Math.min(Math.max(keyword.keyword_difficulty || 0, 0), 100),
                                    cpc: keyword.cpc,
                                    competition: this.mapCompetitionToAllowedValue(keyword.competition),
                                    rankingPosition: keyword.ranking_position,
                                    rankingUrl: keyword.ranking_url,
                                    intent: this.mapIntentToAllowedValue(keyword.intent),
                                    dataSource: this.mapDataSourceToAllowedValue(keyword.data_source)
                                }]);
                            savedCount++;
                        }
                        catch (individualError) {
                            failedCount++;
                            logger.warn(`Failed to save keyword "${keyword.keyword}":`, individualError);
                        }
                    }
                    if (savedCount > 0) {
                        logger.info(`✅ Partially successful: saved ${savedCount}/${state.keywords.length} keywords (${failedCount} failed)`);
                    }
                    else {
                        logger.error(`❌ Failed to save any keywords to database`);
                    }
                }
            }
            else {
                logger.warn('No keywords or website_id to save to database');
            }
            // Save keyword clusters to the clusters column in pseo_keywords table
            if (state.keyword_clusters && state.keyword_clusters.length > 0 && state.website_id) {
                try {
                    logger.info(`Saving ${state.keyword_clusters.length} keyword clusters to pseo_keywords table`);
                    // Store clusters in the first keyword record for this website/workflow
                    // This approach stores all clusters as JSONB in one record rather than separate rows
                    await tools.database.updateKeywordClusters(state.website_id, state.workflow_id, state.keyword_clusters);
                    logger.info(`✅ Successfully saved ${state.keyword_clusters.length} keyword clusters`);
                }
                catch (clusterError) {
                    logger.error('❌ Failed to save keyword clusters:', clusterError);
                    // Don't throw error - clusters are supplementary data
                }
            }
            logger.info('Results saved to database successfully', {
                workflow_id: state.workflow_id,
                keywords_saved: state.keywords.length,
                clusters_saved: state.keyword_clusters.length
            });
        }
        catch (error) {
            context.logger.error('Failed to save results to database', error);
            // Don't throw error to avoid failing the entire workflow
        }
    }
    // Removed insertKeywordsBatch - now using PSEODatabaseService.saveKeywordData()
    // Get workflow configuration with defaults
    static getDefaultConfig() {
        return {
            openai_api_key: process.env.OPENAI_API_KEY,
            semrush_api_key: process.env.SEMRUSH_API_KEY,
            ahrefs_api_key: process.env.AHREFS_API_KEY,
            ubersuggest_api_key: process.env.UBERSUGGEST_API_KEY,
            max_concurrent_requests: 5,
            request_timeout: 30000,
            retry_attempts: 3,
            cache_ttl: 3600
        };
    }
    // Validate workflow configuration
    static validateConfig(config) {
        const issues = [];
        if (!config.openai_api_key) {
            issues.push('OpenAI API key is required for AI-powered keyword research');
        }
        if (!config.semrush_api_key && !config.ahrefs_api_key && !config.ubersuggest_api_key && !process.env.RAPIDAPI_KEY) {
            issues.push('At least one SEO tool API key (Semrush, Ahrefs, Ubersuggest) or RapidAPI key should be configured for better results');
        }
        if (config.max_concurrent_requests && (config.max_concurrent_requests < 1 || config.max_concurrent_requests > 20)) {
            issues.push('max_concurrent_requests should be between 1 and 20');
        }
        if (config.request_timeout && (config.request_timeout < 5000 || config.request_timeout > 300000)) {
            issues.push('request_timeout should be between 5000ms and 300000ms');
        }
        return {
            valid: issues.length === 0,
            issues
        };
    }
    // Create workflow instance with validation
    static async create(config) {
        const fullConfig = { ...PSEOWorkflow.getDefaultConfig(), ...config };
        const validation = PSEOWorkflow.validateConfig(fullConfig);
        if (!validation.valid) {
            throw new Error(`Invalid workflow configuration: ${validation.issues.join(', ')}`);
        }
        return new PSEOWorkflow(fullConfig);
    }
    // Map workflow data sources to database-allowed values
    mapDataSourceToAllowedValue(dataSource) {
        const mapping = {
            'rapidapi': 'ubersuggest', // RapidAPI uses Ubersuggest
            'ubersuggest': 'ubersuggest', // Direct mapping
            'similarweb': 'dataforseo', // Map SimilarWeb to DataForSEO
            'semrush': 'dataforseo', // Map Semrush to DataForSEO
            'langgraph_workflow': 'manual', // Map workflow to manual
            'ai_generated': 'manual' // Map AI generated to manual
        };
        return mapping[dataSource || ''] || 'manual';
    }
    // Map competition values to database-allowed values
    mapCompetitionToAllowedValue(competition) {
        const allowedValues = ['low', 'medium', 'high'];
        const normalized = competition?.toLowerCase();
        if (allowedValues.includes(normalized || '')) {
            return normalized;
        }
        // Map common variations
        const mapping = {
            'easy': 'low',
            'moderate': 'medium',
            'hard': 'high',
            'difficult': 'high'
        };
        return mapping[normalized || ''] || 'medium';
    }
    // Map intent values to database-allowed values
    mapIntentToAllowedValue(intent) {
        const allowedValues = ['informational', 'navigational', 'commercial', 'transactional'];
        const normalized = intent?.toLowerCase();
        if (allowedValues.includes(normalized || '')) {
            return normalized;
        }
        // Map common variations
        const mapping = {
            'info': 'informational',
            'information': 'informational',
            'nav': 'navigational',
            'navigation': 'navigational',
            'comm': 'commercial',
            'buy': 'transactional',
            'purchase': 'transactional',
            'transaction': 'transactional'
        };
        return mapping[normalized || ''] || 'informational';
    }
}
exports.PSEOWorkflow = PSEOWorkflow;
//# sourceMappingURL=PSEOWorkflow.js.map