{"version": 3, "file": "UbersuggestService.js", "sourceRoot": "", "sources": ["../../../../../../server/features/pseo/services/external/UbersuggestService.ts"], "names": [], "mappings": ";AAAA,wDAAwD;AACxD,kCAAkC;AAClC,oCAAoC;AACpC,wDAAwD;;;AA6CxD,MAAa,kBAAkB;IAI7B,YAAY,MAAoC;QAC9C,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QAC5B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,+BAA+B,CAAC;IACnE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAClB,OAAe,EACf,WAAmB,IAAI,EACvB,WAAmB,IAAI;QAEvB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE;gBACvD,OAAO;gBACP,QAAQ;gBACR,QAAQ;aACT,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO,EAAE,QAAQ,CAAC,OAAO,IAAI,OAAO;gBACpC,MAAM,EAAE,QAAQ,CAAC,aAAa,IAAI,CAAC;gBACnC,GAAG,EAAE,QAAQ,CAAC,cAAc,IAAI,CAAC;gBACjC,eAAe,EAAE,QAAQ,CAAC,eAAe,IAAI,CAAC;gBAC9C,cAAc,EAAE,QAAQ,CAAC,cAAc,IAAI,CAAC;gBAC5C,KAAK,EAAE,QAAQ,CAAC,KAAK,IAAI,EAAE;gBAC3B,gBAAgB,EAAE,QAAQ,CAAC,gBAAgB,IAAI,EAAE;aAClD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;YACvE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CACnB,WAAmB,EACnB,WAAmB,IAAI,EACvB,WAAmB,IAAI,EACvB,QAAgB,GAAG;QAEnB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,sBAAsB,EAAE;gBAC9D,OAAO,EAAE,WAAW;gBACpB,QAAQ;gBACR,QAAQ;gBACR,KAAK;aACN,CAAC,CAAC;YAEH,MAAM,WAAW,GAA6B,QAAQ,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,CAAC;gBACtF,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,EAAE;gBAC3B,MAAM,EAAE,IAAI,CAAC,aAAa,IAAI,CAAC;gBAC/B,GAAG,EAAE,IAAI,CAAC,cAAc,IAAI,CAAC;gBAC7B,eAAe,EAAE,IAAI,CAAC,eAAe,IAAI,CAAC;gBAC1C,cAAc,EAAE,IAAI,CAAC,cAAc,IAAI,CAAC;gBACxC,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE;aACxB,CAAC,CAAC,IAAI,EAAE,CAAC;YAEV,OAAO;gBACL,WAAW;gBACX,SAAS,EAAE,QAAQ,CAAC,SAAS,IAAI,EAAE;gBACnC,YAAY,EAAE,QAAQ,CAAC,YAAY,IAAI,EAAE;gBACzC,WAAW,EAAE,QAAQ,CAAC,WAAW,IAAI,EAAE;gBACvC,iBAAiB,EAAE,QAAQ,CAAC,iBAAiB,IAAI,WAAW,CAAC,MAAM;aACpE,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iDAAiD,EAAE,KAAK,CAAC,CAAC;YACxE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CACzB,MAAc,EACd,WAAmB,IAAI;QAEvB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE;gBAC1D,MAAM;gBACN,QAAQ;aACT,CAAC,CAAC;YAEH,OAAO;gBACL,MAAM,EAAE,QAAQ,CAAC,MAAM,IAAI,MAAM;gBACjC,gBAAgB,EAAE,QAAQ,CAAC,gBAAgB,IAAI,CAAC;gBAChD,eAAe,EAAE,QAAQ,CAAC,eAAe,IAAI,CAAC;gBAC9C,aAAa,EAAE,QAAQ,CAAC,aAAa,IAAI,CAAC;gBAC1C,YAAY,EAAE,QAAQ,CAAC,YAAY,IAAI,CAAC;gBACxC,SAAS,EAAE,QAAQ,CAAC,SAAS,IAAI,CAAC;gBAClC,YAAY,EAAE,QAAQ,CAAC,YAAY,IAAI,CAAC;aACzC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uDAAuD,EAAE,KAAK,CAAC,CAAC;YAC9E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CACnB,MAAc,EACd,QAAgB,GAAG;QAEnB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,mBAAmB,EAAE;gBAC3D,MAAM;gBACN,KAAK;aACN,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,CAAC;gBAC7C,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,EAAE;gBACnB,YAAY,EAAE,IAAI,CAAC,YAAY,IAAI,CAAC;gBACpC,UAAU,EAAE,IAAI,CAAC,UAAU,IAAI,CAAC;gBAChC,SAAS,EAAE,IAAI,CAAC,SAAS,IAAI,CAAC;gBAC9B,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,IAAI,CAAC;gBAC9C,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,EAAE;gBACnC,SAAS,EAAE,IAAI,CAAC,SAAS,IAAI,UAAU;aACxC,CAAC,CAAC,IAAI,EAAE,CAAC;QACZ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iDAAiD,EAAE,KAAK,CAAC,CAAC;YACxE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAClB,MAAc,EACd,WAAmB,IAAI,EACvB,QAAgB,GAAG;QAEnB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE;gBAC1D,MAAM;gBACN,QAAQ;gBACR,KAAK;aACN,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,CAAC;gBAC5C,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,EAAE;gBAC3B,MAAM,EAAE,IAAI,CAAC,aAAa,IAAI,CAAC;gBAC/B,GAAG,EAAE,IAAI,CAAC,cAAc,IAAI,CAAC;gBAC7B,eAAe,EAAE,IAAI,CAAC,eAAe,IAAI,CAAC;gBAC1C,cAAc,EAAE,IAAI,CAAC,cAAc,IAAI,CAAC;gBACxC,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE;aACxB,CAAC,CAAC,IAAI,EAAE,CAAC;QACZ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;YACvE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CACzB,YAAoB,EACpB,iBAA2B,EAC3B,WAAmB,IAAI;QAMvB,IAAI,CAAC;YACH,iCAAiC;YACjC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC;YAE9E,mCAAmC;YACnC,MAAM,kBAAkB,GAA+B,EAAE,CAAC;YAC1D,KAAK,MAAM,UAAU,IAAI,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,uBAAuB;gBAC/E,IAAI,CAAC;oBACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC;oBACtE,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAElC,mCAAmC;oBACnC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;gBAC1D,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,IAAI,CAAC,yCAAyC,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;gBAC9E,CAAC;YACH,CAAC;YAED,iCAAiC;YACjC,MAAM,qBAAqB,GAAG,kBAAkB,CAAC,IAAI,EAAE,CAAC;YACxD,MAAM,gBAAgB,GAAG,IAAI,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;YAEnF,MAAM,aAAa,GAAG,qBAAqB,CAAC,MAAM,CAChD,OAAO,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,IAAI,OAAO,CAAC,MAAM,GAAG,GAAG,CACxF,CAAC;YAEF,MAAM,mBAAmB,GAAG,cAAc,CAAC,MAAM,CAC/C,OAAO,CAAC,EAAE,CAAC,qBAAqB,CAAC,IAAI,CACnC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,CACjE,CACF,CAAC;YAEF,MAAM,cAAc,GAAG,cAAc,CAAC,MAAM,CAC1C,OAAO,CAAC,EAAE,CAAC,CAAC,qBAAqB,CAAC,IAAI,CACpC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,CACjE,CACF,CAAC;YAEF,OAAO;gBACL,aAAa,EAAE,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;gBACzC,oBAAoB,EAAE,mBAAmB,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;gBACtD,eAAe,EAAE,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;aAC7C,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;YAChE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CAC1B,MAAc,EACd,cAAwB,EACxB,cAAwB,EAAE;QAY1B,IAAI,CAAC;YACH,sBAAsB;YACtB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;YAEhE,iDAAiD;YACjD,MAAM,eAAe,GAA6B,EAAE,CAAC;YACrD,MAAM,eAAe,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAEpD,KAAK,MAAM,OAAO,IAAI,eAAe,EAAE,CAAC;gBACtC,IAAI,CAAC;oBACH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;oBACvD,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;oBAElC,gBAAgB;oBAChB,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;gBACzD,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,IAAI,CAAC,8BAA8B,OAAO,IAAI,EAAE,KAAK,CAAC,CAAC;gBACjE,CAAC;YACH,CAAC;YAED,gDAAgD;YAChD,MAAM,WAAW,GAAG,cAAc,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9D,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YAE7D,sBAAsB;YACtB,MAAM,kBAAkB,GAAgC,EAAE,CAAC;YAC3D,MAAM,kBAAkB,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAEnD,KAAK,MAAM,UAAU,IAAI,kBAAkB,EAAE,CAAC;gBAC5C,IAAI,CAAC;oBACH,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;oBACpE,kBAAkB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;oBAExC,gBAAgB;oBAChB,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;gBAC1D,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,IAAI,CAAC,iCAAiC,UAAU,IAAI,EAAE,KAAK,CAAC,CAAC;gBACvE,CAAC;YACH,CAAC;YAED,iDAAiD;YACjD,IAAI,WAAW,CAAC;YAChB,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3B,IAAI,CAAC;oBACH,WAAW,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;gBACtE,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,IAAI,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;gBACjE,CAAC;YACH,CAAC;YAED,OAAO;gBACL,cAAc;gBACd,eAAe;gBACf,kBAAkB;gBAClB,YAAY;gBACZ,WAAW;aACZ,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mDAAmD,EAAE,KAAK,CAAC,CAAC;YAC1E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,WAAW,CAAC,QAAgB,EAAE,MAA2B;QACrE,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAE5C,6BAA6B;QAC7B,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC9C,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;YAC9C,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;gBAC1C,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE;gBAC3C,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE;oBACP,QAAQ,EAAE,kBAAkB;oBAC5B,YAAY,EAAE,mBAAmB;iBAClC;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,0BAA0B,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;YACtF,CAAC;YAED,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAEnC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBACf,MAAM,IAAI,KAAK,CAAC,0BAA0B,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;YAC1D,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACxD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,YAAY;QACV,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;CACF;AA/VD,gDA+VC"}