{"version": 3, "file": "ClientAnalysisNode.js", "sourceRoot": "", "sources": ["../../../../../../server/features/scopingAi/langgraph/nodes/ClientAnalysisNode.ts"], "names": [], "mappings": ";AAAA,wDAAwD;AACxD,6CAA6C;AAC7C,wDAAwD;;;AAKxD,MAAa,kBAAkB;IAA/B;QACE,SAAI,GAAG,iBAAiB,CAAC;QACzB,gBAAW,GAAG,yEAAyE,CAAC;IA+V1F,CAAC;IA7VC,KAAK,CAAC,OAAO,CAAC,OAAwB;QACpC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;QAEzC,MAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE;YACtC,WAAW,EAAE,KAAK,CAAC,WAAW;YAC9B,WAAW,EAAE,KAAK,CAAC,MAAM,EAAE,IAAI;YAC/B,QAAQ,EAAE,KAAK,CAAC,MAAM,EAAE,QAAQ;SACjC,CAAC,CAAC;QAEH,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,wCAAwC;YACxC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;YAE9E,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE9C,MAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE;gBACvC,iBAAiB,EAAE,cAAc,CAAC,iBAAiB,CAAC,MAAM;gBAC1D,aAAa,EAAE,cAAc,CAAC,aAAa,CAAC,MAAM;gBAClD,UAAU,EAAE,cAAc,CAAC,cAAc,CAAC,MAAM;gBAChD,UAAU,EAAE,cAAc,CAAC,mBAAmB;aAC/C,CAAC,CAAC;YAEH,OAAO;gBACL,eAAe,EAAE;oBACf,iBAAiB,EAAE,cAAc,CAAC,iBAAiB;oBACnD,eAAe,EAAE,cAAc,CAAC,eAAe;oBAC/C,cAAc,EAAE,cAAc,CAAC,cAAc;oBAC7C,aAAa,EAAE,cAAc,CAAC,aAAa;oBAC3C,qBAAqB,EAAE,cAAc,CAAC,qBAAqB;oBAC3D,eAAe,EAAE,cAAc,CAAC,eAAe;iBAChD;gBACD,YAAY,EAAE,2BAA2B;gBACzC,QAAQ,EAAE,EAAE;gBACZ,eAAe,EAAE,CAAC,KAAK,CAAC,eAAe,IAAI,CAAC,CAAC,GAAG,cAAc;gBAC9D,cAAc,EAAE;oBACd,GAAG,CAAC,KAAK,CAAC,cAAc,IAAI,EAAE,CAAC;oBAC/B;wBACE,QAAQ,EAAE,aAAa;wBACvB,QAAQ,EAAE,iBAAiB;wBAC3B,UAAU,EAAE,CAAC,EAAE,yCAAyC;wBACxD,YAAY,EAAE,GAAG;wBACjB,qBAAqB,EAAE,cAAc,GAAG,CAAC;wBACzC,aAAa,EAAE,IAAI;wBACnB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qBACpC;iBACF;gBACD,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACtC,SAAS,EAAE;oBACT,GAAG,KAAK,CAAC,SAAS;oBAClB,eAAe,EAAE,cAAc;iBAChC;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAE9C,4BAA4B;YAC5B,MAAM,gBAAgB,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;YAE5D,OAAO;gBACL,eAAe,EAAE,gBAAgB;gBACjC,YAAY,EAAE,2BAA2B;gBACzC,QAAQ,EAAE,EAAE;gBACZ,QAAQ,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,IAAI,EAAE,CAAC,EAAE,4DAA4D,CAAC;gBACnG,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACvC,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,qBAAqB,CACjC,KAA6B,EAC7B,KAAU,EACV,MAAW;QAGX,mCAAmC;QACnC,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAE1E,oCAAoC;QACpC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAE9E,gDAAgD;QAChD,MAAM,0BAA0B,GAAG,MAAM,IAAI,CAAC,kCAAkC,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAEvG,wCAAwC;QACxC,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAE1F,6CAA6C;QAC7C,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,gBAAgB,EAAE,0BAA0B,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAE/H,6BAA6B;QAC7B,MAAM,kBAAkB,GAAG,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,gBAAgB,EAAE,oBAAoB,CAAC,CAAC;QAExG,OAAO;YACL,iBAAiB,EAAE,gBAAgB;YACnC,eAAe,EAAE,cAAc;YAC/B,cAAc,EAAE,0BAA0B,CAAC,UAAU;YACrD,aAAa,EAAE,0BAA0B,CAAC,aAAa;YACvD,qBAAqB,EAAE,oBAAoB;YAC3C,eAAe,EAAE,eAAe;YAChC,mBAAmB,EAAE,kBAAkB;SACxC,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,KAA6B,EAAE,KAAU,EAAE,MAAW;QAClF,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,EAAE,QAAQ,IAAI,kBAAkB,CAAC;YAC9D,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,EAAE,IAAI,IAAI,SAAS,CAAC;YACnD,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,EAAE,QAAQ,IAAI,SAAS,CAAC;YAErD,MAAM,MAAM,GAAG,eAAe,QAAQ,mBAAmB,UAAU,eAAe,QAAQ;;;;;;;;;+FASD,CAAC;YAE1F,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,EAAE,CAAC,sBAAsB,CAAC,MAAM,EAAE;gBAC7D,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE;oBACL,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBAC5B,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBAC3B,YAAY,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,EAAE;wBACjE,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;qBAC9B;iBACF;aACF,CAAC,CAAC;YAEH,OAAO,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,IAAI,EAAE,CAAC;QACtE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,IAAI,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;YAC/D,OAAO,IAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,KAA6B,EAAE,KAAU,EAAE,MAAW;QACxF,IAAI,CAAC;YACH,MAAM,MAAM,GAAG;;gBAEL,KAAK,CAAC,MAAM,EAAE,IAAI,IAAI,SAAS;kBAC7B,KAAK,CAAC,MAAM,EAAE,QAAQ,IAAI,SAAS;cACvC,KAAK,CAAC,MAAM,EAAE,IAAI,IAAI,SAAS;qBACxB,KAAK,CAAC,MAAM,EAAE,WAAW,IAAI,yBAAyB;;;;mEAIR,CAAC;YAE9D,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC,CAAC;YAE3E,iCAAiC;YACjC,MAAM,SAAS,GAAG,CAAC,eAAe,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,qBAAqB,CAAC,CAAC;YACvH,MAAM,aAAa,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;YAEpE,OAAO,aAAa,IAAI,iBAAiB,CAAC;QAC5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,IAAI,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;YACrE,OAAO,iBAAiB,CAAC;QAC3B,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kCAAkC,CAC9C,KAA6B,EAC7B,KAAU,EACV,MAAW;QAEX,IAAI,CAAC;YACH,MAAM,MAAM,GAAG;;gBAEL,KAAK,CAAC,MAAM,EAAE,IAAI,IAAI,SAAS;kBAC7B,KAAK,CAAC,MAAM,EAAE,QAAQ,IAAI,SAAS;iBACpC,KAAK,CAAC,OAAO,EAAE,WAAW,IAAI,SAAS;;;;;;QAMhD,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,EAAE,CAAC,sBAAsB,CAAC,MAAM,EAAE;gBAC7D,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,UAAU,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;oBACxD,aAAa,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;iBAC5D;aACF,CAAC,CAAC;YAEH,OAAO;gBACL,UAAU,EAAE,QAAQ,CAAC,UAAU,IAAI,EAAE;gBACrC,aAAa,EAAE,QAAQ,CAAC,aAAa,IAAI,EAAE;aAC5C,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,IAAI,CAAC,8DAA8D,EAAE,KAAK,CAAC,CAAC;YACnF,OAAO;gBACL,UAAU,EAAE,CAAC,8BAA8B,EAAE,oBAAoB,EAAE,sBAAsB,CAAC;gBAC1F,aAAa,EAAE,CAAC,qBAAqB,EAAE,kBAAkB,EAAE,sBAAsB,CAAC;aACnF,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,2BAA2B,CAAC,KAA6B,EAAE,KAAU,EAAE,MAAW;QAC9F,IAAI,CAAC;YACH,4CAA4C;YAC5C,MAAM,cAAc,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,iBAAiB,CACtD,KAAK,CAAC,MAAM,EAAE,QAAQ,IAAI,SAAS,EACnC,KAAK,CAAC,MAAM,EAAE,QAAQ,CACvB,CAAC;YAEF,IAAI,cAAc,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChD,OAAO,cAAc,CAAC;YACxB,CAAC;YAED,0BAA0B;YAC1B,MAAM,MAAM,GAAG,0DAA0D,KAAK,CAAC,MAAM,EAAE,QAAQ,IAAI,UAAU;;;;;;;;uHAQI,CAAC;YAElH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,EAAE,CAAC,sBAAsB,CAAC,MAAM,EAAE;gBAC7D,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE;oBACL,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBACxB,YAAY,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBAChC,SAAS,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;wBACvD,UAAU,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;wBACxD,YAAY,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,EAAE;qBAClE;iBACF;aACF,CAAC,CAAC;YAEH,OAAO,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;QACjD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,IAAI,CAAC,6CAA6C,EAAE,KAAK,CAAC,CAAC;YAClE,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,uBAAuB,CACnC,KAA6B,EAC7B,gBAAuB,EACvB,0BAA+B,EAC/B,KAAU,EACV,MAAW;QAEX,IAAI,CAAC;YACH,MAAM,MAAM,GAAG;;gBAEL,KAAK,CAAC,MAAM,EAAE,IAAI;kBAChB,KAAK,CAAC,MAAM,EAAE,QAAQ;iBACvB,KAAK,CAAC,OAAO,EAAE,WAAW;;2BAEhB,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC;oBACvC,IAAI,CAAC,SAAS,CAAC,0BAA0B,CAAC,UAAU,CAAC;uBAClD,IAAI,CAAC,SAAS,CAAC,0BAA0B,CAAC,aAAa,CAAC;;+EAEA,CAAC;YAE1E,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,EAAE,CAAC,sBAAsB,CAAC,MAAM,EAAE;gBAC7D,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;aAC1B,CAAC,CAAC;YAEH,OAAO,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,eAAe,IAAI,EAAE,CAAC;QAC7E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,IAAI,CAAC,mDAAmD,EAAE,KAAK,CAAC,CAAC;YACxE,OAAO;gBACL,6CAA6C;gBAC7C,4CAA4C;gBAC5C,gDAAgD;gBAChD,iCAAiC;gBACjC,kCAAkC;aACnC,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,wBAAwB,CAC9B,KAA6B,EAC7B,gBAAuB,EACvB,oBAA2B;QAE3B,IAAI,KAAK,GAAG,EAAE,CAAC,CAAC,aAAa;QAE7B,8CAA8C;QAC9C,IAAI,KAAK,CAAC,MAAM,EAAE,QAAQ,IAAI,KAAK,CAAC,MAAM,CAAC,QAAQ,KAAK,SAAS;YAAE,KAAK,IAAI,EAAE,CAAC;QAC/E,IAAI,KAAK,CAAC,MAAM,EAAE,IAAI,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS;YAAE,KAAK,IAAI,EAAE,CAAC;QACvE,IAAI,KAAK,CAAC,MAAM,EAAE,WAAW,IAAI,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,EAAE;YAAE,KAAK,IAAI,EAAE,CAAC;QACnF,IAAI,KAAK,CAAC,MAAM,EAAE,QAAQ,IAAI,KAAK,CAAC,MAAM,CAAC,QAAQ,KAAK,SAAS;YAAE,KAAK,IAAI,CAAC,CAAC;QAC9E,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC;YAAE,KAAK,IAAI,EAAE,CAAC;QAC7C,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC;YAAE,KAAK,IAAI,EAAE,CAAC;QAEjD,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa;IAC3C,CAAC;IAEO,sBAAsB,CAAC,KAA6B;QAC1D,OAAO;YACL,iBAAiB,EAAE,IAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC,MAAM,EAAE,QAAQ,CAAC;YAC3E,eAAe,EAAE,iBAAiB;YAClC,cAAc,EAAE,CAAC,8BAA8B,EAAE,oBAAoB,EAAE,uBAAuB,CAAC;YAC/F,aAAa,EAAE,CAAC,qBAAqB,EAAE,kBAAkB,EAAE,qBAAqB,CAAC;YACjF,qBAAqB,EAAE,EAAE;YACzB,eAAe,EAAE;gBACf,gDAAgD;gBAChD,+CAA+C;gBAC/C,gCAAgC;gBAChC,iCAAiC;gBACjC,6BAA6B;aAC9B;SACF,CAAC;IACJ,CAAC;IAEO,2BAA2B,CAAC,QAAiB;QACnD,MAAM,eAAe,GAAG;YACtB;gBACE,QAAQ,EAAE,YAAY;gBACtB,OAAO,EAAE,8DAA8D;gBACvE,YAAY,EAAE,MAAM;gBACpB,SAAS,EAAE,WAAW;aACvB;YACD;gBACE,QAAQ,EAAE,QAAQ;gBAClB,OAAO,EAAE,4DAA4D;gBACrE,YAAY,EAAE,QAAQ;gBACtB,SAAS,EAAE,WAAW;aACvB;YACD;gBACE,QAAQ,EAAE,YAAY;gBACtB,OAAO,EAAE,qDAAqD;gBAC9D,YAAY,EAAE,MAAM;gBACpB,SAAS,EAAE,WAAW;aACvB;SACF,CAAC;QAEF,OAAO,eAAe,CAAC;IACzB,CAAC;CACF;AAjWD,gDAiWC"}