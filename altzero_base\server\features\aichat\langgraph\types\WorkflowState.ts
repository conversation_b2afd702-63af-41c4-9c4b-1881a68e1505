// =====================================================
// AI CHAT LANGGRAPH WORKFLOW STATE DEFINITIONS
// =====================================================

export interface NavigationAction {
  page: string;
  url: string;
  timestamp: string;
  success: boolean;
  error?: string;
}

export interface DatabaseQuery {
  query: string;
  results: any[];
  timestamp: string;
  success: boolean;
  error?: string;
  execution_time_ms: number;
}

export interface KnowledgeBaseSearch {
  query: string;
  results: any[];
  timestamp: string;
  success: boolean;
  error?: string;
  total_found: number;
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  metadata?: {
    action_triggered?: string;
    processing_time?: number;
    tokens_used?: number;
  };
}

export interface WorkflowError {
  node_name: string;
  error_message: string;
  error_code: string;
  timestamp: string;
  recoverable: boolean;
}

export interface ActionMetrics {
  action_type: string;
  calls_made: number;
  success_rate: number;
  average_response_time: number;
  total_processing_time: number;
}

// Main AI Chat workflow state interface
export interface AIChatWorkflowState {
  // Input parameters
  workflow_id: string;
  user_id: string;
  session_id: string;
  conversation_id: string;
  
  // Current conversation context
  messages: ChatMessage[];
  current_intent: 'navigation' | 'database_query' | 'knowledge_search' | 'general_chat' | 'unknown';
  detected_entities: Record<string, any>;
  
  // Processing state
  current_step: string;
  progress: number;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'paused';
  errors: WorkflowError[];
  
  // Action results
  navigation_actions: NavigationAction[];
  database_queries: DatabaseQuery[];
  knowledge_searches: KnowledgeBaseSearch[];
  
  // AI Response data
  ai_response: {
    content: string;
    confidence: number;
    reasoning: string;
    suggested_actions: string[];
  };
  
  // Metadata and metrics
  action_metrics: ActionMetrics[];
  processing_time: number;
  total_tokens_used: number;
  total_cost: number;
  
  // Timestamps
  started_at: string;
  completed_at?: string;
  last_updated: string;
  
  // Node-specific data (for passing data between nodes)
  node_data: Record<string, any>;
  
  // Configuration
  config: {
    timeout_seconds: number;
    retry_attempts: number;
    enable_caching: boolean;
    max_tokens: number;
    temperature: number;
  };
}

// Node execution result interface
export interface NodeResult {
  success: boolean;
  data?: any;
  error?: string;
  metrics?: {
    execution_time: number;
    tokens_used: number;
    actions_performed: number;
  };
  next_node?: string;
}

// Workflow execution context
export interface WorkflowContext {
  state: AIChatWorkflowState;
  tools: WorkflowTools;
  logger: WorkflowLogger;
  config: WorkflowConfig;
}

export interface WorkflowTools {
  ai: {
    generateResponse: (messages: ChatMessage[], options?: any) => Promise<string>;
    analyzeIntent: (message: string) => Promise<string>;
    extractEntities: (message: string) => Promise<Record<string, any>>;
  };
  navigation: {
    validatePage: (page: string) => boolean;
    getPageUrl: (page: string) => string;
    triggerNavigation: (page: string, userId: string) => Promise<boolean>;
  };
  database: {
    query: (sql: string, params?: any[]) => Promise<any>;
    search: (query: string, filters?: any) => Promise<any[]>;
  };
  knowledgeBase: {
    search: (query: string, maxResults?: number) => Promise<any[]>;
    getDocument: (id: string) => Promise<any>;
  };
  cache: {
    get: <T>(key: string) => Promise<T | null>;
    set: (key: string, value: any, ttl?: number) => Promise<void>;
    delete: (key: string) => Promise<void>;
  };
}

export interface WorkflowLogger {
  info: (message: string, data?: any) => void;
  warn: (message: string, data?: any) => void;
  error: (message: string, error?: any) => void;
  debug: (message: string, data?: any) => void;
}

export interface WorkflowConfig {
  openai_api_key?: string;
  max_concurrent_requests: number;
  request_timeout: number;
  retry_attempts: number;
  cache_ttl: number;
  navigation_enabled: boolean;
  database_access_enabled: boolean;
  knowledge_base_enabled: boolean;
}

// Input validation schemas
export interface ChatWorkflowInput {
  user_id: string;
  session_id: string;
  conversation_id?: string;
  message: string;
  context?: Record<string, any>;
}

export interface WorkflowValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

// Navigation specific types
export interface NavigationRequest {
  target_page: string;
  user_id: string;
  source: 'voice_command' | 'text_command' | 'ai_suggestion';
  confidence: number;
}

export interface NavigationResult {
  success: boolean;
  target_page: string;
  target_url: string;
  execution_time: number;
  error?: string;
}

// Database query types
export interface DatabaseQueryRequest {
  query: string;
  user_id: string;
  query_type: 'search' | 'analytics' | 'report';
  filters?: Record<string, any>;
}

export interface DatabaseQueryResult {
  success: boolean;
  results: any[];
  total_count: number;
  execution_time: number;
  query_hash: string;
  error?: string;
}

// Knowledge base types
export interface KnowledgeBaseRequest {
  query: string;
  user_id: string;
  max_results?: number;
  filters?: Record<string, any>;
}

export interface KnowledgeBaseResult {
  success: boolean;
  results: Array<{
    id: string;
    title: string;
    content: string;
    relevance_score: number;
    metadata: Record<string, any>;
  }>;
  total_found: number;
  execution_time: number;
  error?: string;
}
