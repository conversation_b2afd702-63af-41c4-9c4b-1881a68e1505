{"version": 3, "file": "ResearchAnalysisNode.js", "sourceRoot": "", "sources": ["../../../../../../server/features/scopingAi/langgraph/nodes/ResearchAnalysisNode.ts"], "names": [], "mappings": ";AAAA,wDAAwD;AACxD,+CAA+C;AAC/C,wDAAwD;;;AAKxD,MAAa,oBAAoB;IAAjC;QACE,SAAI,GAAG,mBAAmB,CAAC;QAC3B,gBAAW,GAAG,+FAA+F,CAAC;IAmThH,CAAC;IAjTC,KAAK,CAAC,OAAO,CAAC,OAAwB;QACpC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;QAEzC,MAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE;YACxC,WAAW,EAAE,KAAK,CAAC,WAAW;YAC9B,qBAAqB,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,sBAAsB,EAAE,gBAAgB,CAAC;YACzE,mBAAmB,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC;SAC/C,CAAC,CAAC;QAEH,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,0CAA0C;YAC1C,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;YAEhF,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE9C,MAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE;gBACzC,cAAc,EAAE,cAAc,CAAC,OAAO,CAAC,MAAM;gBAC7C,YAAY,EAAE,cAAc,CAAC,YAAY,CAAC,MAAM;gBAChD,eAAe,EAAE,cAAc,CAAC,eAAe,CAAC,MAAM;gBACtD,UAAU,EAAE,cAAc,CAAC,gBAAgB;aAC5C,CAAC,CAAC;YAEH,OAAO;gBACL,iBAAiB,EAAE;oBACjB,OAAO,EAAE,cAAc,CAAC,OAAO;oBAC/B,YAAY,EAAE,cAAc,CAAC,YAAY;oBACzC,eAAe,EAAE,cAAc,CAAC,eAAe;oBAC/C,YAAY,EAAE,cAAc,CAAC,YAAY;iBAC1C;gBACD,YAAY,EAAE,6BAA6B;gBAC3C,QAAQ,EAAE,EAAE;gBACZ,eAAe,EAAE,CAAC,KAAK,CAAC,eAAe,IAAI,CAAC,CAAC,GAAG,cAAc;gBAC9D,cAAc,EAAE;oBACd,GAAG,CAAC,KAAK,CAAC,cAAc,IAAI,EAAE,CAAC;oBAC/B;wBACE,QAAQ,EAAE,aAAa;wBACvB,QAAQ,EAAE,wBAAwB;wBAClC,UAAU,EAAE,CAAC;wBACb,YAAY,EAAE,GAAG;wBACjB,qBAAqB,EAAE,cAAc;wBACrC,aAAa,EAAE,IAAI;wBACnB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qBACpC;iBACF;gBACD,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACtC,SAAS,EAAE;oBACT,GAAG,KAAK,CAAC,SAAS;oBAClB,iBAAiB,EAAE,cAAc;iBAClC;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YAChD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,uBAAuB,CACnC,KAA6B,EAC7B,KAAU,EACV,MAAW;QAGX,oCAAoC;QACpC,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAElD,uCAAuC;QACvC,MAAM,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QAErE,6BAA6B;QAC7B,MAAM,eAAe,GAAG,MAAM,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,cAAc,EAAE;YAClE,KAAK,EAAE,aAAa;YACpB,WAAW,EAAE,GAAG;YAChB,UAAU,EAAE,IAAI;SACjB,CAAC,CAAC;QAEH,uBAAuB;QACvB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,eAAe,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAEzF,qCAAqC;QACrC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,eAAe,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAElG,6BAA6B;QAC7B,MAAM,eAAe,GAAG,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QAE1E,OAAO;YACL,OAAO,EAAE,eAAe;YACxB,YAAY,EAAE,WAAW;YACzB,eAAe,EAAE,eAAe;YAChC,YAAY,EAAE,WAAW;YACzB,cAAc,EAAE,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC;YAC5D,gBAAgB,EAAE,eAAe;SAClC,CAAC;IACJ,CAAC;IAEO,iBAAiB,CAAC,KAA6B;QACrD,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,IAAI,KAAK,CAAC,sBAAsB,EAAE,mBAAmB,EAAE,MAAM,EAAE,CAAC;YAC9D,OAAO,CAAC,IAAI,CAAC,mBAAmB,KAAK,CAAC,sBAAsB,CAAC,mBAAmB,CAAC,MAAM,aAAa,CAAC,CAAC;QACxG,CAAC;QAED,IAAI,KAAK,CAAC,eAAe,EAAE,iBAAiB,EAAE,MAAM,EAAE,CAAC;YACrD,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACpC,CAAC;QAED,IAAI,KAAK,CAAC,eAAe,EAAE,qBAAqB,EAAE,MAAM,EAAE,CAAC;YACzD,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC;YAC3B,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,EAAE,WAAW,EAAE,CAAC;YAC/B,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACvC,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,oBAAoB,CAAC,KAA6B,EAAE,WAAqB;QAC/E,MAAM,gBAAgB,GAAG,KAAK,CAAC,sBAAsB,EAAE,gBAAgB,IAAI,EAAE,CAAC;QAC9E,MAAM,cAAc,GAAG,KAAK,CAAC,eAAe,CAAC;QAC7C,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC5B,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;QAE9B,OAAO;;;iBAGM,MAAM,EAAE,IAAI,IAAI,SAAS;cAC5B,MAAM,EAAE,QAAQ,IAAI,SAAS;kBACzB,MAAM,EAAE,IAAI,IAAI,SAAS;cAC7B,MAAM,EAAE,QAAQ,IAAI,SAAS;mBACxB,OAAO,EAAE,KAAK,IAAI,SAAS;yBACrB,OAAO,EAAE,WAAW,IAAI,yBAAyB;0BAChD,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,YAAY,IAAI,EAAE,CAAC;;EAEnE,cAAc,CAAC,CAAC,CAAC;;qBAEE,cAAc,CAAC,eAAe;oBAC/B,cAAc,CAAC,cAAc,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,iBAAiB;mBAC/D,cAAc,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,iBAAiB;uBACzD,cAAc,CAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,gBAAgB;2BAChF,cAAc,CAAC,qBAAqB,EAAE,MAAM,IAAI,CAAC;CAC3E,CAAC,CAAC,CAAC,EAAE;;EAEJ,gBAAgB,CAAC,CAAC,CAAC;;EAEnB,gBAAgB;;;;;;;;CAQjB,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;EAUJ,gBAAgB,CAAC,CAAC,CAAC,0FAA0F,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;;EAWlH,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;;;;;;;;;sBAcF,KAAK,CAAC,UAAU,EAAE,uBAAuB,IAAI,eAAe,EAAE,CAAC;IACnF,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAC9B,KAA6B,EAC7B,eAAuB,EACvB,KAAU,EACV,MAAW;QAEX,IAAI,CAAC;YACH,MAAM,MAAM,GAAG;;;EAGnB,eAAe;;kGAEiF,CAAC;YAE7F,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,EAAE,CAAC,sBAAsB,CAAC,MAAM,EAAE;gBAC7D,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;aAC1B,CAAC,CAAC;YAEH,OAAO,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,IAAI,EAAE,CAAC;QACtE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,IAAI,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;YACrE,OAAO;gBACL,6EAA6E;gBAC7E,mEAAmE;gBACnE,kEAAkE;gBAClE,yDAAyD;gBACzD,8DAA8D;aAC/D,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,uBAAuB,CACnC,KAA6B,EAC7B,eAAuB,EACvB,KAAU,EACV,MAAW;QAEX,IAAI,CAAC;YACH,MAAM,MAAM,GAAG;;;EAGnB,eAAe;;;UAGP,KAAK,CAAC,MAAM,EAAE,IAAI;cACd,KAAK,CAAC,MAAM,EAAE,QAAQ;aACvB,KAAK,CAAC,OAAO,EAAE,WAAW;;oIAE6F,CAAC;YAE/H,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,EAAE,CAAC,sBAAsB,CAAC,MAAM,EAAE;gBAC7D,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;aAC1B,CAAC,CAAC;YAEH,OAAO,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,eAAe,IAAI,EAAE,CAAC;QAC7E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,IAAI,CAAC,mDAAmD,EAAE,KAAK,CAAC,CAAC;YACxE,OAAO;gBACL,uEAAuE;gBACvE,mEAAmE;gBACnE,kEAAkE;gBAClE,0DAA0D;gBAC1D,+DAA+D;gBAC/D,4DAA4D;gBAC5D,oDAAoD;aACrD,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,sBAAsB,CAAC,OAAe;QAC5C,6CAA6C;QAC7C,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;QAC9C,MAAM,YAAY,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;QAC5D,MAAM,cAAc,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,oGAAoG,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;QAE1J,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,SAAS,GAAG,IAAI;YAAE,KAAK,IAAI,EAAE,CAAC;QAClC,IAAI,SAAS,GAAG,IAAI;YAAE,KAAK,IAAI,EAAE,CAAC;QAClC,IAAI,YAAY,GAAG,CAAC;YAAE,KAAK,IAAI,EAAE,CAAC;QAClC,IAAI,cAAc,GAAG,EAAE;YAAE,KAAK,IAAI,EAAE,CAAC;QAErC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC9B,CAAC;IAEO,wBAAwB,CAAC,KAA6B,EAAE,WAAqB;QACnF,IAAI,KAAK,GAAG,EAAE,CAAC,CAAC,aAAa;QAE7B,8CAA8C;QAC9C,IAAI,KAAK,CAAC,sBAAsB,EAAE,mBAAmB,EAAE,MAAM,EAAE,CAAC;YAC9D,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QACD,IAAI,KAAK,CAAC,eAAe,EAAE,iBAAiB,EAAE,MAAM,EAAE,CAAC;YACrD,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QACD,IAAI,KAAK,CAAC,eAAe,EAAE,qBAAqB,EAAE,MAAM,EAAE,CAAC;YACzD,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QACD,IAAI,KAAK,CAAC,MAAM,EAAE,QAAQ,IAAI,KAAK,CAAC,MAAM,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YAClE,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QACD,IAAI,KAAK,CAAC,OAAO,EAAE,WAAW,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YACzE,KAAK,IAAI,CAAC,CAAC;QACb,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IAC7B,CAAC;CACF;AArTD,oDAqTC"}