"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.serverSEOScoringService = void 0;
class ServerSEOScoringService {
    constructor() {
        // Safely access environment variables on server-side
        this.lighthouseApiKey = process.env.SEO_PAGESPEED_API_KEY;
        this.semrushApiKey = process.env.SEO_KEYWORD_API_KEY;
        this.ahrefsApiKey = process.env.SEO_BACKLINK_API_KEY;
        console.log('🔧 Server SEO Service initialized:');
        console.log(`   📊 Lighthouse: ${this.lighthouseApiKey ? '✅ Configured' : '❌ Not configured'}`);
        console.log(`   🔍 Semrush: ${this.semrushApiKey ? '✅ Configured' : '❌ Not configured'}`);
        console.log(`   🔗 Ahrefs: ${this.ahrefsApiKey ? '✅ Configured' : '❌ Not configured'}`);
    }
    async performSEOScoring(htmlContent, url) {
        const startTime = Date.now();
        console.log('🔒 Server-side SEO scoring started');
        try {
            // Initialize metrics
            const metrics = {
                overallScore: 0,
                grade: 'F',
                technicalSEO: 0,
                contentQuality: 0,
                userExperience: 0,
                performance: 0,
            };
            const recommendations = [];
            const providerAnalysis = {};
            // Perform provider-specific analysis
            if (this.lighthouseApiKey) {
                console.log('📊 Running Lighthouse analysis...');
                const lighthouseData = await this.performLighthouseAnalysis(url);
                providerAnalysis.lighthouse = lighthouseData;
                if (lighthouseData) {
                    metrics.performance = Math.round(lighthouseData.performance * 100);
                    metrics.technicalSEO = Math.round(lighthouseData.seo * 100);
                    metrics.userExperience = Math.round(lighthouseData.accessibility * 100);
                    recommendations.push(...lighthouseData.recommendations);
                }
            }
            if (this.semrushApiKey) {
                console.log('🔍 Running Semrush analysis...');
                // Add Semrush API integration here
                providerAnalysis.semrush = { status: 'configured' };
            }
            if (this.ahrefsApiKey) {
                console.log('🔗 Running Ahrefs analysis...');
                // Add Ahrefs API integration here
                providerAnalysis.ahrefs = { status: 'configured' };
            }
            // Perform basic HTML content analysis
            const contentAnalysis = this.analyzeHTMLContent(htmlContent);
            metrics.contentQuality = contentAnalysis.score;
            recommendations.push(...contentAnalysis.recommendations);
            // Calculate overall score
            const scores = [
                metrics.technicalSEO || 0,
                metrics.contentQuality || 0,
                metrics.userExperience || 0,
                metrics.performance || 0,
            ].filter(score => score > 0);
            metrics.overallScore = scores.length > 0
                ? Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length)
                : 0;
            // Assign grade
            metrics.grade = this.calculateGrade(metrics.overallScore);
            const processingTime = Date.now() - startTime;
            console.log(`✅ Server-side SEO scoring completed in ${processingTime}ms`);
            return {
                metrics,
                overallScore: metrics.overallScore,
                grade: metrics.grade,
                recommendations: recommendations.slice(0, 10), // Limit recommendations
                providerAnalysis,
                processingTime,
            };
        }
        catch (error) {
            console.error('❌ Server-side SEO scoring error:', error);
            throw error;
        }
    }
    async performLighthouseAnalysis(url) {
        if (!this.lighthouseApiKey) {
            console.log('⚠️ No Lighthouse API key configured');
            return null;
        }
        try {
            const response = await fetch(`https://www.googleapis.com/pagespeedonline/v5/runPagespeed?url=${encodeURIComponent(url)}&key=${this.lighthouseApiKey}&category=performance&category=seo&category=accessibility&category=best-practices`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            });
            if (!response.ok) {
                throw new Error(`Lighthouse API error: ${response.status}`);
            }
            const data = await response.json();
            return {
                performance: data.lighthouseResult?.categories?.performance?.score || 0,
                seo: data.lighthouseResult?.categories?.seo?.score || 0,
                accessibility: data.lighthouseResult?.categories?.accessibility?.score || 0,
                bestPractices: data.lighthouseResult?.categories?.['best-practices']?.score || 0,
                recommendations: this.extractLighthouseRecommendations(data),
            };
        }
        catch (error) {
            console.error('Lighthouse API error:', error);
            return null;
        }
    }
    extractLighthouseRecommendations(data) {
        const recommendations = [];
        try {
            const audits = data.lighthouseResult?.audits || {};
            Object.values(audits).forEach((audit) => {
                if (audit.score < 1 && audit.title && audit.description) {
                    recommendations.push(`${audit.title}: ${audit.description}`);
                }
            });
        }
        catch (error) {
            console.error('Error extracting Lighthouse recommendations:', error);
        }
        return recommendations.slice(0, 5); // Limit to top 5
    }
    analyzeHTMLContent(htmlContent) {
        const recommendations = [];
        let score = 100;
        // Basic SEO checks
        if (!htmlContent.includes('<title>')) {
            score -= 15;
            recommendations.push('Add a title tag to the page');
        }
        if (!htmlContent.includes('<meta name="description"')) {
            score -= 10;
            recommendations.push('Add a meta description');
        }
        if (!htmlContent.includes('<h1>')) {
            score -= 10;
            recommendations.push('Add an H1 heading');
        }
        const imgMatches = htmlContent.match(/<img[^>]*>/g) || [];
        const imgsWithoutAlt = imgMatches.filter((img) => !img.includes('alt=')).length;
        if (imgsWithoutAlt > 0) {
            score -= Math.min(15, imgsWithoutAlt * 3);
            recommendations.push(`Add alt attributes to ${imgsWithoutAlt} images`);
        }
        return {
            score: Math.max(0, score),
            recommendations: recommendations.slice(0, 3),
        };
    }
    calculateGrade(score) {
        if (score >= 90)
            return 'A';
        if (score >= 80)
            return 'B';
        if (score >= 70)
            return 'C';
        if (score >= 60)
            return 'D';
        return 'F';
    }
}
exports.serverSEOScoringService = new ServerSEOScoringService();
//# sourceMappingURL=seoScoringService.js.map