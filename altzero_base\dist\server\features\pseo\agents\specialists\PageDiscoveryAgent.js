"use strict";
// =====================================================
// PAGE DISCOVERY AGENT - SITEMAP PARSING & CRAWLING
// =====================================================
Object.defineProperty(exports, "__esModule", { value: true });
exports.PageDiscoveryAgent = void 0;
const BaseAgent_1 = require("../core/BaseAgent");
const ProviderConfigService_1 = require("../../services/external/ProviderConfigService");
const GoogleSearchConsoleService_1 = require("../../services/external/GoogleSearchConsoleService");
const GoogleLighthouseService_1 = require("../../services/external/GoogleLighthouseService");
const GTmetrixService_1 = require("../../services/external/GTmetrixService");
class PageDiscoveryAgent extends BaseAgent_1.BaseAgent {
    constructor() {
        super('PageDiscoveryAgent', 'Discovers website pages through sitemap parsing and crawling', ['page_discovery', 'sitemap_parsing', 'web_crawling']);
    }
    async execute(context) {
        return this.executeWithMetrics(context, async () => {
            // Validate configuration and tools
            this.validateConfig(context);
            this.checkRequiredTools(context);
            const input = context.job.result_data;
            if (!this.validateInput(input)) {
                return this.createErrorResult('Invalid input parameters');
            }
            const website = context.website;
            const params = input.parameters;
            context.logger.info('Starting page discovery', {
                website_id: website.id,
                domain: website.domain,
                discovery_methods: params.discovery_methods
            });
            const discoveredPages = [];
            const sitemapUrls = [];
            const crawlErrors = [];
            let totalPagesFound = 0;
            try {
                // Step 1: Sitemap Discovery
                if (params.discovery_methods.includes('sitemap')) {
                    await this.reportProgress(context, 10, 'Discovering sitemaps');
                    const sitemaps = await this.discoverSitemaps(website.url, context);
                    sitemapUrls.push(...sitemaps);
                    await this.reportProgress(context, 30, `Found ${sitemaps.length} sitemaps`);
                }
                // Step 2: Parse Sitemaps
                if (sitemapUrls.length > 0) {
                    await this.reportProgress(context, 40, 'Parsing sitemaps');
                    const sitemapPages = await this.parseSitemaps(sitemapUrls, context);
                    discoveredPages.push(...sitemapPages);
                    totalPagesFound += sitemapPages.length;
                    await this.reportProgress(context, 60, `Discovered ${sitemapPages.length} pages from sitemaps`);
                }
                // Step 3: Crawling (if enabled)
                if (params.discovery_methods.includes('crawl')) {
                    await this.reportProgress(context, 70, 'Starting crawl discovery');
                    const crawlPages = await this.crawlWebsite(website.url, params.max_pages || 100, params.crawl_depth || 3, context);
                    // Merge with sitemap pages (deduplicate)
                    const existingUrls = new Set(discoveredPages.map(p => p.url));
                    const newCrawlPages = crawlPages.filter(p => !existingUrls.has(p.url));
                    discoveredPages.push(...newCrawlPages);
                    totalPagesFound += newCrawlPages.length;
                    await this.reportProgress(context, 90, `Found ${newCrawlPages.length} additional pages via crawling`);
                }
                // Step 4: Performance analysis with real providers
                if (discoveredPages.length > 0) {
                    await this.reportProgress(context, 85, 'Analyzing page performance with real providers');
                    const enhancedPages = await this.enhanceWithPagespeedAnalysis(discoveredPages.slice(0, Math.min(10, discoveredPages.length)), // Limit for API efficiency
                    context);
                    // Replace the first few pages with enhanced versions
                    discoveredPages.splice(0, enhancedPages.length, ...enhancedPages);
                }
                // Step 5: Save to Database
                await this.reportProgress(context, 95, 'Saving discovered pages');
                await this.saveDiscoveredPages(website.id, discoveredPages, context);
                await this.reportProgress(context, 100, 'Page discovery completed');
                return this.createSuccessResult({
                    pages_discovered: discoveredPages,
                    sitemap_urls: sitemapUrls,
                    total_pages_found: totalPagesFound,
                    crawl_errors: crawlErrors
                }, {
                    data_points_processed: totalPagesFound,
                    api_calls_made: sitemapUrls.length + Math.ceil(totalPagesFound / 10) // estimate
                });
            }
            catch (error) {
                const agentError = this.handleError(error, 'Page discovery failed');
                return this.createErrorResult(agentError.message);
            }
        });
    }
    validateInput(input) {
        this.validateCommonInput(input);
        const params = input.parameters;
        if (!params.discovery_methods || params.discovery_methods.length === 0) {
            throw new Error('At least one discovery method must be specified');
        }
        const validMethods = ['sitemap', 'crawl'];
        const invalidMethods = params.discovery_methods.filter(m => !validMethods.includes(m));
        if (invalidMethods.length > 0) {
            throw new Error(`Invalid discovery methods: ${invalidMethods.join(', ')}`);
        }
        if (params.max_pages && params.max_pages <= 0) {
            throw new Error('max_pages must be a positive number');
        }
        if (params.crawl_depth && params.crawl_depth <= 0) {
            throw new Error('crawl_depth must be a positive number');
        }
        return true;
    }
    getRequiredTools() {
        return ['http', 'crawler', 'parser', 'database', 'cache'];
    }
    // =====================================================
    // SITEMAP DISCOVERY METHODS
    // =====================================================
    async discoverSitemaps(baseUrl, context) {
        const sitemaps = [];
        const domain = this.extractDomain(baseUrl);
        // Common sitemap locations
        const commonPaths = [
            '/sitemap.xml',
            '/sitemap_index.xml',
            '/sitemaps.xml',
            '/sitemap-index.xml',
            '/wp-sitemap.xml' // WordPress
        ];
        // Check robots.txt first
        try {
            const robotsInfo = await context.tools.crawler.checkRobotsTxt(domain);
            sitemaps.push(...robotsInfo.sitemap_urls);
        }
        catch (error) {
            if (error instanceof Error && error.message.includes('not configured')) {
                context.logger.warn('Web crawler not configured - skipping robots.txt sitemap discovery', { domain });
            }
            else {
                context.logger.warn('Failed to check robots.txt', { domain, error });
            }
        }
        // Check common sitemap locations using HTTP tool instead of crawler
        for (const path of commonPaths) {
            const sitemapUrl = new URL(path, baseUrl).toString();
            try {
                const response = await context.tools.http.get(sitemapUrl, {
                    timeout: 10000,
                    follow_redirects: true
                });
                if (response.status === 200) {
                    sitemaps.push(sitemapUrl);
                }
            }
            catch (error) {
                // Silently ignore 404s and other errors for common paths
                context.logger.debug(`Sitemap not found: ${sitemapUrl}`);
            }
        }
        // Deduplicate
        return Array.from(new Set(sitemaps));
    }
    async parseSitemaps(sitemapUrls, context) {
        const allPages = [];
        for (const sitemapUrl of sitemapUrls) {
            try {
                const pages = await context.tools.crawler.parseSitemap(sitemapUrl);
                // Convert URLs to page objects
                const pageObjects = pages.map(url => ({
                    url,
                    discovered_via: 'sitemap',
                    status: 'discovered',
                    page_type: this.inferPageType(url),
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                }));
                allPages.push(...pageObjects);
                context.logger.info(`Parsed sitemap: ${sitemapUrl}`, {
                    pages_found: pages.length
                });
            }
            catch (error) {
                if (error instanceof Error && error.message.includes('not configured')) {
                    context.logger.warn(`Sitemap parser not configured - skipping sitemap: ${sitemapUrl}`);
                    // Try basic HTTP parsing as fallback
                    try {
                        const response = await context.tools.http.get(sitemapUrl);
                        if (response.status === 200) {
                            const basicUrls = this.extractUrlsFromSitemap(response.data);
                            const pageObjects = basicUrls.map((url) => ({
                                url,
                                discovered_via: 'sitemap',
                                status: 'discovered',
                                page_type: this.inferPageType(url),
                                created_at: new Date().toISOString(),
                                updated_at: new Date().toISOString()
                            }));
                            allPages.push(...pageObjects);
                            context.logger.info(`Basic sitemap parsing completed: ${sitemapUrl}`, {
                                pages_found: basicUrls.length
                            });
                        }
                    }
                    catch (httpError) {
                        context.logger.error(`Failed to parse sitemap with HTTP fallback: ${sitemapUrl}`, httpError);
                    }
                }
                else {
                    context.logger.error(`Failed to parse sitemap: ${sitemapUrl}`, error);
                }
            }
        }
        return allPages;
    }
    // =====================================================
    // CRAWLING METHODS
    // =====================================================
    async crawlWebsite(startUrl, maxPages, maxDepth, context) {
        try {
            // Check if crawler is available by attempting a test call
            await context.tools.crawler.crawlPage(startUrl, { max_depth: 0 });
        }
        catch (error) {
            if (error instanceof Error && error.message.includes('not configured')) {
                context.logger.warn('Web crawler not configured - skipping crawl discovery', {
                    startUrl,
                    message: 'Configure a crawling service to enable page crawling'
                });
                return [];
            }
            // If it's a different error, continue with the crawling attempt
        }
        const discoveredPages = [];
        const visitedUrls = new Set();
        const urlQueue = [{ url: startUrl, depth: 0 }];
        const domain = this.extractDomain(startUrl);
        while (urlQueue.length > 0 && discoveredPages.length < maxPages) {
            const { url, depth } = urlQueue.shift();
            if (visitedUrls.has(url) || depth > maxDepth) {
                continue;
            }
            visitedUrls.add(url);
            try {
                const crawlResult = await context.tools.crawler.crawlPage(url, {
                    follow_redirects: true,
                    max_depth: 1,
                    respect_robots_txt: true,
                    delay_between_requests: 1000,
                    include_assets: false
                });
                if (crawlResult.status_code === 200) {
                    // Add current page
                    const pageObject = {
                        url,
                        title: crawlResult.metadata.title,
                        meta_description: crawlResult.metadata.description,
                        discovered_via: 'crawl',
                        status: 'crawled',
                        page_type: this.inferPageType(url),
                        word_count: this.countWords(crawlResult.content),
                        response_code: crawlResult.status_code,
                        load_time_ms: crawlResult.load_time_ms,
                        last_crawled: new Date().toISOString(),
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString()
                    };
                    discoveredPages.push(pageObject);
                    // Add internal links to queue for next depth level
                    if (depth < maxDepth) {
                        const internalLinks = crawlResult.links
                            .filter(link => link.type === 'internal')
                            .filter(link => this.extractDomain(link.url) === domain)
                            .map(link => ({ url: link.url, depth: depth + 1 }));
                        urlQueue.push(...internalLinks);
                    }
                }
            }
            catch (error) {
                if (error instanceof Error && error.message.includes('not configured')) {
                    context.logger.warn('Web crawler not configured during crawling', {
                        url,
                        message: 'Configure a crawling service to enable page crawling'
                    });
                    break; // Exit crawling if crawler becomes unavailable
                }
                else {
                    context.logger.warn(`Failed to crawl page: ${url}`, { error, depth });
                }
            }
            // Small delay between requests
            await this.delay(500);
        }
        return discoveredPages;
    }
    // =====================================================
    // UTILITY METHODS
    // =====================================================
    inferPageType(url) {
        const urlLower = url.toLowerCase();
        const urlObj = new URL(url);
        // Homepage
        if (urlObj.pathname === '/' || urlObj.pathname === '') {
            return 'homepage';
        }
        // Blog patterns
        if (urlLower.includes('/blog/') || urlLower.includes('/news/') || urlLower.includes('/articles/')) {
            return 'blog';
        }
        // Product patterns
        if (urlLower.includes('/product/') || urlLower.includes('/shop/') || urlLower.includes('/store/')) {
            return 'product';
        }
        // Category patterns
        if (urlLower.includes('/category/') || urlLower.includes('/categories/') || urlLower.includes('/section/')) {
            return 'category';
        }
        return 'page';
    }
    countWords(content) {
        return content.trim().split(/\s+/).length;
    }
    extractUrlsFromSitemap(xml) {
        const urls = [];
        // Basic XML parsing using regex to extract URLs from <loc> tags
        const urlRegex = /<loc[^>]*>(.*?)<\/loc>/gi;
        let match;
        while ((match = urlRegex.exec(xml)) !== null) {
            const url = match[1].trim();
            if (url && (url.startsWith('http://') || url.startsWith('https://'))) {
                urls.push(url);
            }
        }
        return urls;
    }
    // =====================================================
    // DATABASE METHODS
    // =====================================================
    async saveDiscoveredPages(websiteId, pages, context) {
        const batchSize = 50;
        const batches = this.chunkArray(pages, batchSize);
        for (let i = 0; i < batches.length; i++) {
            const batch = batches[i];
            try {
                // Prepare batch insert data
                const insertData = batch.map(page => ({
                    website_id: websiteId,
                    url: page.url,
                    title: page.title,
                    meta_description: page.meta_description,
                    page_type: page.page_type,
                    status: page.status,
                    discovered_via: page.discovered_via,
                    last_crawled: page.last_crawled,
                    word_count: page.word_count,
                    response_code: page.response_code,
                    load_time_ms: page.load_time_ms
                }));
                // Insert batch
                await this.insertPagesBatch(insertData, context);
                context.logger.debug(`Saved batch ${i + 1}/${batches.length}`, {
                    batch_size: batch.length,
                    total_saved: (i + 1) * batchSize
                });
            }
            catch (error) {
                context.logger.error(`Failed to save batch ${i + 1}`, error);
            }
        }
    }
    async insertPagesBatch(pages, context) {
        const batchSize = 50;
        for (let i = 0; i < pages.length; i += batchSize) {
            const batch = pages.slice(i, i + batchSize);
            const values = batch.map(page => `('${page.website_id}', '${page.url}', '${page.title}', '${page.page_type}', ${page.word_count}, NOW(), NOW())`).join(', ');
            await context.tools.database.query(`
        INSERT INTO pseo_discovered_pages (
          website_id, url, title, page_type, word_count, created_at, updated_at
        ) VALUES ${values}
        ON CONFLICT (website_id, url) 
        DO UPDATE SET 
          title = EXCLUDED.title,
          page_type = EXCLUDED.page_type,
          word_count = EXCLUDED.word_count,
          updated_at = EXCLUDED.updated_at
      `);
        }
    }
    // =====================================================
    // REAL PROVIDER INTEGRATION METHODS
    // =====================================================
    /**
     * Enhance discovered pages with real pagespeed analysis
     */
    async enhanceWithPagespeedAnalysis(pages, context) {
        const enhancedPages = [];
        // Get configured pagespeed provider
        const providerConfig = ProviderConfigService_1.providerConfigService.getProviderForFunction('pagespeed');
        if (!providerConfig) {
            context.logger.warn('No pagespeed provider configured, skipping performance analysis');
            return pages;
        }
        context.logger.info(`Using ${providerConfig.provider} for pagespeed analysis`);
        // Initialize the appropriate service
        let pagespeedService;
        try {
            switch (providerConfig.provider) {
                case 'lighthouse':
                    pagespeedService = new GoogleLighthouseService_1.GoogleLighthouseService(providerConfig.config);
                    break;
                case 'gtmetrix':
                    pagespeedService = new GTmetrixService_1.GTmetrixService(providerConfig.config);
                    break;
                default:
                    context.logger.warn(`Unsupported pagespeed provider: ${providerConfig.provider}`);
                    return pages;
            }
            // Analyze each page
            for (const page of pages) {
                try {
                    const pagespeedResult = await pagespeedService.analyze(page.url);
                    if (pagespeedResult.success) {
                        enhancedPages.push({
                            ...page,
                            performance_metrics: {
                                overall_score: pagespeedResult.metrics.overall,
                                performance_score: pagespeedResult.metrics.performance,
                                accessibility_score: pagespeedResult.metrics.accessibility,
                                seo_score: pagespeedResult.metrics.seo,
                                provider_used: providerConfig.provider,
                                analysis_timestamp: pagespeedResult.timestamp
                            },
                            seo_issues: pagespeedResult.issues,
                            raw_analysis_data: pagespeedResult.rawData
                        });
                    }
                    else {
                        // Keep original page if analysis failed
                        enhancedPages.push({
                            ...page,
                            performance_metrics: null,
                            analysis_error: pagespeedResult.error
                        });
                    }
                    // Add delay to respect rate limits
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }
                catch (error) {
                    context.logger.warn(`Failed to analyze page: ${page.url}`, { error });
                    enhancedPages.push({
                        ...page,
                        performance_metrics: null,
                        analysis_error: error instanceof Error ? error.message : 'Unknown error'
                    });
                }
            }
            context.logger.info(`Enhanced ${enhancedPages.length} pages with real performance data`);
            return enhancedPages;
        }
        catch (error) {
            context.logger.error('Failed to initialize pagespeed service', error);
            return pages;
        }
    }
    /**
     * Enhance page discovery with Google Search Console data (if available)
     */
    async enhanceWithSearchConsoleData(pages, context) {
        try {
            // Check if Google Search Console is configured
            const searchConsoleService = new GoogleSearchConsoleService_1.GoogleSearchConsoleService({
                clientId: process.env.GOOGLE_SEARCH_CONSOLE_CLIENT_ID || '',
                clientSecret: process.env.GOOGLE_SEARCH_CONSOLE_CLIENT_SECRET || '',
                redirectUri: process.env.GOOGLE_SEARCH_CONSOLE_REDIRECT_URI || '',
                refreshToken: process.env.GOOGLE_SEARCH_CONSOLE_REFRESH_TOKEN || ''
            });
            if (!searchConsoleService.isAuthenticatedStatus()) {
                context.logger.info('Google Search Console not configured, skipping enhanced page data');
                return pages;
            }
            // Get search console data for each page
            const enhancedPages = [];
            for (const page of pages) {
                try {
                    // Get page performance data from Search Console
                    const searchData = await searchConsoleService.getPagePerformance(context.website.url, // siteUrl
                    page.url, // pageUrl
                    new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // startDate as string
                    new Date().toISOString().split('T')[0] // endDate as string
                    );
                    enhancedPages.push({
                        ...page,
                        search_console_data: {
                            clicks: searchData.clicks || 0,
                            impressions: searchData.impressions || 0,
                            ctr: searchData.ctr || 0,
                            position: searchData.position || 0,
                            data_period: '90d'
                        }
                    });
                }
                catch (error) {
                    context.logger.debug(`No Search Console data for: ${page.url}`);
                    enhancedPages.push(page);
                }
            }
            context.logger.info(`Enhanced ${enhancedPages.length} pages with Search Console data`);
            return enhancedPages;
        }
        catch (error) {
            context.logger.warn('Failed to enhance with Search Console data', { error });
            return pages;
        }
    }
}
exports.PageDiscoveryAgent = PageDiscoveryAgent;
//# sourceMappingURL=PageDiscoveryAgent.js.map