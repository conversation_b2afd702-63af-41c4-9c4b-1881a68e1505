"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GTmetrixService = void 0;
const BaseExternalService_1 = require("./BaseExternalService");
class GTmetrixService extends BaseExternalService_1.BaseExternalService {
    constructor(config) {
        super('GTmetrix', 'free', config);
        this.API_URL = 'https://gtmetrix.com/api/2.0';
    }
    isConfigured() {
        return !!this.config.apiKey;
    }
    async testConnection() {
        try {
            const response = await fetch(`${this.API_URL}/status`, {
                headers: {
                    'Authorization': `Bearer ${this.config.apiKey}`,
                }
            });
            return response.ok;
        }
        catch (error) {
            return false;
        }
    }
    async analyze(url) {
        if (!this.isEnabled()) {
            return this.createErrorResult('GTmetrix service is not enabled or configured');
        }
        try {
            await this.handleRateLimit();
            const result = await this.withRetry(async () => {
                // Start test
                const testResponse = await fetch(`${this.API_URL}/reports`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${this.config.apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        url: url,
                        report: 'lighthouse'
                    }),
                    signal: AbortSignal.timeout(this.config.timeout || 30000)
                });
                if (!testResponse.ok) {
                    throw new Error(`GTmetrix API failed: ${testResponse.statusText}`);
                }
                const testData = await testResponse.json();
                const reportId = testData.data.id;
                // Poll for results
                return await this.pollForResults(reportId);
            });
            return this.parseResponse(result);
        }
        catch (error) {
            return this.createErrorResult(error instanceof Error ? error.message : 'Unknown error');
        }
    }
    getFreeTierLimit() {
        return 20; // 20 reports per month
    }
    getPremiumCost() {
        return 0; // Free service
    }
    async pollForResults(reportId, maxAttempts = 30) {
        for (let attempt = 0; attempt < maxAttempts; attempt++) {
            const response = await fetch(`${this.API_URL}/reports/${reportId}`, {
                headers: {
                    'Authorization': `Bearer ${this.config.apiKey}`
                }
            });
            if (!response.ok) {
                throw new Error(`Failed to get GTmetrix report: ${response.statusText}`);
            }
            const data = await response.json();
            if (data.data.state === 'completed') {
                return data.data;
            }
            else if (data.data.state === 'error') {
                throw new Error(`GTmetrix analysis failed: ${data.data.error}`);
            }
            // Wait before next poll
            await new Promise(resolve => setTimeout(resolve, 5000));
        }
        throw new Error('GTmetrix analysis timed out');
    }
    parseResponse(data) {
        const lighthouse = data.reports?.lighthouse || {};
        const categories = lighthouse.categories || {};
        const metrics = {
            overall: Math.round((categories.seo?.score || 0) * 100),
            technical: Math.round((categories['best-practices']?.score || 0) * 100),
            content: Math.round((categories.seo?.score || 0) * 100),
            performance: Math.round((categories.performance?.score || 0) * 100),
            accessibility: Math.round((categories.accessibility?.score || 0) * 100),
            seo: Math.round((categories.seo?.score || 0) * 100),
            details: lighthouse
        };
        const issues = this.extractIssues(lighthouse.audits || {});
        return {
            provider: this.name,
            metrics,
            issues,
            rawData: data,
            timestamp: new Date().toISOString(),
            success: true
        };
    }
    extractIssues(audits) {
        const issues = [];
        const seoAudits = [
            { id: 'document-title', category: 'Meta Tags' },
            { id: 'meta-description', category: 'Meta Tags' },
            { id: 'viewport', category: 'Mobile' },
            { id: 'image-alt', category: 'Images' },
            { id: 'link-text', category: 'Links' },
            { id: 'crawlable-anchors', category: 'Links' },
            { id: 'is-crawlable', category: 'Technical SEO' }
        ];
        seoAudits.forEach(({ id, category }) => {
            const audit = audits[id];
            if (audit && audit.score !== null && audit.score < 1) {
                issues.push({
                    category,
                    severity: audit.score === 0 ? 'critical' : 'warning',
                    title: audit.title || id,
                    description: audit.description || 'GTmetrix SEO issue detected',
                    recommendation: this.getRecommendation(id),
                    impact: audit.score === 0 ? 'high' : 'medium'
                });
            }
        });
        return issues;
    }
    getRecommendation(auditId) {
        const recommendations = {
            'document-title': 'Add a unique, descriptive title tag',
            'meta-description': 'Add a compelling meta description',
            'viewport': 'Add viewport meta tag for mobile optimization',
            'image-alt': 'Add alt text to all images',
            'link-text': 'Use descriptive link text',
            'crawlable-anchors': 'Ensure links are crawlable by search engines',
            'is-crawlable': 'Make sure page is crawlable by search engines'
        };
        return recommendations[auditId] || 'Fix this SEO issue for better rankings';
    }
    getRateLimit() {
        return 1; // 1 request per minute for free tier
    }
}
exports.GTmetrixService = GTmetrixService;
//# sourceMappingURL=GTmetrixService.js.map