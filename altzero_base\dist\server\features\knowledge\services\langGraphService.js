"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.langGraphService = void 0;
const openai_1 = require("@langchain/openai");
const prompts_1 = require("@langchain/core/prompts");
const runnables_1 = require("@langchain/core/runnables");
const output_parsers_1 = require("@langchain/core/output_parsers");
const pineconeService_1 = require("./pineconeService");
class LangGraphService {
    constructor() {
        this.llm = null;
        this.isEnabled = false;
        if (!process.env.OPENAI_API_KEY) {
            console.warn("OPENAI_API_KEY not found. LangGraph features will be disabled.");
            this.isEnabled = false;
        }
        else {
            try {
                this.llm = new openai_1.ChatOpenAI({
                    openAIApiKey: process.env.OPENAI_API_KEY,
                    modelName: process.env.OPENAI_MODEL || "gpt-4",
                    temperature: 0.7,
                    maxTokens: 2048,
                });
                this.isEnabled = true;
            }
            catch (error) {
                console.error("Failed to initialize ChatOpenAI:", error);
                this.isEnabled = false;
            }
        }
        this.ragPrompt = prompts_1.PromptTemplate.fromTemplate(`
You are an advanced AI assistant with access to a comprehensive knowledge base. You must ONLY use the provided context to answer the user's question. Do NOT use any external knowledge or make assumptions beyond what is explicitly stated in the context.

System Instructions: {systemMessage}

Context from Knowledge Base:
{context}

User Question: {question}

IMPORTANT INSTRUCTIONS:
- ONLY use information from the provided context above
- If the context doesn't contain enough information to answer the question, say "I don't have enough information in the knowledge base to answer this question"
- Do NOT make up information or use general knowledge
- Cite specific sources when referencing information
- Use a professional but conversational tone
- Be precise and stick to the facts provided in the context

Answer based ONLY on the context provided:`);
        this.analysisPrompt = prompts_1.PromptTemplate.fromTemplate(`
You are an expert document analyst. Analyze the provided document content and generate insights based on the analysis type requested.

Analysis Type: {analysisType}
Document Content: {content}

Provide a comprehensive analysis including:
- Key findings and insights
- Important themes and patterns
- Actionable recommendations
- Summary of main points

Analysis:`);
    }
    async performRAG(request) {
        if (!this.isEnabled || !this.llm) {
            throw new Error("LangGraph service not available - OpenAI API key not configured");
        }
        const startTime = Date.now();
        try {
            console.log(`🔍 Starting LangGraph RAG for query: "${request.query}"`);
            console.log(`📋 User filter:`, request.userFilter);
            console.log(`📄 Selected documents:`, request.selectedDocuments);
            // Step 1: Retrieve relevant documents from Pinecone with better debugging
            const retrievalResults = await this.retrieveDocuments(request.query, request.selectedDocuments, { topK: 15, minScore: 0.2 }, request.userFilter);
            // IMPORTANT: Ensure strict document filtering
            let finalResults = retrievalResults;
            if (request.selectedDocuments && request.selectedDocuments.length > 0) {
                console.log(`🔒 ENFORCING STRICT DOCUMENT FILTERING...`);
                const beforeCount = finalResults.length;
                finalResults = retrievalResults.filter(result => request.selectedDocuments.includes(result.metadata?.llamaCloudId));
                const afterCount = finalResults.length;
                if (beforeCount !== afterCount) {
                    console.log(`⚠️  FILTERING APPLIED: Removed ${beforeCount - afterCount} results from unselected documents`);
                }
                console.log(`🔒 STRICT FILTERING COMPLETE: ${finalResults.length} results from selected documents only`);
                // Double-check: verify all results are from selected documents
                const resultDocIds = finalResults.map(r => r.metadata?.llamaCloudId);
                const uniqueDocIds = [...new Set(resultDocIds)];
                const allFromSelected = uniqueDocIds.every(docId => request.selectedDocuments.includes(docId));
                if (!allFromSelected) {
                    console.error(`❌ CRITICAL ERROR: Some results are still from unselected documents!`);
                    console.error(`   Selected: ${request.selectedDocuments.join(', ')}`);
                    console.error(`   Found: ${uniqueDocIds.join(', ')}`);
                    // Filter again as safety measure
                    finalResults = finalResults.filter(result => request.selectedDocuments.includes(result.metadata?.llamaCloudId));
                }
                else {
                    console.log(`✅ VALIDATION PASSED: All ${finalResults.length} results are from selected documents`);
                }
            }
            console.log(`📊 Retrieved ${finalResults.length} documents from Pinecone`);
            // Debug: Log retrieved documents
            if (finalResults.length > 0) {
                console.log(`📝 Sample document metadata:`, finalResults[0].metadata);
                console.log(`🎯 Document scores:`, finalResults.map((r) => ({ id: r.id, score: r.score })));
                // Show document filtering results
                if (request.selectedDocuments && request.selectedDocuments.length > 0) {
                    const resultDocIds = finalResults.map(r => r.metadata?.llamaCloudId);
                    const uniqueDocIds = [...new Set(resultDocIds)];
                    console.log(`🎯 FINAL DOCUMENT FILTERING RESULTS:`);
                    console.log(`   User selected: ${request.selectedDocuments.length} document(s)`);
                    console.log(`   Selected IDs: ${request.selectedDocuments.join(', ')}`);
                    console.log(`   Found chunks from: ${uniqueDocIds.length} document(s)`);
                    console.log(`   Document IDs found: ${uniqueDocIds.join(', ')}`);
                    console.log(`   Total chunks retrieved: ${finalResults.length}`);
                    // Verify filtering worked correctly
                    const correctlyFiltered = uniqueDocIds.every(docId => request.selectedDocuments.includes(docId));
                    if (correctlyFiltered) {
                        console.log(`✅ FILTERING SUCCESS: All results are from selected documents`);
                    }
                    else {
                        console.log(`❌ FILTERING ERROR: Some results are from unselected documents`);
                        console.log(`   Expected: ${request.selectedDocuments.join(', ')}`);
                        console.log(`   Found: ${uniqueDocIds.join(', ')}`);
                    }
                }
                else {
                    console.log(`🌐 NO DOCUMENT FILTER: Searched across all user documents`);
                }
            }
            // Step 2: Check if we have valid context - ONLY respond if we have relevant documents
            if (finalResults.length === 0) {
                console.log("❌ No documents found in Pinecone - not generating response");
                return {
                    answer: "I don't have any relevant information in my knowledge base to answer your question. Please make sure documents are uploaded and properly indexed.",
                    sources: [],
                    metadata: {
                        retrievedDocuments: 0,
                        processingTime: Date.now() - startTime,
                        model: this.llm.modelName,
                        hasValidContext: false,
                    },
                };
            }
            // Filter results with a reasonable minimum score
            const filteredResults = finalResults.filter((result) => result.score >= 0.2);
            if (filteredResults.length === 0) {
                console.log("❌ No documents with sufficient relevance score - not generating response");
                console.log(`📊 Retrieved ${finalResults.length} documents with scores:`, finalResults.map(r => ({ id: r.id, score: r.score })));
                return {
                    answer: "I found some documents in the knowledge base, but they don't seem directly relevant to your question. Please try rephrasing your question or check if the relevant documents are uploaded.",
                    sources: finalResults.map((result) => ({
                        id: result.id,
                        content: result.text.substring(0, 500) + "...",
                        score: result.score,
                        metadata: result.metadata,
                    })),
                    metadata: {
                        retrievedDocuments: finalResults.length,
                        processingTime: Date.now() - startTime,
                        model: this.llm.modelName,
                        hasValidContext: false,
                    },
                };
            }
            console.log(`✅ Using ${filteredResults.length} relevant chunks for response generation`);
            // Step 3: Format context for the LLM
            const context = this.formatContext(filteredResults);
            // Step 4: Generate response using LLM - only with valid context
            const ragChain = runnables_1.RunnableSequence.from([
                this.ragPrompt,
                this.llm,
                new output_parsers_1.StringOutputParser(),
            ]);
            const answer = await ragChain.invoke({
                systemMessage: request.systemMessage ||
                    "You are a helpful AI assistant that only uses provided context.",
                context: context,
                question: request.query,
            });
            const processingTime = Date.now() - startTime;
            // Calculate unique documents for better logging
            const uniqueDocuments = [...new Set(filteredResults.map(r => r.metadata?.llamaCloudId))];
            const documentNames = [...new Set(filteredResults.map(r => r.metadata?.fileName || r.metadata?.filename))];
            console.log(`✅ LangGraph RAG completed in ${processingTime}ms with ${filteredResults.length} chunks from ${uniqueDocuments.length} document(s): ${documentNames.join(', ')}`);
            return {
                answer,
                sources: filteredResults.map((result) => ({
                    id: result.id,
                    content: result.text.substring(0, 500) + "...",
                    score: result.score,
                    metadata: result.metadata,
                    documentName: result.metadata?.fileName || result.metadata?.filename || "Unknown Document",
                    documentId: result.metadata?.llamaCloudId || "unknown",
                    chunkIndex: result.metadata?.chunkIndex || 0,
                })),
                metadata: {
                    retrievedDocuments: filteredResults.length,
                    processingTime,
                    model: this.llm.modelName,
                    hasValidContext: true,
                    uniqueDocuments: [...new Set(filteredResults.map(r => r.metadata?.llamaCloudId))].length,
                    documentsUsed: [...new Set(filteredResults.map(r => r.metadata?.fileName || r.metadata?.filename))],
                },
            };
        }
        catch (error) {
            console.error("❌ Error in LangGraph RAG:", error);
            throw error;
        }
    }
    async searchDocuments(query, options = {}) {
        try {
            console.log(`🔍 LangGraph search for: "${query}"`);
            console.log(`🔧 Search options:`, options);
            const results = await pineconeService_1.pineconeService.searchSimilar(query, {
                topK: options.topK || 10,
                minScore: options.minScore || 0.3, // Lower default minimum score
                filter: options.filter,
            });
            console.log(`📊 Found ${results.length} results in Pinecone`);
            // Enhanced debugging
            if (results.length === 0) {
                console.log("🔍 Debug: Trying search without filters to check if any documents exist...");
                const debugResults = await pineconeService_1.pineconeService.searchSimilar(query, {
                    topK: 5,
                    minScore: 0.1,
                    // No filter to see if documents exist at all
                });
                console.log(`🔍 Debug search found ${debugResults.length} documents total`);
                if (debugResults.length > 0) {
                    console.log(`🔍 Debug: Sample document metadata:`, debugResults[0].metadata);
                }
            }
            // Enhance results with LangGraph processing
            return results.map((result) => ({
                ...result,
                // Add any LangGraph-specific enhancements here
                metadata: {
                    ...result.metadata,
                    processedBy: "LangGraph",
                    enhancedAt: new Date().toISOString(),
                },
            }));
        }
        catch (error) {
            console.error("❌ Error in LangGraph search:", error);
            return [];
        }
    }
    async retrieveDocuments(query, selectedDocuments, options = { topK: 15, minScore: 0.2 }, userFilter) {
        try {
            console.log(`🔍 retrieveDocuments called with:`);
            console.log(`   Query: "${query}"`);
            console.log(`   Options:`, options);
            console.log(`   UserFilter:`, userFilter);
            console.log(`   SelectedDocuments:`, selectedDocuments);
            console.log(`   SelectedDocuments type:`, typeof selectedDocuments);
            console.log(`   SelectedDocuments length:`, selectedDocuments?.length);
            if (selectedDocuments && selectedDocuments.length > 0) {
                console.log(`   SelectedDocuments values:`, selectedDocuments.map(id => `"${id}"`));
                console.log(`🎯 DOCUMENT FILTERING: Will search ONLY within selected documents`);
            }
            else {
                console.log(`🌐 NO DOCUMENT FILTER: Will search across all user documents`);
            }
            // Build filter more carefully
            let filter = {};
            // Add user filter if provided
            if (userFilter?.userId) {
                filter.userId = userFilter.userId;
                console.log(`🔧 Adding user filter: userId = ${userFilter.userId}`);
            }
            // Add document filter if provided - try different approaches
            if (selectedDocuments && selectedDocuments.length > 0) {
                // Try simple equality for single document, or array for multiple
                if (selectedDocuments.length === 1) {
                    filter.llamaCloudId = selectedDocuments[0];
                    console.log(`🎯 SINGLE DOCUMENT SEARCH: llamaCloudId = ${selectedDocuments[0]}`);
                }
                else {
                    // For multiple documents, try both $in and simple array
                    filter.llamaCloudId = { $in: selectedDocuments };
                    console.log(`🎯 MULTIPLE DOCUMENT SEARCH: llamaCloudIds = ${selectedDocuments.join(', ')}`);
                }
                console.log(`📋 Note: These are the llamaCloudId values from getUserDocuments`);
            }
            console.log(`🔧 Final search filter:`, JSON.stringify(filter, null, 2));
            // DEBUG: Check what's actually in Pinecone for this user
            if (userFilter?.userId) {
                console.log(`🔍 DEBUG: Checking what documents exist in Pinecone for user ${userFilter.userId}...`);
                try {
                    const debugUserResults = await pineconeService_1.pineconeService.searchSimilar("", {
                        topK: 50,
                        minScore: 0.0,
                        filter: { userId: userFilter.userId },
                    });
                    console.log(`🔍 DEBUG: Found ${debugUserResults.length} total chunks for user`);
                    if (debugUserResults.length > 0) {
                        const userDocIds = debugUserResults.map(r => r.metadata?.llamaCloudId);
                        const uniqueUserDocIds = [...new Set(userDocIds)];
                        console.log(`🔍 DEBUG: User has ${uniqueUserDocIds.length} unique documents in Pinecone:`);
                        console.log(`🔍 DEBUG: Available llamaCloudIds:`, uniqueUserDocIds);
                        if (selectedDocuments && selectedDocuments.length > 0) {
                            console.log(`🔍 DEBUG: Looking for selectedDocuments:`, selectedDocuments);
                            const foundSelected = selectedDocuments.filter(id => uniqueUserDocIds.includes(id));
                            const missingSelected = selectedDocuments.filter(id => !uniqueUserDocIds.includes(id));
                            console.log(`🔍 DEBUG: Found selected docs in Pinecone:`, foundSelected);
                            console.log(`🔍 DEBUG: Missing selected docs from Pinecone:`, missingSelected);
                        }
                    }
                }
                catch (debugError) {
                    console.log(`🔍 DEBUG: Error checking user documents:`, debugError);
                }
            }
            // IMPORTANT: Try without any filters first to debug (this was working before!)
            console.log(`🔍 DEBUG: First trying search WITHOUT any filters...`);
            const debugNoFilterResults = await pineconeService_1.pineconeService.searchSimilar(query, {
                topK: options.topK,
                minScore: 0.1, // Use lower threshold for debugging
                // No filter
            });
            console.log(`🔍 DEBUG: No filter search returned ${debugNoFilterResults.length} results`);
            if (debugNoFilterResults.length > 0) {
                console.log(`🔍 DEBUG: Sample result metadata:`, debugNoFilterResults[0].metadata);
                console.log(`🔍 DEBUG: Sample result userId:`, debugNoFilterResults[0].metadata?.userId);
                console.log(`🔍 DEBUG: Looking for userId:`, userFilter?.userId);
                console.log(`🔍 DEBUG: Match?`, debugNoFilterResults[0].metadata?.userId === userFilter?.userId);
                // Show all scores for debugging
                console.log(`🔍 DEBUG: All result scores:`, debugNoFilterResults.map(r => ({ id: r.id, score: r.score })));
                // Show llamaCloudId values for debugging document filtering
                console.log(`🔍 DEBUG: All llamaCloudId values:`, debugNoFilterResults.map(r => ({ id: r.id, llamaCloudId: r.metadata?.llamaCloudId })));
                if (selectedDocuments && selectedDocuments.length > 0) {
                    console.log(`🔍 DEBUG: Looking for llamaCloudIds:`, selectedDocuments);
                    console.log(`🔍 DEBUG: Document matches:`, debugNoFilterResults.map(r => ({
                        id: r.id,
                        llamaCloudId: r.metadata?.llamaCloudId,
                        matches: selectedDocuments.includes(r.metadata?.llamaCloudId)
                    })));
                }
            }
            // If we have filters, try with them first
            if (Object.keys(filter).length > 0) {
                console.log(`🔍 Searching with filters (user + document constraints)...`);
                console.log(`🔧 Filter object type check:`, {
                    filterKeys: Object.keys(filter),
                    filterValues: Object.values(filter),
                    filterStringified: JSON.stringify(filter),
                    selectedDocumentsType: typeof selectedDocuments,
                    selectedDocumentsLength: selectedDocuments?.length,
                    selectedDocumentsContent: selectedDocuments
                });
                const results = await pineconeService_1.pineconeService.searchSimilar(query, {
                    topK: options.topK,
                    minScore: options.minScore,
                    filter: filter,
                });
                console.log(`📊 Filtered search returned ${results.length} results`);
                if (results.length > 0) {
                    // Verify all results are from selected documents
                    if (selectedDocuments && selectedDocuments.length > 0) {
                        const resultDocIds = results.map(r => r.metadata?.llamaCloudId);
                        const uniqueDocIds = [...new Set(resultDocIds)];
                        console.log(`✅ DOCUMENT FILTERING SUCCESS:`);
                        console.log(`   Selected documents: ${selectedDocuments.join(', ')}`);
                        console.log(`   Found chunks from documents: ${uniqueDocIds.join(', ')}`);
                        console.log(`   Total chunks found: ${results.length}`);
                        console.log(`   Chunks per document:`, uniqueDocIds.map(docId => ({
                            docId,
                            chunks: resultDocIds.filter(id => id === docId).length
                        })));
                    }
                    console.log(`✅ Found ${results.length} documents with filters`);
                    return results;
                }
                // If no results with complex filter, try simpler filters
                if (selectedDocuments && selectedDocuments.length > 0) {
                    console.log(`🔧 No results with complex filter, trying simpler document filter variations...`);
                    // Try different filter formats for document selection
                    const filterVariations = [
                        // Variation 1: Simple equality for single document
                        ...(selectedDocuments.length === 1 ? [{ llamaCloudId: selectedDocuments[0] }] : []),
                        // Variation 2: Array format without $in
                        { llamaCloudId: selectedDocuments },
                        // Variation 3: $in format (original)
                        { llamaCloudId: { $in: selectedDocuments } },
                        // Variation 4: $eq format for single document
                        ...(selectedDocuments.length === 1 ? [{ llamaCloudId: { $eq: selectedDocuments[0] } }] : []),
                    ];
                    for (let i = 0; i < filterVariations.length; i++) {
                        const testFilter = filterVariations[i];
                        console.log(`🧪 Testing filter variation ${i + 1}:`, JSON.stringify(testFilter));
                        const testResults = await pineconeService_1.pineconeService.searchSimilar(query, {
                            topK: options.topK,
                            minScore: options.minScore,
                            filter: testFilter,
                        });
                        console.log(`📊 Filter variation ${i + 1} returned ${testResults.length} results`);
                        if (testResults.length > 0) {
                            console.log(`✅ Found ${testResults.length} documents with filter variation ${i + 1}`);
                            return testResults;
                        }
                    }
                }
                // If still no results, try with just user filter
                if (userFilter?.userId) {
                    console.log(`🔧 Trying with just user filter...`);
                    const userOnlyResults = await pineconeService_1.pineconeService.searchSimilar(query, {
                        topK: options.topK,
                        minScore: options.minScore,
                        filter: { userId: userFilter.userId },
                    });
                    console.log(`📊 User-only filter returned ${userOnlyResults.length} results`);
                    // If we have selectedDocuments, we need to filter further even if we got user results
                    if (userOnlyResults.length > 0) {
                        if (selectedDocuments && selectedDocuments.length > 0) {
                            console.log(`🔧 Applying document filter to user results...`);
                            const documentFilteredResults = userOnlyResults.filter(result => selectedDocuments.includes(result.metadata?.llamaCloudId));
                            console.log(`📊 After document filtering: ${documentFilteredResults.length} results`);
                            if (documentFilteredResults.length > 0) {
                                console.log(`✅ Found ${documentFilteredResults.length} documents with user + document filter`);
                                return documentFilteredResults;
                            }
                        }
                        else {
                            // No document filter needed, return user results
                            console.log(`✅ Found ${userOnlyResults.length} documents with user filter only`);
                            return userOnlyResults;
                        }
                    }
                }
                // If still no results, try without any filter but with manual filtering
                console.log(`🔧 No results with any filter, trying manual filtering...`);
                const allResults = await pineconeService_1.pineconeService.searchSimilar(query, {
                    topK: options.topK * 2, // Get more results for manual filtering
                    minScore: 0.1, // Lower threshold for manual filtering
                    // No filter
                });
                console.log(`📊 All results search returned ${allResults.length} results`);
                // Manual filtering
                let filteredResults = allResults;
                // Filter by user if specified
                if (userFilter?.userId) {
                    filteredResults = filteredResults.filter(result => result.metadata?.userId === userFilter.userId);
                    console.log(`📊 After user filtering: ${filteredResults.length} results`);
                }
                // Filter by documents if specified
                if (selectedDocuments && selectedDocuments.length > 0) {
                    console.log(`🔧 Manual document filtering - before:`, filteredResults.length);
                    console.log(`🔧 Looking for llamaCloudIds:`, selectedDocuments);
                    console.log(`🔧 Available llamaCloudIds:`, filteredResults.map(r => r.metadata?.llamaCloudId));
                    filteredResults = filteredResults.filter(result => selectedDocuments.includes(result.metadata?.llamaCloudId));
                    console.log(`📊 After document filtering: ${filteredResults.length} results`);
                    if (filteredResults.length === 0) {
                        console.log(`❌ No documents matched the selected IDs`);
                        console.log(`🔧 Selected IDs:`, selectedDocuments);
                        console.log(`🔧 Available IDs:`, allResults.map(r => r.metadata?.llamaCloudId));
                    }
                }
                // Apply score threshold
                filteredResults = filteredResults.filter(result => result.score >= options.minScore);
                console.log(`📊 After score filtering (>= ${options.minScore}): ${filteredResults.length} results`);
                // Limit results
                filteredResults = filteredResults.slice(0, options.topK);
                if (filteredResults.length > 0) {
                    console.log(`✅ Found ${filteredResults.length} documents after manual filtering`);
                    return filteredResults;
                }
                return allResults.slice(0, options.topK);
            }
            else {
                // No filters, search all documents
                console.log(`🔧 Searching without filters (all user documents)`);
                return await pineconeService_1.pineconeService.searchSimilar(query, {
                    topK: options.topK,
                    minScore: options.minScore,
                });
            }
        }
        catch (error) {
            console.error("❌ Error retrieving documents:", error);
            return [];
        }
    }
    formatContext(results) {
        if (results.length === 0) {
            return "No relevant context found in the knowledge base.";
        }
        return results
            .map((result, index) => {
            const source = result.metadata?.filename || `Source ${index + 1}`;
            return `[${source}] (Relevance: ${(result.score * 100).toFixed(1)}%)\n${result.text}`;
        })
            .join("\n\n---\n\n");
    }
    async getDocumentsByIds(documentIds, userFilter) {
        const allResults = [];
        for (const docId of documentIds) {
            try {
                let filter = { llamaCloudId: docId };
                if (userFilter) {
                    filter = { ...filter, ...userFilter };
                }
                const results = await pineconeService_1.pineconeService.searchSimilar("", {
                    topK: 100,
                    filter,
                });
                allResults.push(...results);
            }
            catch (error) {
                console.error(`❌ Error fetching document ${docId}:`, error);
            }
        }
        return allResults;
    }
    async generateRecommendations(intent, availableDocuments) {
        try {
            console.log(`🎯 Generating recommendations for intent: "${intent}"`);
            // Use semantic search to find relevant documents
            const searchResults = await this.searchDocuments(intent, {
                topK: 10,
                minScore: 0.3,
            });
            // Map search results to available documents
            const recommendations = searchResults
                .map((result) => {
                const doc = availableDocuments.find((d) => result.metadata?.filename?.includes(d.name) ||
                    result.metadata?.llamaCloudId === d.id);
                if (doc) {
                    return {
                        id: doc.id,
                        name: doc.name,
                        relevanceScore: result.score,
                        reasoning: `Relevant to "${intent}" based on content similarity (score: ${(result.score * 100).toFixed(1)}%)`,
                    };
                }
                return null;
            })
                .filter(Boolean)
                .slice(0, 5);
            return recommendations;
        }
        catch (error) {
            console.error("❌ Error generating recommendations:", error);
            return [];
        }
    }
    async analyzeDocuments(documentIds, analysisType, userFilter) {
        if (!this.isEnabled || !this.llm) {
            throw new Error("LangGraph service not available - OpenAI API key not configured");
        }
        try {
            console.log(`📊 Analyzing ${documentIds.length} documents with type: ${analysisType}`);
            // Retrieve document content for analysis
            const documents = await this.getDocumentsByIds(documentIds, userFilter);
            if (documents.length === 0) {
                throw new Error("No documents found for analysis");
            }
            // Combine document content
            const combinedContent = documents
                .map((doc) => `Document: ${doc.metadata?.filename || "Unknown"}\n${doc.text}`)
                .join("\n\n---\n\n");
            // Generate analysis
            const analysisChain = runnables_1.RunnableSequence.from([
                this.analysisPrompt,
                this.llm,
                new output_parsers_1.StringOutputParser(),
            ]);
            const analysis = await analysisChain.invoke({
                analysisType,
                content: combinedContent,
            });
            return {
                analysis,
                documentCount: documents.length,
                analysisType,
            };
        }
        catch (error) {
            console.error("❌ Error in document analysis:", error);
            throw error;
        }
    }
    async healthCheck() {
        if (!this.isEnabled || !this.llm) {
            return false;
        }
        try {
            // Test LLM connectivity
            const testResponse = await this.llm.invoke("Hello");
            // Test Pinecone connectivity
            const pineconeHealthy = await pineconeService_1.pineconeService.healthCheck();
            return !!testResponse && pineconeHealthy;
        }
        catch (error) {
            console.error("❌ LangGraph health check failed:", error);
            return false;
        }
    }
    // Getter to check if LangGraph is enabled
    get enabled() {
        return this.isEnabled;
    }
}
exports.langGraphService = new LangGraphService();
//# sourceMappingURL=langGraphService.js.map