"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.knowledgeBaseService = exports.KnowledgeBaseService = void 0;
// Import the real Pinecone service
let pineconeService;
try {
    const pineconeModule = require("../../../knowledge/services/pineconeService");
    pineconeService = pineconeModule.pineconeService || pineconeModule.default;
    if (!pineconeService) {
        throw new Error("PineconeService not found in module");
    }
}
catch (error) {
    console.error("❌ Failed to load real PineconeService:", error);
    // Create fallback service
    pineconeService = {
        getUserDocuments: async (userId) => {
            console.log("❌ CRITICAL: Real PineconeService failed to load");
            return [];
        },
        getDocumentsByIds: async (documentIds, userId) => {
            console.log("❌ CRITICAL: Real PineconeService failed to load");
            return [];
        },
        enabled: false,
    };
}
class KnowledgeBaseService {
    constructor() {
        this.isEnabled = pineconeService?.enabled || false;
        if (this.isEnabled) {
            console.log("✅ KnowledgeBaseService initialized with real Pinecone integration");
        }
        else {
            console.warn("⚠️ KnowledgeBaseService initialized without Pinecone (fallback mode)");
        }
    }
    /**
     * Get all documents for a user
     */
    async getUserDocuments(userId) {
        try {
            console.log(`🔍 Fetching documents for user: ${userId}`);
            if (!this.isEnabled) {
                console.warn("❌ Pinecone not available, returning empty documents list");
                return [];
            }
            const startTime = Date.now();
            const rawDocuments = await pineconeService.getUserDocuments(userId);
            const processingTime = Date.now() - startTime;
            const documents = rawDocuments.map((doc) => ({
                id: doc.id || doc.metadata?.id || `doc_${Date.now()}`,
                title: doc.metadata?.fileName ||
                    doc.metadata?.filename ||
                    doc.title ||
                    "Untitled Document",
                content: doc.content || doc.text || "",
                type: doc.metadata?.fileType || doc.type || "document",
                size: doc.metadata?.fileSize || doc.size || 0,
                uploadedAt: doc.metadata?.uploadedAt ||
                    doc.uploadedAt ||
                    new Date().toISOString(),
                metadata: doc.metadata || {},
            }));
            console.log(`✅ Retrieved ${documents.length} documents in ${processingTime}ms`);
            return documents;
        }
        catch (error) {
            console.error("❌ Error fetching user documents:", error);
            return [];
        }
    }
    /**
     * Get specific documents by their IDs with user requirements
     */
    async getDocumentsByIds(documentIds, userId, documentRequirements) {
        try {
            console.log(`🔍 Fetching ${documentIds.length} specific documents for user: ${userId}`);
            console.log(`📝 Document requirements:`, documentRequirements);
            if (!this.isEnabled) {
                console.warn("❌ Pinecone not available, returning empty documents list");
                return [];
            }
            const startTime = Date.now();
            const rawDocuments = await pineconeService.getDocumentsByIds(documentIds, userId);
            const processingTime = Date.now() - startTime;
            const documents = rawDocuments.map((doc) => {
                const docId = doc.id || doc.metadata?.id;
                const userRequirement = documentRequirements?.[docId];
                return {
                    id: docId || `doc_${Date.now()}`,
                    title: doc.metadata?.fileName ||
                        doc.metadata?.filename ||
                        doc.title ||
                        "Untitled Document",
                    content: doc.content || doc.text || "",
                    type: doc.metadata?.fileType || doc.type || "document",
                    size: doc.metadata?.fileSize || doc.size || 0,
                    uploadedAt: doc.metadata?.uploadedAt ||
                        doc.uploadedAt ||
                        new Date().toISOString(),
                    metadata: {
                        ...doc.metadata,
                        chunkCount: doc.metadata?.chunkCount || 1,
                    },
                    userRequirement: userRequirement || undefined,
                };
            });
            console.log(`✅ Retrieved ${documents.length} specific documents in ${processingTime}ms`);
            // Log which documents have specific requirements
            const docsWithRequirements = documents.filter((doc) => doc.userRequirement);
            console.log(`📝 ${docsWithRequirements.length} documents have specific user requirements`);
            return documents;
        }
        catch (error) {
            console.error("❌ Error fetching documents by IDs:", error);
            return [];
        }
    }
    /**
     * Search documents using semantic search
     */
    async searchDocuments(userId, options = {}) {
        try {
            console.log(`🔍 Searching documents for user: ${userId}`, options);
            if (!this.isEnabled) {
                console.warn("❌ Pinecone not available for search");
                return { documents: [], totalFound: 0, processingTime: 0 };
            }
            const startTime = Date.now();
            // Use Pinecone's search functionality
            const searchResults = await pineconeService.searchSimilar(options.query || "", {
                topK: options.limit || 10,
                minScore: options.minScore || 0.0,
                filter: { userId: userId, ...options.filter },
            });
            const documents = searchResults.map((result) => ({
                id: result.id,
                title: result.metadata?.fileName ||
                    result.metadata?.filename ||
                    "Untitled Document",
                content: result.text || "",
                type: result.metadata?.fileType || "document",
                size: result.metadata?.fileSize || 0,
                uploadedAt: result.metadata?.uploadedAt || new Date().toISOString(),
                metadata: {
                    ...result.metadata,
                    score: result.score,
                },
            }));
            const processingTime = Date.now() - startTime;
            console.log(`✅ Found ${documents.length} documents in ${processingTime}ms`);
            return {
                documents,
                totalFound: documents.length,
                processingTime,
            };
        }
        catch (error) {
            console.error("❌ Error searching documents:", error);
            return { documents: [], totalFound: 0, processingTime: 0 };
        }
    }
    /**
     * Format documents for AI processing with user requirements
     */
    formatDocumentsForAI(documents) {
        if (!documents.length) {
            return "";
        }
        return documents
            .map((doc, index) => {
            const requirementSection = doc.userRequirement
                ? `
USER SPECIFIC REQUIREMENT FOR THIS DOCUMENT:
"${doc.userRequirement}"

FOCUSED ANALYSIS INSTRUCTIONS:
- Prioritize content that directly addresses the user's specific requirement above
- Extract information that specifically relates to: ${doc.userRequirement}
- Focus on sections, data points, and insights that fulfill this requirement
- Ensure the analysis is targeted and relevant to the user's stated need
`
                : `
GENERAL ANALYSIS INSTRUCTIONS:
- Analyze the entire document for relevant information
- Extract key methodologies, best practices, and technical specifications
- Identify important data points and insights for proposal development
`;
            return `
=== REFERENCE DOCUMENT ${index + 1}: ${doc.title} ===
Document Type: ${doc.type}
File Size: ${Math.round(doc.size / 1024)}KB
Uploaded: ${new Date(doc.uploadedAt).toLocaleDateString()}
${requirementSection}
Content:
${doc.content}

Key Information Summary:
- Document provides reference data for proposal development
- Use this information to enhance proposal accuracy and relevance
- Extract specific details, methodologies, case studies, or best practices
- Incorporate relevant technical specifications or requirements
${doc.userRequirement
                ? `- PRIORITY: Focus on fulfilling the user requirement: "${doc.userRequirement}"`
                : ""}

===================================`;
        })
            .join("\n\n");
    }
    /**
     * Get service health status
     */
    async healthCheck() {
        try {
            const pineconeStatus = this.isEnabled && pineconeService?.enabled;
            return {
                isEnabled: this.isEnabled,
                pineconeStatus: pineconeStatus || false,
                lastChecked: new Date().toISOString(),
            };
        }
        catch (error) {
            return {
                isEnabled: false,
                pineconeStatus: false,
                lastChecked: new Date().toISOString(),
            };
        }
    }
    /**
     * Search for specific content based on requirements (OPTIMIZED FOR PROMPTS)
     * Instead of fetching entire documents, searches for relevant chunks
     */
    async searchByRequirements(documentRequirements, userId, selectedDocumentIds) {
        try {
            console.log(`🎯 Searching for targeted content based on requirements for user: ${userId}`);
            console.log(`📝 Requirements:`, documentRequirements);
            if (!this.isEnabled) {
                console.warn("❌ Pinecone not available for targeted search");
                return { relevantContent: "", searchResults: [] };
            }
            const searchResults = [];
            let allRelevantContent = [];
            // For each requirement, search for relevant content
            for (const [docId, requirement] of Object.entries(documentRequirements)) {
                if (!requirement || requirement.trim().length === 0)
                    continue;
                try {
                    console.log(`🔍 Searching for: "${requirement}" in document ${docId}`);
                    // Build search filter
                    const searchFilter = { userId: userId };
                    console.log(`🔍 Base search filter:`, searchFilter);
                    // If specific document IDs provided, filter by them
                    if (selectedDocumentIds && selectedDocumentIds.length > 0) {
                        // Add document ID filters (try multiple possible field names)
                        searchFilter.$or = [
                            { llamaCloudId: { $in: selectedDocumentIds } },
                            { id: { $in: selectedDocumentIds } },
                            { filename: { $in: selectedDocumentIds } },
                            { fileName: { $in: selectedDocumentIds } },
                        ];
                        console.log(`🔍 Document ID filter added:`, searchFilter);
                    }
                    // Extract key terms from requirement for better search
                    const searchTerms = this.extractSearchTerms(requirement);
                    console.log(`🔍 Extracted search terms: ${searchTerms.join(", ")}`);
                    // Try multiple search strategies
                    let searchResponse = [];
                    // Strategy 1: Use original requirement
                    try {
                        searchResponse = await pineconeService.searchSimilar(requirement, {
                            topK: 5,
                            minScore: 0.6, // Lowered threshold for better recall
                            filter: searchFilter,
                        });
                        console.log(`📊 Strategy 1 (original): Found ${searchResponse.length} results`);
                    }
                    catch (error) {
                        console.error(`❌ Strategy 1 failed:`, error);
                    }
                    // Strategy 2: If no results, try with extracted key terms
                    if (searchResponse.length === 0 && searchTerms.length > 0) {
                        try {
                            const keyTermsQuery = searchTerms.join(" ");
                            searchResponse = await pineconeService.searchSimilar(keyTermsQuery, {
                                topK: 8,
                                minScore: 0.5, // Even lower threshold
                                filter: searchFilter,
                            });
                            console.log(`📊 Strategy 2 (key terms): Found ${searchResponse.length} results`);
                        }
                        catch (error) {
                            console.error(`❌ Strategy 2 failed:`, error);
                        }
                    }
                    // Strategy 3: If still no results, try broader search with individual terms
                    if (searchResponse.length === 0 && searchTerms.length > 0) {
                        try {
                            for (const term of searchTerms.slice(0, 3)) {
                                // Try top 3 terms
                                const termResponse = await pineconeService.searchSimilar(term, {
                                    topK: 3,
                                    minScore: 0.4, // Very low threshold for broad search
                                    filter: searchFilter,
                                });
                                searchResponse.push(...termResponse);
                                if (searchResponse.length >= 5)
                                    break; // Stop when we have enough
                            }
                            console.log(`📊 Strategy 3 (individual terms): Found ${searchResponse.length} results`);
                        }
                        catch (error) {
                            console.error(`❌ Strategy 3 failed:`, error);
                        }
                    }
                    // Strategy 4: If still no results, try without document ID filter (broader search)
                    if (searchResponse.length === 0 &&
                        selectedDocumentIds &&
                        selectedDocumentIds.length > 0) {
                        try {
                            console.log(`🔄 Trying broader search without document ID filter...`);
                            const broadFilter = { userId: userId }; // Only filter by user
                            const broadResponse = await pineconeService.searchSimilar(searchTerms.length > 0 ? searchTerms.join(" ") : requirement, {
                                topK: 10,
                                minScore: 0.3, // Very low threshold for maximum recall
                                filter: broadFilter,
                            });
                            searchResponse = broadResponse;
                            console.log(`📊 Strategy 4 (broad search): Found ${searchResponse.length} results`);
                        }
                        catch (error) {
                            console.error(`❌ Strategy 4 failed:`, error);
                        }
                    }
                    if (searchResponse && searchResponse.length > 0) {
                        console.log(`✅ Found ${searchResponse.length} relevant chunks for requirement: "${requirement}"`);
                        const relevantChunks = searchResponse.map((result) => ({
                            content: result.text || result.pageContent || "",
                            score: result.score || 0,
                            metadata: result.metadata || {},
                        }));
                        searchResults.push({
                            documentId: docId,
                            requirement: requirement,
                            relevantChunks: relevantChunks,
                        });
                        // Add to combined content
                        const requirementContent = `
=== REQUIREMENT: ${requirement} ===
${relevantChunks
                            .map((chunk) => `[Relevance: ${Math.round(chunk.score * 100)}%] ${chunk.content}`)
                            .join("\n\n")}
===================================`;
                        allRelevantContent.push(requirementContent);
                    }
                    else {
                        console.warn(`⚠️ No relevant content found for requirement: "${requirement}"`);
                    }
                }
                catch (searchError) {
                    console.error(`❌ Error searching for requirement "${requirement}":`, searchError);
                }
            }
            const combinedContent = allRelevantContent.join("\n\n");
            console.log(`✅ Targeted search complete: ${searchResults.length} requirements processed, ${combinedContent.length} characters of relevant content`);
            return {
                relevantContent: combinedContent,
                searchResults: searchResults,
            };
        }
        catch (error) {
            console.error("❌ Error in searchByRequirements:", error);
            return { relevantContent: "", searchResults: [] };
        }
    }
    /**
     * Extract key search terms from a requirement query
     */
    extractSearchTerms(requirement) {
        // Remove common question words and extract meaningful terms
        const stopWords = new Set([
            "list",
            "the",
            "three",
            "core",
            "what",
            "are",
            "how",
            "when",
            "where",
            "why",
            "who",
            "which",
            "can",
            "you",
            "please",
            "tell",
            "me",
            "about",
            "of",
            "for",
            "in",
            "on",
            "at",
            "to",
            "from",
            "with",
            "by",
            "a",
            "an",
            "and",
            "or",
            "but",
        ]);
        // Extract words, filter stop words, and prioritize important terms
        const words = requirement
            .toLowerCase()
            .replace(/[^\w\s]/g, " ") // Remove punctuation
            .split(/\s+/)
            .filter((word) => word.length > 2 && !stopWords.has(word));
        // Prioritize certain keywords
        const priorityTerms = [
            "objectives",
            "goals",
            "features",
            "benefits",
            "initiative",
            "firefly",
        ];
        const priority = words.filter((word) => priorityTerms.some((term) => word.includes(term)));
        const regular = words.filter((word) => !priorityTerms.some((term) => word.includes(term)));
        return [...priority, ...regular].slice(0, 5); // Return top 5 terms
    }
    /**
     * Check if service is available
     */
    isAvailable() {
        return this.isEnabled && pineconeService?.enabled;
    }
}
exports.KnowledgeBaseService = KnowledgeBaseService;
exports.knowledgeBaseService = new KnowledgeBaseService();
//# sourceMappingURL=KnowledgeBaseService.js.map