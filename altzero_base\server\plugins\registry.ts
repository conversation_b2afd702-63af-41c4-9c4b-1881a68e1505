// Backend plugin registry - mirrors frontend registry
export interface BackendPluginConfig {
  enabled: boolean;
  name: string;
  apiPrefix: string;
  version: string;
  description?: string;
  dependencies?: string[];
  middleware?: string[];
  agents?: AgentConfig[];
}

// Agent configuration interface
export interface AgentConfig {
  name: string;
  className: string;
  description: string;
  capabilities: string[];
  dependencies: string[];
  resourceRequirements: {
    memory_mb: number;
    cpu_cores: number;
    timeout_seconds: number;
    max_concurrent_jobs: number;
  };
  tools: string[];
  supportedAnalysisTypes: string[];
  priority: number;
}

// Backend plugin registry - should match frontend PLUGIN_REGISTRY
export const BACKEND_PLUGIN_REGISTRY: Record<string, BackendPluginConfig> = {
  knowledge: {
    enabled: true,
    name: "Knowledge Base API",
    apiPrefix: "/api/knowledge",
    version: "1.0.0",
    description: "Knowledge base management and chat APIs",
    middleware: ["auth", "rateLimit"],
  },
  pseo: {
    enabled: true,
    name: "pSEO API",
    apiPrefix: "/api/pseo",
    version: "1.0.0",
    description: "Programmatic SEO tools and analytics APIs",
    middleware: ["auth"],
    agents: [
      {
        name: "PageDiscoveryAgent",
        className: "PageDiscoveryAgent",
        description:
          "Discovers website pages through sitemap parsing and crawling",
        capabilities: ["page_discovery", "sitemap_parsing", "web_crawling"],
        dependencies: [],
        resourceRequirements: {
          memory_mb: 512,
          cpu_cores: 1,
          timeout_seconds: 600,
          max_concurrent_jobs: 3,
        },
        tools: ["http", "crawler", "parser", "database", "cache"],
        supportedAnalysisTypes: ["page_discovery", "full_site_audit"],
        priority: 1,
      },
      {
        name: "KeywordResearchAgent",
        className: "KeywordResearchAgent",
        description:
          "Performs comprehensive keyword research and analysis for domain-level SEO insights",
        capabilities: [
          "keyword_research",
          "search_volume_analysis",
          "competition_analysis",
        ],
        dependencies: [],
        resourceRequirements: {
          memory_mb: 256,
          cpu_cores: 1,
          timeout_seconds: 300,
          max_concurrent_jobs: 5,
        },
        tools: ["ai", "http", "database", "cache"],
        supportedAnalysisTypes: ["keyword_research", "full_site_audit"],
        priority: 2,
      },
      {
        name: "ContentGenerationAgent",
        className: "ContentGenerationAgent",
        description:
          "Generates SEO-optimized content based on keyword opportunities and content gaps",
        capabilities: ["content_generation", "seo_optimization"],
        dependencies: ["PageDiscoveryAgent", "KeywordResearchAgent"],
        resourceRequirements: {
          memory_mb: 1024,
          cpu_cores: 2,
          timeout_seconds: 900,
          max_concurrent_jobs: 2,
        },
        tools: ["ai", "database", "cache", "http"],
        supportedAnalysisTypes: ["content_generation", "full_site_audit"],
        priority: 3,
      },
      {
        name: "BacklinkAnalysisAgent",
        className: "BacklinkAnalysisAgent",
        description:
          "Analyzes backlink profiles and identifies link building opportunities",
        capabilities: [
          "backlink_analysis",
          "domain_authority_analysis",
          "competitor_analysis",
        ],
        dependencies: [],
        resourceRequirements: {
          memory_mb: 512,
          cpu_cores: 1,
          timeout_seconds: 450,
          max_concurrent_jobs: 3,
        },
        tools: ["http", "database", "cache", "external_apis"],
        supportedAnalysisTypes: ["backlink_analysis", "full_site_audit"],
        priority: 4,
      },
      {
        name: "SEOAuditAgent",
        className: "SEOAuditAgent",
        description:
          "Performs comprehensive technical SEO audits and optimization recommendations",
        capabilities: [
          "technical_seo_audit",
          "performance_analysis",
          "accessibility_audit",
        ],
        dependencies: ["PageDiscoveryAgent"],
        resourceRequirements: {
          memory_mb: 768,
          cpu_cores: 1,
          timeout_seconds: 720,
          max_concurrent_jobs: 2,
        },
        tools: [
          "http",
          "lighthouse",
          "database",
          "cache",
          "performance_analyzer",
        ],
        supportedAnalysisTypes: ["technical_audit", "full_site_audit"],
        priority: 5,
      },
      {
        name: "AgentOrchestrator",
        className: "AgentOrchestrator",
        description: "Orchestrates multi-agent SEO analysis workflows",
        capabilities: [
          "multi_agent_coordination",
          "workflow_management",
          "dependency_resolution",
        ],
        dependencies: [
          "PageDiscoveryAgent",
          "KeywordResearchAgent",
          "ContentGenerationAgent",
        ],
        resourceRequirements: {
          memory_mb: 256,
          cpu_cores: 1,
          timeout_seconds: 1800,
          max_concurrent_jobs: 1,
        },
        tools: ["database", "cache", "logger", "metrics"],
        supportedAnalysisTypes: ["full_site_audit"],
        priority: 0,
      },
      {
        name: "EnhancedAgentOrchestrator",
        className: "EnhancedAgentOrchestrator",
        description:
          "Advanced orchestrator with external API integrations and enhanced workflows",
        capabilities: [
          "enhanced_coordination",
          "external_api_integration",
          "advanced_analytics",
        ],
        dependencies: [
          "PageDiscoveryAgent",
          "KeywordResearchAgent",
          "ContentGenerationAgent",
        ],
        resourceRequirements: {
          memory_mb: 512,
          cpu_cores: 2,
          timeout_seconds: 2400,
          max_concurrent_jobs: 1,
        },
        tools: [
          "ai",
          "database",
          "cache",
          "external_apis",
          "analytics",
          "search_console",
        ],
        supportedAnalysisTypes: ["enhanced_audit", "full_site_audit"],
        priority: 0,
      },
    ],
  },
  aichat: {
    enabled: true, // Temporarily disabled for testing
    name: "AI Chat API",
    apiPrefix: "/api/aichat",
    version: "1.0.0",
    description: "CopilotKit runtime integration APIs",
    middleware: ["auth"],
  },
  crm: {
    enabled: true,
    name: "CRM API",
    apiPrefix: "/api/crm",
    version: "1.0.0",
    description:
      "Customer Relationship Management APIs for contacts, companies, opportunities, and activities",
    middleware: ["auth", "rateLimit"],
  },
  scopingAi: {
    enabled: true,
    name: "ScopingAI API",
    apiPrefix: "/api/scopingai",
    version: "1.0.0",
    description: "AI-powered project scoping and proposal generation APIs",
    middleware: ["auth", "rateLimit"],
  },
} as const;

// Helper functions
export const getEnabledBackendPlugins = (): string[] => {
  return Object.entries(BACKEND_PLUGIN_REGISTRY)
    .filter(([_, config]) => config.enabled)
    .map(([name, _]) => name);
};

export const getBackendPluginConfig = (
  pluginName: string
): BackendPluginConfig | undefined => {
  return BACKEND_PLUGIN_REGISTRY[pluginName];
};

export const isBackendPluginEnabled = (pluginName: string): boolean => {
  return BACKEND_PLUGIN_REGISTRY[pluginName]?.enabled ?? false;
};

export const getAllBackendPlugins = (): Record<string, BackendPluginConfig> => {
  return BACKEND_PLUGIN_REGISTRY;
};

// Agent-specific helper functions
export const getAgentConfigs = (pluginName: string): AgentConfig[] => {
  return BACKEND_PLUGIN_REGISTRY[pluginName]?.agents || [];
};

export const getAgentConfig = (
  pluginName: string,
  agentName: string
): AgentConfig | undefined => {
  const agents = getAgentConfigs(pluginName);
  return agents.find((agent) => agent.name === agentName);
};

export const getAgentsByCapability = (capability: string): AgentConfig[] => {
  const allAgents: AgentConfig[] = [];

  Object.values(BACKEND_PLUGIN_REGISTRY).forEach((plugin) => {
    if (plugin.agents) {
      allAgents.push(...plugin.agents);
    }
  });

  return allAgents.filter((agent) => agent.capabilities.includes(capability));
};

export const getAgentsByAnalysisType = (
  analysisType: string
): AgentConfig[] => {
  const allAgents: AgentConfig[] = [];

  Object.values(BACKEND_PLUGIN_REGISTRY).forEach((plugin) => {
    if (plugin.agents) {
      allAgents.push(...plugin.agents);
    }
  });

  return allAgents.filter((agent) =>
    agent.supportedAnalysisTypes.includes(analysisType)
  );
};

export const validateAgentDependencies = (
  agentName: string
): { valid: boolean; missingDependencies: string[] } => {
  const allAgents: AgentConfig[] = [];

  Object.values(BACKEND_PLUGIN_REGISTRY).forEach((plugin) => {
    if (plugin.agents) {
      allAgents.push(...plugin.agents);
    }
  });

  const agent = allAgents.find((a) => a.name === agentName);
  if (!agent) {
    return {
      valid: false,
      missingDependencies: [`Agent ${agentName} not found`],
    };
  }

  const availableAgents = allAgents.map((a) => a.name);
  const missingDependencies = agent.dependencies.filter(
    (dep) => !availableAgents.includes(dep)
  );

  return {
    valid: missingDependencies.length === 0,
    missingDependencies,
  };
};

export const getAgentExecutionOrder = (requestedAgents: string[]): string[] => {
  const allAgents: AgentConfig[] = [];

  Object.values(BACKEND_PLUGIN_REGISTRY).forEach((plugin) => {
    if (plugin.agents) {
      allAgents.push(...plugin.agents);
    }
  });

  const agentMap = new Map(allAgents.map((agent) => [agent.name, agent]));
  const resolved: string[] = [];
  const visiting = new Set<string>();

  const visit = (agentName: string) => {
    if (resolved.includes(agentName)) return;
    if (visiting.has(agentName)) {
      throw new Error(`Circular dependency detected involving ${agentName}`);
    }

    visiting.add(agentName);
    const agent = agentMap.get(agentName);

    if (agent) {
      // Visit dependencies first
      for (const dependency of agent.dependencies) {
        visit(dependency);
      }
      resolved.push(agentName);
    }

    visiting.delete(agentName);
  };

  // Visit all requested agents
  for (const agentName of requestedAgents) {
    visit(agentName);
  }

  // Sort by priority (lower number = higher priority)
  return resolved.sort((a, b) => {
    const agentA = agentMap.get(a);
    const agentB = agentMap.get(b);
    return (agentA?.priority || 999) - (agentB?.priority || 999);
  });
};
