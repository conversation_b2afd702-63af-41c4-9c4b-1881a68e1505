"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const crm_1 = __importDefault(require("../routes/crm"));
// Create the backend plugin for CRM
const crmBackendPlugin = {
    router: crm_1.default,
    config: {
        name: 'CRM API',
        version: '1.0.0',
        apiPrefix: '/api/crm'
    },
    initialize: async () => {
        console.log('🏢 CRM backend plugin initialized');
    },
    cleanup: async () => {
        console.log('🏢 CRM backend plugin cleaned up');
    },
    healthCheck: async () => {
        try {
            // Basic health check - could be expanded to check database connectivity
            return true;
        }
        catch (error) {
            console.error('CRM backend health check failed:', error);
            return false;
        }
    }
};
exports.default = crmBackendPlugin;
//# sourceMappingURL=index.js.map