"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const knowledge_1 = __importDefault(require("../routes/knowledge"));
// Create the backend plugin for knowledge
const knowledgeBackendPlugin = {
    router: knowledge_1.default,
    config: {
        name: 'Knowledge Base API',
        version: '1.0.0',
        apiPrefix: '/api/knowledge'
    },
    initialize: async () => {
        console.log('🧠 Knowledge Base backend plugin initialized');
    },
    cleanup: async () => {
        console.log('🧠 Knowledge Base backend plugin cleaned up');
    },
    healthCheck: async () => {
        // Basic health check - you can enhance this
        try {
            return true;
        }
        catch (error) {
            console.error('Knowledge backend health check failed:', error);
            return false;
        }
    }
};
exports.default = knowledgeBackendPlugin;
//# sourceMappingURL=index.js.map