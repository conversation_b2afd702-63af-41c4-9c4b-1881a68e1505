"use strict";
// =====================================================
// PROVIDER CONFIGURATION SERVICE - BACKEND
// =====================================================
Object.defineProperty(exports, "__esModule", { value: true });
exports.providerConfigService = void 0;
class ProviderConfigService {
    constructor() {
        this.envConfig = {};
        this.PROVIDER_INFO = {
            lighthouse: {
                name: 'Google Lighthouse',
                tier: 'free',
                description: 'Google PageSpeed Insights API with Lighthouse analysis',
                defaultRateLimit: 60,
                defaultTimeout: 30000,
                setupUrl: 'https://console.cloud.google.com/apis/credentials',
                monthlyLimit: 25000,
                costPerRequest: 0,
                capabilities: ['generator', 'pagespeed']
            },
            gtmetrix: {
                name: 'GTmetrix',
                tier: 'free',
                description: 'GTmetrix performance and SEO analysis',
                defaultRateLimit: 1,
                defaultTimeout: 120000,
                setupUrl: 'https://gtmetrix.com/api/',
                monthlyLimit: 20,
                costPerRequest: 0,
                capabilities: ['generator', 'pagespeed']
            },
            webpagetest: {
                name: 'WebPageTest',
                tier: 'free',
                description: 'WebPageTest performance analysis',
                defaultRateLimit: 3,
                defaultTimeout: 180000,
                setupUrl: 'https://www.webpagetest.org/api',
                monthlyLimit: 200,
                costPerRequest: 0,
                capabilities: ['pagespeed']
            },
            semrush: {
                name: 'Semrush',
                tier: 'premium',
                description: 'Comprehensive SEO analysis with keyword and backlink data',
                defaultRateLimit: 600,
                defaultTimeout: 30000,
                setupUrl: 'https://www.semrush.com/api-documentation/',
                costPerRequest: 0.005,
                capabilities: ['generator', 'backlink', 'keyword']
            },
            ahrefs: {
                name: 'Ahrefs',
                tier: 'premium',
                description: 'Advanced backlink and keyword analysis',
                defaultRateLimit: 300,
                defaultTimeout: 30000,
                setupUrl: 'https://ahrefs.com/api',
                costPerRequest: 0.008,
                capabilities: ['backlink', 'keyword']
            },
            screaming_frog: {
                name: 'Screaming Frog',
                tier: 'premium',
                description: 'Technical SEO crawler and analysis',
                defaultRateLimit: 60,
                defaultTimeout: 60000,
                setupUrl: 'https://api.screamingfrog.co.uk/',
                costPerRequest: 0.003,
                capabilities: ['generator']
            }
        };
        this.loadEnvironmentConfig();
    }
    /**
     * Load configuration from environment variables (Backend only)
     */
    loadEnvironmentConfig() {
        // Backend Node.js environment only
        this.envConfig = {
            SEO_GENERATOR_PROVIDER: process.env.SEO_GENERATOR_PROVIDER,
            SEO_GENERATOR_API_KEY: process.env.SEO_GENERATOR_API_KEY,
            SEO_PAGESPEED_PROVIDER: process.env.SEO_PAGESPEED_PROVIDER,
            SEO_PAGESPEED_API_KEY: process.env.SEO_PAGESPEED_API_KEY,
            SEO_BACKLINK_PROVIDER: process.env.SEO_BACKLINK_PROVIDER,
            SEO_BACKLINK_API_KEY: process.env.SEO_BACKLINK_API_KEY,
            SEO_KEYWORD_PROVIDER: process.env.SEO_KEYWORD_PROVIDER,
            SEO_KEYWORD_API_KEY: process.env.SEO_KEYWORD_API_KEY,
            SEO_RATE_LIMIT: process.env.SEO_RATE_LIMIT,
            SEO_TIMEOUT: process.env.SEO_TIMEOUT,
            SEO_RETRIES: process.env.SEO_RETRIES
        };
        console.log('🔧 Provider Config loaded (Backend):', {
            configuredProviders: Object.entries(this.envConfig)
                .filter(([key, value]) => key.includes('_PROVIDER') && value)
                .map(([key]) => key)
        });
    }
    /**
     * Get provider configuration for a specific function
     */
    getProviderForFunction(func) {
        const envKey = `SEO_${func.toUpperCase()}_PROVIDER`;
        const keyEnvKey = `SEO_${func.toUpperCase()}_API_KEY`;
        const provider = this.envConfig[envKey];
        const apiKey = this.envConfig[keyEnvKey];
        if (!provider || !apiKey) {
            return null;
        }
        const providerInfo = this.PROVIDER_INFO[provider];
        if (!providerInfo.capabilities.includes(func)) {
            console.warn(`Provider ${provider} does not support ${func} function`);
            return null;
        }
        return {
            provider,
            config: {
                apiKey,
                enabled: !!apiKey,
                rateLimit: this.getNumberConfig('SEO_RATE_LIMIT', providerInfo.defaultRateLimit),
                timeout: this.getNumberConfig('SEO_TIMEOUT', providerInfo.defaultTimeout),
                retries: this.getNumberConfig('SEO_RETRIES', 3)
            }
        };
    }
    /**
     * Get all configured functions and their providers
     */
    getAllConfiguredFunctions() {
        return {
            generator: this.getProviderForFunction('generator'),
            pagespeed: this.getProviderForFunction('pagespeed'),
            backlink: this.getProviderForFunction('backlink'),
            keyword: this.getProviderForFunction('keyword')
        };
    }
    /**
     * Get available providers for a specific function
     */
    getAvailableProvidersForFunction(func) {
        return Object.entries(this.PROVIDER_INFO)
            .filter(([_, info]) => info.capabilities.includes(func))
            .map(([_, info]) => info);
    }
    /**
     * Get provider information
     */
    getProviderInfo(provider) {
        return this.PROVIDER_INFO[provider];
    }
    /**
     * Get all available providers
     */
    getAvailableProviders() {
        return this.PROVIDER_INFO;
    }
    /**
     * Validate current configuration
     */
    validateConfiguration() {
        const errors = [];
        const warnings = [];
        const suggestions = [];
        // Check if at least one function is configured
        const configuredFunctions = this.getAllConfiguredFunctions();
        const hasAnyFunction = Object.values(configuredFunctions).some(config => config !== null);
        if (!hasAnyFunction) {
            errors.push('No SEO functions are configured. Configure at least one function (generator, pagespeed, backlink, or keyword)');
        }
        // Validate each configured function
        Object.entries(configuredFunctions).forEach(([func, config]) => {
            if (config) {
                const providerInfo = this.getProviderInfo(config.provider);
                if (!providerInfo.capabilities.includes(func)) {
                    errors.push(`Provider ${config.provider} does not support ${func} function`);
                }
            }
        });
        // Check for incomplete configurations
        const functions = ['generator', 'pagespeed', 'backlink', 'keyword'];
        functions.forEach(func => {
            const providerKey = `SEO_${func.toUpperCase()}_PROVIDER`;
            const keyKey = `SEO_${func.toUpperCase()}_API_KEY`;
            const hasProvider = !!this.envConfig[providerKey];
            const hasKey = !!this.envConfig[keyKey];
            if (hasProvider && !hasKey) {
                warnings.push(`${providerKey} is set but ${keyKey} is missing`);
            }
            if (hasKey && !hasProvider) {
                warnings.push(`${keyKey} is set but ${providerKey} is missing`);
            }
        });
        // Validate numeric configs
        if (this.envConfig.SEO_RATE_LIMIT && isNaN(parseInt(this.envConfig.SEO_RATE_LIMIT))) {
            errors.push('SEO_RATE_LIMIT must be a valid number');
        }
        if (this.envConfig.SEO_TIMEOUT && isNaN(parseInt(this.envConfig.SEO_TIMEOUT))) {
            errors.push('SEO_TIMEOUT must be a valid number');
        }
        if (this.envConfig.SEO_RETRIES && isNaN(parseInt(this.envConfig.SEO_RETRIES))) {
            errors.push('SEO_RETRIES must be a valid number');
        }
        // Suggestions for optimal configuration
        if (!configuredFunctions.generator) {
            suggestions.push('Configure SEO_GENERATOR_PROVIDER for basic SEO report generation (lighthouse recommended for free tier)');
        }
        if (!configuredFunctions.pagespeed) {
            suggestions.push('Configure SEO_PAGESPEED_PROVIDER for performance analysis (lighthouse or gtmetrix recommended)');
        }
        if (!configuredFunctions.backlink && !configuredFunctions.keyword) {
            suggestions.push('Consider configuring premium providers (semrush/ahrefs) for backlink and keyword analysis');
        }
        return {
            isValid: errors.length === 0,
            errors,
            warnings,
            suggestions
        };
    }
    /**
     * Generate .env template
     */
    generateEnvTemplate() {
        const functionDescriptions = {
            generator: 'Main SEO report generation and technical analysis',
            pagespeed: 'Page speed and performance analysis',
            backlink: 'Backlink analysis and domain authority',
            keyword: 'Keyword research and ranking analysis'
        };
        return `
# ======================================
# Function-Based SEO Provider Configuration
# ======================================

# Configure providers based on specific SEO functions
# Use the best provider for each function based on your needs

# GENERATOR - ${functionDescriptions.generator}
# Recommended: lighthouse (free), semrush (premium), screaming_frog (premium)
SEO_GENERATOR_PROVIDER=lighthouse
SEO_GENERATOR_API_KEY=your-google-api-key

# PAGESPEED - ${functionDescriptions.pagespeed}  
# Recommended: lighthouse (free), gtmetrix (free), webpagetest (free)
SEO_PAGESPEED_PROVIDER=lighthouse
SEO_PAGESPEED_API_KEY=your-google-api-key

# BACKLINK - ${functionDescriptions.backlink}
# Recommended: semrush (premium), ahrefs (premium)
# SEO_BACKLINK_PROVIDER=semrush
# SEO_BACKLINK_API_KEY=your-semrush-api-key

# KEYWORD - ${functionDescriptions.keyword}
# Recommended: semrush (premium), ahrefs (premium)
# SEO_KEYWORD_PROVIDER=semrush  
# SEO_KEYWORD_API_KEY=your-semrush-api-key

# ======================================
# Optional Global Configuration Overrides
# ======================================

# Rate limit (requests per minute)
# SEO_RATE_LIMIT=60

# Timeout (milliseconds)
# SEO_TIMEOUT=30000

# Retry count
# SEO_RETRIES=3

# ======================================
# Provider Capabilities & Setup URLs:
# ======================================
${Object.entries(this.PROVIDER_INFO)
            .map(([key, info]) => `# ${key}: ${info.capabilities.join(', ')} - ${info.setupUrl}`)
            .join('\n')}
`.trim();
    }
    /**
     * Helper to get numeric configuration with fallback
     */
    getNumberConfig(key, defaultValue) {
        const value = this.envConfig[key];
        return value ? parseInt(value) : defaultValue;
    }
}
// Export singleton instance for backend use
exports.providerConfigService = new ProviderConfigService();
//# sourceMappingURL=ProviderConfigService.js.map