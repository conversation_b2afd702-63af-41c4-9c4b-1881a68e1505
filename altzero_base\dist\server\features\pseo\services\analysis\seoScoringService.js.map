{"version": 3, "file": "seoScoringService.js", "sourceRoot": "", "sources": ["../../../../../../server/features/pseo/services/analysis/seoScoringService.ts"], "names": [], "mappings": ";;;AAwBA,MAAM,uBAAuB;IAK3B;QACE,qDAAqD;QACrD,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC;QAC1D,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC;QACrD,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC;QAErD,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QAClD,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,kBAAkB,EAAE,CAAC,CAAC;QAChG,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,kBAAkB,EAAE,CAAC,CAAC;QAC1F,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,kBAAkB,EAAE,CAAC,CAAC;IAC1F,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,WAAmB,EAAE,GAAW;QACtD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QAElD,IAAI,CAAC;YACH,qBAAqB;YACrB,MAAM,OAAO,GAAe;gBAC1B,YAAY,EAAE,CAAC;gBACf,KAAK,EAAE,GAAG;gBACV,YAAY,EAAE,CAAC;gBACf,cAAc,EAAE,CAAC;gBACjB,cAAc,EAAE,CAAC;gBACjB,WAAW,EAAE,CAAC;aACf,CAAC;YAEF,MAAM,eAAe,GAAa,EAAE,CAAC;YACrC,MAAM,gBAAgB,GAAQ,EAAE,CAAC;YAEjC,qCAAqC;YACrC,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC1B,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;gBACjD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,CAAC;gBACjE,gBAAgB,CAAC,UAAU,GAAG,cAAc,CAAC;gBAE7C,IAAI,cAAc,EAAE,CAAC;oBACnB,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,WAAW,GAAG,GAAG,CAAC,CAAC;oBACnE,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;oBAC5D,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,aAAa,GAAG,GAAG,CAAC,CAAC;oBACxE,eAAe,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,eAAe,CAAC,CAAC;gBAC1D,CAAC;YACH,CAAC;YAED,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACvB,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;gBAC9C,mCAAmC;gBACnC,gBAAgB,CAAC,OAAO,GAAG,EAAE,MAAM,EAAE,YAAY,EAAE,CAAC;YACtD,CAAC;YAED,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;gBAC7C,kCAAkC;gBAClC,gBAAgB,CAAC,MAAM,GAAG,EAAE,MAAM,EAAE,YAAY,EAAE,CAAC;YACrD,CAAC;YAED,sCAAsC;YACtC,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;YAC7D,OAAO,CAAC,cAAc,GAAG,eAAe,CAAC,KAAK,CAAC;YAC/C,eAAe,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,eAAe,CAAC,CAAC;YAEzD,0BAA0B;YAC1B,MAAM,MAAM,GAAG;gBACb,OAAO,CAAC,YAAY,IAAI,CAAC;gBACzB,OAAO,CAAC,cAAc,IAAI,CAAC;gBAC3B,OAAO,CAAC,cAAc,IAAI,CAAC;gBAC3B,OAAO,CAAC,WAAW,IAAI,CAAC;aACzB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YAE7B,OAAO,CAAC,YAAY,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC;gBACtC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;gBAC3E,CAAC,CAAC,CAAC,CAAC;YAEN,eAAe;YACf,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YAE1D,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC9C,OAAO,CAAC,GAAG,CAAC,0CAA0C,cAAc,IAAI,CAAC,CAAC;YAE1E,OAAO;gBACL,OAAO;gBACP,YAAY,EAAE,OAAO,CAAC,YAAY;gBAClC,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,eAAe,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,wBAAwB;gBACvE,gBAAgB;gBAChB,cAAc;aACf,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,yBAAyB,CAAC,GAAW;QACjD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;YACnD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAC1B,kEAAkE,kBAAkB,CAAC,GAAG,CAAC,QAAQ,IAAI,CAAC,gBAAgB,mFAAmF,EACzM;gBACE,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;aACF,CACF,CAAC;YAEF,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,yBAAyB,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;YAC9D,CAAC;YAED,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAEnC,OAAO;gBACL,WAAW,EAAE,IAAI,CAAC,gBAAgB,EAAE,UAAU,EAAE,WAAW,EAAE,KAAK,IAAI,CAAC;gBACvE,GAAG,EAAE,IAAI,CAAC,gBAAgB,EAAE,UAAU,EAAE,GAAG,EAAE,KAAK,IAAI,CAAC;gBACvD,aAAa,EAAE,IAAI,CAAC,gBAAgB,EAAE,UAAU,EAAE,aAAa,EAAE,KAAK,IAAI,CAAC;gBAC3E,aAAa,EAAE,IAAI,CAAC,gBAAgB,EAAE,UAAU,EAAE,CAAC,gBAAgB,CAAC,EAAE,KAAK,IAAI,CAAC;gBAChF,eAAe,EAAE,IAAI,CAAC,gCAAgC,CAAC,IAAI,CAAC;aAC7D,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC9C,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAEO,gCAAgC,CAAC,IAAS;QAChD,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,EAAE,MAAM,IAAI,EAAE,CAAC;YAEnD,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,KAAU,EAAE,EAAE;gBAC3C,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC,IAAI,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;oBACxD,eAAe,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;gBAC/D,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAC;QACvE,CAAC;QAED,OAAO,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,iBAAiB;IACvD,CAAC;IAEO,kBAAkB,CAAC,WAAmB;QAC5C,MAAM,eAAe,GAAa,EAAE,CAAC;QACrC,IAAI,KAAK,GAAG,GAAG,CAAC;QAEhB,mBAAmB;QACnB,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACrC,KAAK,IAAI,EAAE,CAAC;YACZ,eAAe,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,0BAA0B,CAAC,EAAE,CAAC;YACtD,KAAK,IAAI,EAAE,CAAC;YACZ,eAAe,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAClC,KAAK,IAAI,EAAE,CAAC;YACZ,eAAe,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAC5C,CAAC;QAED,MAAM,UAAU,GAAG,WAAW,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;QAC1D,MAAM,cAAc,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;QACxF,IAAI,cAAc,GAAG,CAAC,EAAE,CAAC;YACvB,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,cAAc,GAAG,CAAC,CAAC,CAAC;YAC1C,eAAe,CAAC,IAAI,CAAC,yBAAyB,cAAc,SAAS,CAAC,CAAC;QACzE,CAAC;QAED,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC;YACzB,eAAe,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;SAC7C,CAAC;IACJ,CAAC;IAEO,cAAc,CAAC,KAAa;QAClC,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,GAAG,CAAC;QAC5B,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,GAAG,CAAC;QAC5B,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,GAAG,CAAC;QAC5B,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,GAAG,CAAC;QAC5B,OAAO,GAAG,CAAC;IACb,CAAC;CACF;AAEY,QAAA,uBAAuB,GAAG,IAAI,uBAAuB,EAAE,CAAC"}