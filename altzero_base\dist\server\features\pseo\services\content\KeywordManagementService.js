"use strict";
// =====================================================
// KEYWORD MANAGEMENT SERVICE
// Phase 5: Content & Keyword Management
// =====================================================
Object.defineProperty(exports, "__esModule", { value: true });
exports.KeywordManagementService = void 0;
const supabaseClient_1 = require("../../../base/utils/supabaseClient");
class KeywordManagementService {
    // =====================================================
    // KEYWORD CRUD OPERATIONS
    // =====================================================
    async createKeyword(keywordData) {
        const { data, error } = await supabaseClient_1.supabase
            .from('pseo_keywords')
            .insert({
            ...keywordData,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        })
            .select()
            .single();
        if (error) {
            throw new Error(`Failed to create keyword: ${error.message}`);
        }
        return data;
    }
    async updateKeyword(id, updates) {
        const { data, error } = await supabaseClient_1.supabase
            .from('pseo_keywords')
            .update({
            ...updates,
            updated_at: new Date().toISOString()
        })
            .eq('id', id)
            .select()
            .single();
        if (error) {
            throw new Error(`Failed to update keyword: ${error.message}`);
        }
        return data;
    }
    async getKeywordById(id) {
        const { data, error } = await supabaseClient_1.supabase
            .from('pseo_keywords')
            .select('*')
            .eq('id', id)
            .single();
        if (error) {
            if (error.code === 'PGRST116')
                return null; // Not found
            throw new Error(`Failed to get keyword: ${error.message}`);
        }
        return data;
    }
    async getKeywordsByWebsite(websiteId, filters) {
        let query = supabaseClient_1.supabase
            .from('pseo_keywords')
            .select('*', { count: 'exact' })
            .eq('website_id', websiteId);
        // Apply filters
        if (filters?.status) {
            query = query.in('status', filters.status);
        }
        if (filters?.priority) {
            query = query.in('priority', filters.priority);
        }
        if (filters?.intent) {
            query = query.in('intent', filters.intent);
        }
        if (filters?.difficulty_range) {
            query = query
                .gte('keyword_difficulty', filters.difficulty_range[0])
                .lte('keyword_difficulty', filters.difficulty_range[1]);
        }
        if (filters?.volume_range) {
            query = query
                .gte('search_volume', filters.volume_range[0])
                .lte('search_volume', filters.volume_range[1]);
        }
        if (filters?.cluster_id) {
            query = query.eq('cluster_id', filters.cluster_id);
        }
        if (filters?.search) {
            query = query.ilike('keyword', `%${filters.search}%`);
        }
        // Pagination
        if (filters?.limit) {
            query = query.limit(filters.limit);
        }
        if (filters?.offset) {
            query = query.range(filters.offset, (filters.offset + (filters.limit || 20)) - 1);
        }
        // Order by priority and search volume
        query = query.order('priority', { ascending: false }).order('search_volume', { ascending: false });
        const { data, error, count } = await query;
        if (error) {
            throw new Error(`Failed to get keywords: ${error.message}`);
        }
        return { items: data || [], total: count || 0 };
    }
    async deleteKeyword(id) {
        const { error } = await supabaseClient_1.supabase
            .from('pseo_keywords')
            .delete()
            .eq('id', id);
        if (error) {
            throw new Error(`Failed to delete keyword: ${error.message}`);
        }
    }
    async bulkImportKeywords(websiteId, keywords) {
        const keywordsToInsert = keywords.map(keyword => ({
            ...keyword,
            website_id: websiteId,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        }));
        const { data, error } = await supabaseClient_1.supabase
            .from('pseo_keywords')
            .insert(keywordsToInsert)
            .select();
        if (error) {
            throw new Error(`Failed to bulk import keywords: ${error.message}`);
        }
        return data || [];
    }
    // =====================================================
    // KEYWORD CLUSTERING
    // =====================================================
    async createKeywordCluster(clusterData) {
        const { data, error } = await supabaseClient_1.supabase
            .from('pseo_keyword_clusters')
            .insert({
            ...clusterData,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        })
            .select()
            .single();
        if (error) {
            throw new Error(`Failed to create keyword cluster: ${error.message}`);
        }
        return data;
    }
    async getKeywordClusters(websiteId) {
        const { data, error } = await supabaseClient_1.supabase
            .from('pseo_keyword_clusters')
            .select('*')
            .eq('website_id', websiteId)
            .order('created_at', { ascending: false });
        if (error) {
            throw new Error(`Failed to get keyword clusters: ${error.message}`);
        }
        return data || [];
    }
    async addKeywordToCluster(clusterId, keywordId) {
        // Get current cluster
        const { data: cluster, error: getError } = await supabaseClient_1.supabase
            .from('pseo_keyword_clusters')
            .select('keyword_ids')
            .eq('id', clusterId)
            .single();
        if (getError) {
            throw new Error(`Failed to get cluster: ${getError.message}`);
        }
        // Add keyword ID to array
        const keywordIds = cluster.keyword_ids || [];
        if (!keywordIds.includes(keywordId)) {
            keywordIds.push(keywordId);
            const { error: updateError } = await supabaseClient_1.supabase
                .from('pseo_keyword_clusters')
                .update({ keyword_ids: keywordIds })
                .eq('id', clusterId);
            if (updateError) {
                throw new Error(`Failed to add keyword to cluster: ${updateError.message}`);
            }
            // Update keyword with cluster_id
            await this.updateKeyword(keywordId, { cluster_id: clusterId });
        }
    }
    async autoClusterKeywords(websiteId, clusteringMethod = 'semantic') {
        // Get all unclustered keywords
        const { items: keywords } = await this.getKeywordsByWebsite(websiteId, {
            limit: 1000
        });
        const unclusteredKeywords = keywords.filter(k => !k.cluster_id);
        if (unclusteredKeywords.length === 0) {
            return [];
        }
        // Simple clustering algorithm based on keyword similarity
        const clusters = [];
        for (const keyword of unclusteredKeywords) {
            let addedToCluster = false;
            // Try to find an existing cluster this keyword belongs to
            for (const cluster of clusters) {
                if (this.keywordsBelongToSameCluster(keyword, cluster.keywords[0], clusteringMethod)) {
                    cluster.keywords.push(keyword);
                    addedToCluster = true;
                    break;
                }
            }
            // If no cluster found, create a new one
            if (!addedToCluster) {
                clusters.push({
                    name: this.generateClusterName(keyword.keyword),
                    keywords: [keyword],
                    primary_keyword: keyword.keyword
                });
            }
        }
        // Create clusters in database
        const createdClusters = [];
        for (const cluster of clusters) {
            if (cluster.keywords.length >= 2) { // Only create clusters with multiple keywords
                const clusterData = {
                    website_id: websiteId,
                    name: cluster.name,
                    description: `Auto-generated ${clusteringMethod} cluster`,
                    primary_keyword: cluster.primary_keyword,
                    secondary_keywords: cluster.keywords.slice(1).map(k => k.keyword),
                    cluster_type: clusteringMethod === 'semantic' ? 'semantic' : clusteringMethod === 'intent' ? 'intent_based' : 'topical',
                    total_search_volume: cluster.keywords.reduce((sum, k) => sum + k.search_volume, 0),
                    avg_difficulty: cluster.keywords.reduce((sum, k) => sum + k.keyword_difficulty, 0) / cluster.keywords.length,
                    content_strategy: `Target ${cluster.keywords.length} related keywords in cluster content`,
                    target_pages: Math.ceil(cluster.keywords.length / 3), // Rough estimate
                    completed_pages: 0,
                    keyword_ids: cluster.keywords.map(k => k.id),
                    content_ids: []
                };
                const createdCluster = await this.createKeywordCluster(clusterData);
                createdClusters.push(createdCluster);
                // Update keywords with cluster_id
                for (const keyword of cluster.keywords) {
                    await this.updateKeyword(keyword.id, { cluster_id: createdCluster.id });
                }
            }
        }
        return createdClusters;
    }
    keywordsBelongToSameCluster(keyword1, keyword2, method) {
        switch (method) {
            case 'semantic':
                return this.calculateSemanticSimilarity(keyword1.keyword, keyword2.keyword) > 0.7;
            case 'intent':
                return keyword1.intent === keyword2.intent;
            case 'topical':
                return this.shareTopicalSimilarity(keyword1.keyword, keyword2.keyword);
            default:
                return false;
        }
    }
    calculateSemanticSimilarity(keyword1, keyword2) {
        // Simple word overlap similarity - in production, you'd use more sophisticated NLP
        const words1 = keyword1.toLowerCase().split(' ');
        const words2 = keyword2.toLowerCase().split(' ');
        const commonWords = words1.filter(word => words2.includes(word));
        const totalWords = new Set([...words1, ...words2]).size;
        return commonWords.length / totalWords;
    }
    shareTopicalSimilarity(keyword1, keyword2) {
        // Simple root word matching - in production, you'd use topic modeling
        const root1 = keyword1.split(' ')[0].toLowerCase();
        const root2 = keyword2.split(' ')[0].toLowerCase();
        return root1 === root2;
    }
    generateClusterName(primaryKeyword) {
        // Extract the main topic from the primary keyword
        const words = primaryKeyword.split(' ');
        const mainTopic = words.length > 1 ? words.slice(0, 2).join(' ') : words[0];
        return `${mainTopic} Cluster`;
    }
    // =====================================================
    // KEYWORD PERFORMANCE TRACKING
    // =====================================================
    async recordKeywordPerformance(metrics) {
        const { error } = await supabaseClient_1.supabase
            .from('pseo_keyword_performance')
            .upsert({
            ...metrics,
            updated_at: new Date().toISOString()
        }, {
            onConflict: 'keyword_id,date'
        });
        if (error) {
            throw new Error(`Failed to record keyword performance: ${error.message}`);
        }
        // Update current position in keyword record
        await this.updateKeyword(metrics.keyword_id, {
            current_position: metrics.position
        });
    }
    async getKeywordPerformance(keywordId, startDate, endDate) {
        let query = supabaseClient_1.supabase
            .from('pseo_keyword_performance')
            .select('*')
            .eq('keyword_id', keywordId);
        if (startDate) {
            query = query.gte('date', startDate);
        }
        if (endDate) {
            query = query.lte('date', endDate);
        }
        query = query.order('date', { ascending: true });
        const { data, error } = await query;
        if (error) {
            throw new Error(`Failed to get keyword performance: ${error.message}`);
        }
        return data || [];
    }
    // =====================================================
    // KEYWORD OPPORTUNITIES & RESEARCH
    // =====================================================
    async identifyKeywordOpportunities(websiteId) {
        const { items: keywords } = await this.getKeywordsByWebsite(websiteId, { limit: 1000 });
        const opportunities = [];
        for (const keyword of keywords) {
            // Low competition opportunities
            if (keyword.keyword_difficulty < 30 && keyword.search_volume > 100) {
                opportunities.push({
                    keyword: keyword.keyword,
                    opportunity_type: 'low_competition',
                    opportunity_score: this.calculateOpportunityScore(keyword),
                    current_position: keyword.current_position,
                    estimated_traffic: Math.floor(keyword.search_volume * 0.3), // Rough estimate
                    competition_analysis: {
                        top_competitors: [], // Would be populated from competitive analysis
                        content_quality_gap: keyword.keyword_difficulty < 20 ? 0.8 : 0.5
                    },
                    recommended_actions: [
                        'Create high-quality content targeting this keyword',
                        'Optimize on-page SEO',
                        'Build relevant backlinks'
                    ],
                    estimated_effort: keyword.keyword_difficulty < 20 ? 'low' : 'medium',
                    potential_roi: this.calculatePotentialROI(keyword)
                });
            }
            // Position improvement opportunities
            if (keyword.current_position && keyword.current_position > 10 && keyword.current_position <= 20) {
                opportunities.push({
                    keyword: keyword.keyword,
                    opportunity_type: 'content_gap',
                    opportunity_score: this.calculateOpportunityScore(keyword),
                    current_position: keyword.current_position,
                    estimated_traffic: Math.floor(keyword.search_volume * 0.15),
                    competition_analysis: {
                        top_competitors: [],
                        content_quality_gap: 0.6
                    },
                    recommended_actions: [
                        'Improve existing content quality',
                        'Add more comprehensive information',
                        'Optimize content structure'
                    ],
                    estimated_effort: 'medium',
                    potential_roi: this.calculatePotentialROI(keyword)
                });
            }
        }
        return opportunities.sort((a, b) => b.opportunity_score - a.opportunity_score);
    }
    calculateOpportunityScore(keyword) {
        // Simple scoring algorithm - in production, this would be more sophisticated
        const volumeScore = Math.min(keyword.search_volume / 1000, 1) * 30;
        const difficultyScore = (100 - keyword.keyword_difficulty) / 100 * 40;
        const positionScore = keyword.current_position ?
            Math.max(0, (50 - keyword.current_position) / 50) * 30 : 20;
        return volumeScore + difficultyScore + positionScore;
    }
    calculatePotentialROI(keyword) {
        // Simple ROI calculation based on search volume and difficulty
        const potentialClicks = keyword.search_volume * 0.2; // Assume 20% CTR if ranking well
        const estimatedValue = potentialClicks * (keyword.cpc || 1); // Use CPC as value proxy
        const estimatedCost = keyword.keyword_difficulty * 10; // Rough cost estimate
        return estimatedCost > 0 ? (estimatedValue / estimatedCost) * 100 : 0;
    }
    // =====================================================
    // KEYWORD STRATEGIES
    // =====================================================
    async createKeywordStrategy(strategyData) {
        const { data, error } = await supabaseClient_1.supabase
            .from('pseo_keyword_strategies')
            .insert({
            ...strategyData,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        })
            .select()
            .single();
        if (error) {
            throw new Error(`Failed to create keyword strategy: ${error.message}`);
        }
        return data;
    }
    async getKeywordStrategies(websiteId) {
        const { data, error } = await supabaseClient_1.supabase
            .from('pseo_keyword_strategies')
            .select('*')
            .eq('website_id', websiteId)
            .order('created_at', { ascending: false });
        if (error) {
            throw new Error(`Failed to get keyword strategies: ${error.message}`);
        }
        return data || [];
    }
    // =====================================================
    // ANALYTICS & REPORTING
    // =====================================================
    async getKeywordAnalytics(websiteId) {
        const { items: keywords } = await this.getKeywordsByWebsite(websiteId, { limit: 1000 });
        const byStatus = keywords.reduce((acc, item) => {
            acc[item.status] = (acc[item.status] || 0) + 1;
            return acc;
        }, {});
        const byIntent = keywords.reduce((acc, item) => {
            acc[item.intent] = (acc[item.intent] || 0) + 1;
            return acc;
        }, {});
        const byDifficulty = keywords.reduce((acc, item) => {
            const range = item.keyword_difficulty < 30 ? 'easy' :
                item.keyword_difficulty < 60 ? 'medium' : 'hard';
            acc[range] = (acc[range] || 0) + 1;
            return acc;
        }, {});
        // Get performance data (would need to aggregate from performance table)
        const performanceSummary = {
            total_impressions: 0,
            total_clicks: 0,
            avg_position: 0,
            avg_ctr: 0
        };
        // Trending keywords (simplified - would need historical data)
        const trendingKeywords = keywords
            .filter(k => k.monthly_trend && k.monthly_trend.length >= 2)
            .map(k => {
            const recent = k.monthly_trend.slice(-2);
            const change = ((recent[1] - recent[0]) / recent[0]) * 100;
            return {
                keyword: k.keyword,
                trend: change > 0 ? 'up' : 'down',
                change: Math.abs(change)
            };
        })
            .sort((a, b) => b.change - a.change)
            .slice(0, 10);
        return {
            total_keywords: keywords.length,
            by_status: byStatus,
            by_intent: byIntent,
            by_difficulty: byDifficulty,
            performance_summary: performanceSummary,
            trending_keywords: trendingKeywords
        };
    }
}
exports.KeywordManagementService = KeywordManagementService;
//# sourceMappingURL=KeywordManagementService.js.map