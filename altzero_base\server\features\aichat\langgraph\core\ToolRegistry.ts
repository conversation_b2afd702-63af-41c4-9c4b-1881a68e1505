// =====================================================
// AI CHAT WORKFLOW TOOL REGISTRY
// =====================================================

import { WorkflowTools, WorkflowConfig } from "../types/WorkflowState";
import OpenAI from "openai";

export class ToolRegistry {
  private config: WorkflowConfig;
  private openai: OpenAI;
  private tools: WorkflowTools | null = null;

  constructor(config: WorkflowConfig) {
    this.config = config;
    this.openai = new OpenAI({
      apiKey: config.openai_api_key || process.env.OPENAI_API_KEY,
    });
  }

  // Get all available tools
  public async getTools(): Promise<WorkflowTools> {
    if (this.tools) {
      return this.tools;
    }

    const tools: WorkflowTools = {
      ai: {
        generateResponse: this.createAIResponseGenerator(),
        analyzeIntent: this.createIntentAnalyzer(),
        extractEntities: this.createEntityExtractor(),
      },
      navigation: {
        validatePage: this.createPageValidator(),
        getPageUrl: this.createPageUrlGetter(),
        triggerNavigation: this.createNavigationTrigger(),
      },
      database: {
        query: this.createDatabaseQuery(),
        search: this.createDatabaseSearch(),
      },
      knowledgeBase: {
        search: this.createKnowledgeBaseSearch(),
        getDocument: this.createDocumentGetter(),
      },
      cache: {
        get: this.createCacheGetter(),
        set: this.createCacheSetter(),
        delete: this.createCacheDeleter(),
      },
    };

    this.tools = tools;
    return tools;
  }

  // AI Tools
  private createAIResponseGenerator() {
    return async (messages: any[], options: any = {}) => {
      try {
        const completion = await this.openai.chat.completions.create({
          model: options.model || "gpt-4",
          messages: messages.map((msg) => ({
            role: msg.role,
            content: msg.content,
          })),
          temperature: options.temperature || 0.7,
          max_tokens: options.max_tokens || 2048,
        });

        return (
          completion.choices[0]?.message?.content ||
          "I apologize, but I could not generate a response."
        );
      } catch (error) {
        console.error("AI response generation failed:", error);
        throw new Error("Failed to generate AI response");
      }
    };
  }

  private createIntentAnalyzer() {
    return async (message: string) => {
      try {
        const completion = await this.openai.chat.completions.create({
          model: "gpt-4",
          messages: [
            {
              role: "system",
              content: `Analyze the user's intent from their message. Classify it as one of:
- navigation: User wants to go to a different page
- database_query: User wants to search or query data
- knowledge_search: User wants to find information from documents
- general_chat: General conversation or questions

Return only the intent category.`,
            },
            {
              role: "user",
              content: message,
            },
          ],
          temperature: 0.1,
          max_tokens: 50,
        });

        const intent =
          completion.choices[0]?.message?.content?.toLowerCase().trim() ||
          "unknown";

        if (
          [
            "navigation",
            "database_query",
            "knowledge_search",
            "general_chat",
          ].includes(intent)
        ) {
          return intent;
        }

        return "unknown";
      } catch (error) {
        console.error("Intent analysis failed:", error);
        return "unknown";
      }
    };
  }

  private createEntityExtractor() {
    return async (message: string) => {
      try {
        const completion = await this.openai.chat.completions.create({
          model: "gpt-4",
          messages: [
            {
              role: "system",
              content: `Extract entities from the user's message. Look for:
- page_name: If they mention a specific page (dashboard, settings, crm, etc.)
- search_terms: Keywords they want to search for
- time_period: Any time references (today, this month, etc.)
- entity_type: What type of data they're looking for (customers, orders, etc.)

Return as JSON object with these keys. If no entities found, return empty object.`,
            },
            {
              role: "user",
              content: message,
            },
          ],
          temperature: 0.1,
          max_tokens: 200,
        });

        const response = completion.choices[0]?.message?.content;
        try {
          return JSON.parse(response || "{}");
        } catch {
          return {};
        }
      } catch (error) {
        console.error("Entity extraction failed:", error);
        return {};
      }
    };
  }

  // Navigation Tools
  private createPageValidator() {
    return (page: string): boolean => {
      const validPages = [
        "dashboard",
        "knowledge",
        "teams",
        "settings",
        "profile",
        "pseo",
        "scopingai",
        "crm",
        "ai-chat",
      ];
      return validPages.includes(page.toLowerCase());
    };
  }

  private createPageUrlGetter() {
    return (page: string): string => {
      const pageMap: Record<string, string> = {
        dashboard: "/",
        knowledge: "/knowledge",
        "knowledge base": "/knowledge",
        teams: "/teams",
        settings: "/settings",
        profile: "/profile",
        pseo: "/pseo",
        scopingai: "/scopingai",
        crm: "/crm",
        chat: "/ai-chat",
        "ai-chat": "/ai-chat",
      };

      return pageMap[page.toLowerCase()] || `/${page.toLowerCase()}`;
    };
  }

  private createNavigationTrigger() {
    return async (page: string, userId: string): Promise<boolean> => {
      try {
        // In a real implementation, this would trigger actual navigation
        // For now, we'll just log and return success
        console.log(`🧭 Navigation triggered: ${page} for user ${userId}`);

        // Here you could:
        // 1. Send a WebSocket message to the frontend
        // 2. Update user session with navigation intent
        // 3. Log the navigation action

        return true;
      } catch (error) {
        console.error("Navigation trigger failed:", error);
        return false;
      }
    };
  }

  // Database Tools
  private createDatabaseQuery() {
    return async (sql: string, params: any[] = []) => {
      try {
        // In a real implementation, this would execute actual database queries
        console.log(`🔍 Database query: ${sql}`, params);

        // Mock response for now
        return {
          rows: [],
          rowCount: 0,
          command: "SELECT",
          fields: [],
        };
      } catch (error) {
        console.error("Database query failed:", error);
        throw new Error("Database query failed");
      }
    };
  }

  private createDatabaseSearch() {
    return async (query: string, filters: any = {}) => {
      try {
        console.log(`🔍 Database search: ${query}`, filters);

        // Mock search results
        return [
          {
            id: 1,
            title: "Sample Result",
            description: "This is a sample search result",
            relevance: 0.95,
          },
        ];
      } catch (error) {
        console.error("Database search failed:", error);
        return [];
      }
    };
  }

  // Knowledge Base Tools
  private createKnowledgeBaseSearch() {
    return async (query: string, maxResults: number = 5) => {
      try {
        console.log(
          `📚 Knowledge base search: ${query}, max results: ${maxResults}`
        );

        // Mock knowledge base results
        return [
          {
            id: "doc1",
            title: "Sample Document",
            content: "This is sample content from the knowledge base.",
            relevance_score: 0.9,
            metadata: {
              created_at: new Date().toISOString(),
              author: "System",
            },
          },
        ];
      } catch (error) {
        console.error("Knowledge base search failed:", error);
        return [];
      }
    };
  }

  private createDocumentGetter() {
    return async (id: string) => {
      try {
        console.log(`📄 Getting document: ${id}`);

        // Mock document retrieval
        return {
          id,
          title: "Sample Document",
          content: "This is the full content of the document.",
          metadata: {
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            author: "System",
          },
        };
      } catch (error) {
        console.error("Document retrieval failed:", error);
        return null;
      }
    };
  }

  // Cache Tools
  private createCacheGetter() {
    return async <T>(key: string): Promise<T | null> => {
      try {
        // In a real implementation, this would use Redis or similar
        console.log(`🗄️ Cache get: ${key}`);
        return null;
      } catch (error) {
        console.error("Cache get failed:", error);
        return null;
      }
    };
  }

  private createCacheSetter() {
    return async (key: string, value: any, ttl: number = 3600) => {
      try {
        console.log(`🗄️ Cache set: ${key}, TTL: ${ttl}s`);
        // In a real implementation, this would store in cache
      } catch (error) {
        console.error("Cache set failed:", error);
      }
    };
  }

  private createCacheDeleter() {
    return async (key: string) => {
      try {
        console.log(`🗄️ Cache delete: ${key}`);
        // In a real implementation, this would delete from cache
      } catch (error) {
        console.error("Cache delete failed:", error);
      }
    };
  }

  // Health check for tools
  public async healthCheck(): Promise<{ [key: string]: boolean }> {
    const health = {
      openai: false,
      navigation: true,
      database: true,
      knowledgeBase: true,
      cache: true,
    };

    // Check OpenAI
    try {
      await this.openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [{ role: "user", content: "test" }],
        max_tokens: 1,
      });
      health.openai = true;
    } catch (error) {
      console.warn("OpenAI health check failed:", error);
    }

    return health;
  }
}
