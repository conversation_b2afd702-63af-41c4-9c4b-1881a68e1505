import "@copilotkit/react-ui/styles.css";
import React from "react";
import { CopilotKit } from "@copilotkit/react-core";
import { CopilotChat } from "@copilotkit/react-ui";
import { useCopilotReadable } from "@copilotkit/react-core";

/**
 * CopilotChat - Clean CopilotKit chat interface for AltZero
 * AI assistant with backend actions for database queries, navigation, and knowledge base search
 */
const AltzeroChat: React.FC = () => {
  // Make application state readable to the copilot
  useCopilotReadable({
    description: "The current application context and user information",
    value: {
      currentPage: window.location.pathname,
      timestamp: new Date().toISOString(),
      platform: "AltZero",
      availableFeatures: [
        "Database Queries (@customers, @orders, @users)",
        "Navigation (dashboard, knowledge, teams, settings, pseo, scopingai, crm)",
        "Knowledge Base Search",
      ],
    },
  });

  return (
    <CopilotKit runtimeUrl="http://localhost:3001/api/aichat">
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
        {/* Back Button */}
        <button
          onClick={() => window.history.back()}
          className="absolute top-6 left-6 flex items-center gap-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M15 19l-7-7 7-7"
            />
          </svg>
          <span className="text-sm font-medium">Back to Dashboard</span>
        </button>

        {/* Chat Panel */}
        <div className="w-full max-w-4xl h-[80vh] bg-white dark:bg-gray-900 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 overflow-hidden">
          {/* Panel Header */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-4 text-white">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                    />
                  </svg>
                </div>
                <div>
                  <h1 className="text-lg font-bold">AltZero AI Chat</h1>
                  <p className="text-sm text-white/80">
                    Your intelligent assistant
                  </p>
                </div>
              </div>
              <div className="px-3 py-1 bg-green-500/20 border border-green-400/30 rounded-full">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-xs font-medium">Online</span>
                </div>
              </div>
            </div>
          </div>

          {/* Chat Container */}
          <div className="h-[calc(100%-80px)]">
            <CopilotChat
              instructions="You are AltZero AI, an intelligent assistant for the AltZero platform. You have access to powerful backend actions:

1. **Database Queries**: Use the 'queryDatabase' action to query the AltZero database with natural language. Examples: 'Find all customers from this month', 'Show pending orders', 'Count active users'.

2. **Navigation**: Use the 'navigateToPage' action to help users navigate to different pages like dashboard, knowledge base, teams, settings, pseo, scopingai, or crm.

3. **Knowledge Base Search**: Use the 'searchKnowledgeBase' action to search through documents and find relevant information.

Always use these backend actions when users ask for database information, navigation, or document searches. Be helpful, concise, and leverage these powerful capabilities!"
              labels={{
                title: "",
                initial:
                  "Hi! 👋 I'm your AltZero AI assistant with powerful backend actions. I can help you query databases, navigate the platform, and search knowledge base. What would you like to do?",
              }}
              className="h-full"
            />
          </div>
        </div>
      </div>
    </CopilotKit>
  );
};

export default AltzeroChat;
