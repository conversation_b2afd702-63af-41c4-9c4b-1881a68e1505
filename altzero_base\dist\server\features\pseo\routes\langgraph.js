"use strict";
// =====================================================
// LANGGRAPH API ROUTES FOR PSEO WORKFLOWS
// =====================================================
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const express = __importStar(require("express"));
const PSEOWorkflow_1 = require("../langgraph/workflows/PSEOWorkflow");
const StateManager_1 = require("../langgraph/core/StateManager");
const RapidAPITool_1 = require("../langgraph/tools/RapidAPITool");
const router = express.Router();
// Use singleton StateManager to ensure workflow and SSE endpoint share the same state
const stateManager = StateManager_1.StateManager.getInstance();
console.log('🚀 LangGraph router initialized - routes will be available at /api/pseo/langgraph/*');
// Get available data sources based on configuration
router.get('/data-sources', async (req, res) => {
    try {
        const dataSources = [];
        // Check RapidAPI configuration
        if (process.env.RAPIDAPI_KEY) {
            // Determine which provider is enabled
            let activeProvider = 'ubersuggest'; // default
            if (process.env.RAPIDAPI_SEMRUSH_ENABLED === 'true') {
                activeProvider = 'semrush';
            }
            else if (process.env.RAPIDAPI_AHREFS_ENABLED === 'true') {
                activeProvider = 'ahrefs';
            }
            else if (process.env.RAPIDAPI_MOZ_ENABLED === 'true') {
                activeProvider = 'moz';
            }
            dataSources.push({
                id: 'rapidapi',
                name: `RapidAPI (${activeProvider.charAt(0).toUpperCase() + activeProvider.slice(1)}) ⭐`,
                description: `Professional SEO data via ${activeProvider}`,
                enabled: true,
                recommended: true,
                provider: activeProvider
            });
        }
        // Check direct API configurations
        if (process.env.SEMRUSH_API_KEY) {
            dataSources.push({
                id: 'semrush',
                name: 'Semrush (Direct API)',
                description: 'Direct Semrush API access',
                enabled: true,
                recommended: false
            });
        }
        if (process.env.UBERSUGGEST_API_KEY) {
            dataSources.push({
                id: 'ubersuggest',
                name: 'Ubersuggest (Direct API)',
                description: 'Direct Ubersuggest API access',
                enabled: true,
                recommended: false
            });
        }
        // Always include AI generated as fallback
        dataSources.push({
            id: 'ai_generated',
            name: 'AI Generated (Free)',
            description: 'AI-powered keyword suggestions',
            enabled: true,
            recommended: false
        });
        const recommendedSources = dataSources.filter(ds => ds.recommended).map(ds => ds.id);
        const defaultSelection = recommendedSources.length > 0 ? recommendedSources.concat(['ai_generated']) : ['ai_generated'];
        res.json({
            data_sources: dataSources,
            default_selection: defaultSelection
        });
    }
    catch (error) {
        console.error('Failed to get data sources:', error);
        res.status(500).json({
            error: 'Failed to get data sources',
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
// Health check for LangGraph workflows
router.get('/health', async (req, res) => {
    try {
        const config = PSEOWorkflow_1.PSEOWorkflow.getDefaultConfig();
        const validation = PSEOWorkflow_1.PSEOWorkflow.validateConfig(config);
        res.status(200).json({
            status: 'ok',
            langgraph: 'healthy',
            configuration: {
                valid: validation.valid,
                issues: validation.issues,
                available_tools: {
                    openai: !!config.openai_api_key,
                    semrush: !!config.semrush_api_key,
                    ahrefs: !!config.ahrefs_api_key,
                    ubersuggest: !!config.ubersuggest_api_key
                }
            },
            workflow_stats: stateManager.getWorkflowStats(),
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('LangGraph health check error:', error);
        res.status(500).json({
            error: 'Health check failed',
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
// Test RapidAPI endpoints (Ubersuggest + SimilarWeb)
router.post('/test-apis', async (req, res) => {
    try {
        const { testKeyword = 'digital marketing', testDomain = 'example.com' } = req.body;
        const results = {
            timestamp: new Date().toISOString(),
            tests: []
        };
        // Test Ubersuggest API
        try {
            console.log(`🧪 Testing Ubersuggest API with keyword: ${testKeyword}`);
            const rapidApiTool = new RapidAPITool_1.RapidAPITool({
                apiKey: process.env.RAPIDAPI_KEY || '',
                enabled: true,
                rateLimit: 10,
                timeout: 30000,
                retries: 2
            });
            const keywordData = await rapidApiTool.getKeywordData([testKeyword], { country: 'us' });
            results.tests.push({
                api: 'Ubersuggest',
                endpoint: 'keyword-research',
                test_input: testKeyword,
                status: 'success',
                response_size: keywordData.length,
                sample_data: keywordData.slice(0, 2),
                message: `Successfully retrieved ${keywordData.length} keyword results`
            });
        }
        catch (ubersuggestError) {
            results.tests.push({
                api: 'Ubersuggest',
                endpoint: 'keyword-research',
                test_input: testKeyword,
                status: 'failed',
                error: ubersuggestError instanceof Error ? ubersuggestError.message : 'Unknown error',
                message: 'Ubersuggest API test failed'
            });
        }
        // Test SimilarWeb API
        try {
            console.log(`🧪 Testing SimilarWeb API with domain: ${testDomain}`);
            const rapidApiTool = new RapidAPITool_1.RapidAPITool({
                apiKey: process.env.RAPIDAPI_KEY || '',
                enabled: true,
                rateLimit: 10,
                timeout: 30000,
                retries: 2
            });
            const domainData = await rapidApiTool.getDomainData(testDomain);
            results.tests.push({
                api: 'SimilarWeb',
                endpoint: 'traffic',
                test_input: testDomain,
                status: 'success',
                response_data: domainData,
                message: `Successfully retrieved domain data for ${testDomain}`
            });
        }
        catch (similarwebError) {
            results.tests.push({
                api: 'SimilarWeb',
                endpoint: 'traffic',
                test_input: testDomain,
                status: 'failed',
                error: similarwebError instanceof Error ? similarwebError.message : 'Unknown error',
                message: 'SimilarWeb API test failed'
            });
        }
        // Summary
        const successfulTests = results.tests.filter((t) => t.status === 'success').length;
        const totalTests = results.tests.length;
        results.summary = {
            total_tests: totalTests,
            successful_tests: successfulTests,
            success_rate: `${Math.round((successfulTests / totalTests) * 100)}%`,
            overall_status: successfulTests === totalTests ? 'all_passed' : successfulTests > 0 ? 'partial_success' : 'all_failed'
        };
        res.status(200).json({
            success: true,
            message: `API tests completed: ${successfulTests}/${totalTests} passed`,
            data: results
        });
    }
    catch (error) {
        console.error('API test error:', error);
        res.status(500).json({
            error: 'API test failed',
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
// Start keyword research workflow
router.post('/keyword-research', async (req, res) => {
    try {
        const userId = req.headers['x-user-id'];
        const { website_id, seed_keywords = [], research_method, topic_input, competitor_domains = [], max_keywords = 100, data_sources = process.env.RAPIDAPI_KEY ? ['rapidapi'] : ['semrush', 'ubersuggest'] } = req.body;
        // Validate required fields
        if (!userId) {
            return res.status(400).json({
                error: 'Missing user ID',
                message: 'x-user-id header is required'
            });
        }
        if (!website_id) {
            return res.status(400).json({
                error: 'Missing required field',
                message: 'website_id is required'
            });
        }
        if (!research_method) {
            return res.status(400).json({
                error: 'Missing required field',
                message: 'research_method is required'
            });
        }
        if (research_method === 'website' && !req.body.domain) {
            return res.status(400).json({
                error: 'Missing required field',
                message: 'domain is required for website research method'
            });
        }
        if (research_method === 'topic' && !topic_input) {
            return res.status(400).json({
                error: 'Missing required field',
                message: 'topic_input is required for topic research method'
            });
        }
        console.log(`🚀 Starting LangGraph keyword research workflow for user: ${userId}`);
        // Create workflow instance
        const workflow = await PSEOWorkflow_1.PSEOWorkflow.create();
        // Prepare workflow input
        const workflowInput = {
            user_id: userId,
            website_id,
            domain: req.body.domain || '',
            seed_keywords,
            research_method,
            topic_input,
            competitor_domains,
            max_keywords,
            data_sources
        };
        // Execute workflow asynchronously and handle errors
        let initialState;
        try {
            console.log(`🔄 Executing workflow with input:`, workflowInput);
            const workflowPromise = workflow.execute(workflowInput);
            initialState = await workflowPromise;
            console.log(`✅ Workflow created with ID: ${initialState.workflow_id}, status: ${initialState.status}`);
            // Verify the workflow is stored in StateManager
            const storedState = await stateManager.getWorkflowState(initialState.workflow_id);
            if (storedState) {
                console.log(`✅ Workflow state confirmed in StateManager: ${initialState.workflow_id}, status: ${storedState.status}`);
            }
            else {
                console.log(`❌ WARNING: Workflow state NOT found in StateManager: ${initialState.workflow_id}`);
                console.log(`📊 Available workflows in StateManager:`, stateManager.getAllWorkflows().map(w => `${w.workflowId}:${w.state.status}`));
            }
        }
        catch (workflowError) {
            // If workflow execution fails, return the error to frontend
            console.error('Workflow execution failed:', workflowError);
            return res.status(400).json({
                error: 'Workflow execution failed',
                message: workflowError instanceof Error ? workflowError.message : 'Unknown workflow error',
                details: 'The keyword research workflow failed to execute. Please check your configuration and try again.'
            });
        }
        res.status(200).json({
            success: true,
            workflow_id: initialState.workflow_id,
            status: initialState.status,
            message: 'Keyword research workflow started successfully',
            data: {
                workflow_id: initialState.workflow_id,
                status: initialState.status,
                progress: initialState.progress,
                current_step: initialState.current_step,
                started_at: initialState.started_at,
                estimated_completion: new Date(Date.now() + 60000).toISOString() // Estimate 1 minute
            }
        });
    }
    catch (error) {
        console.error('Keyword research workflow error:', error);
        res.status(500).json({
            error: 'Workflow execution failed',
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
// Get workflow status
router.get('/workflow/:workflowId/status', async (req, res) => {
    try {
        const { workflowId } = req.params;
        const userId = req.headers['x-user-id'];
        if (!userId) {
            return res.status(400).json({
                error: 'Missing user ID',
                message: 'x-user-id header is required'
            });
        }
        const status = await stateManager.getWorkflowStatus(workflowId);
        if (!status || status.status === 'not_found') {
            return res.status(404).json({
                error: 'Workflow not found',
                message: `Workflow ${workflowId} not found`
            });
        }
        res.status(200).json({
            success: true,
            data: status
        });
    }
    catch (error) {
        console.error('Get workflow status error:', error);
        res.status(500).json({
            error: 'Failed to get workflow status',
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
// Get workflow results
router.get('/workflow/:workflowId/results', async (req, res) => {
    try {
        const { workflowId } = req.params;
        const userId = req.headers['x-user-id'];
        if (!userId) {
            return res.status(400).json({
                error: 'Missing user ID',
                message: 'x-user-id header is required'
            });
        }
        console.log(`🔍 Looking for workflow state: ${workflowId}`);
        const workflowState = await stateManager.getWorkflowState(workflowId);
        if (!workflowState) {
            console.log(`❌ Workflow state not found for: ${workflowId}`);
            console.log(`📊 Active workflows:`, stateManager.getActiveWorkflows().map(w => w.workflowId));
            console.log(`📊 All workflows:`, stateManager.getAllWorkflows().map(w => `${w.workflowId}:${w.state.status}`));
            return res.status(404).json({
                error: 'Workflow not found',
                message: `Workflow ${workflowId} not found`
            });
        }
        console.log(`✅ Found workflow state: ${workflowId}, status: ${workflowState.status}`);
        // Check if workflow belongs to the user
        if (workflowState.user_id !== userId) {
            return res.status(403).json({
                error: 'Access denied',
                message: 'You do not have access to this workflow'
            });
        }
        res.status(200).json({
            success: true,
            data: {
                workflow_id: workflowState.workflow_id,
                status: workflowState.status,
                progress: workflowState.progress,
                keywords: workflowState.keywords,
                keyword_clusters: workflowState.keyword_clusters,
                competitor_data: workflowState.competitor_data,
                content_suggestions: workflowState.content_suggestions,
                processing_time: workflowState.processing_time,
                api_calls_made: workflowState.api_calls_made,
                total_cost: workflowState.total_cost,
                data_sources_used: workflowState.data_sources_used,
                started_at: workflowState.started_at,
                completed_at: workflowState.completed_at,
                errors: workflowState.errors
            }
        });
    }
    catch (error) {
        console.error('Get workflow results error:', error);
        res.status(500).json({
            error: 'Failed to get workflow results',
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
// Cancel workflow
router.post('/workflow/:workflowId/cancel', async (req, res) => {
    try {
        const { workflowId } = req.params;
        const userId = req.headers['x-user-id'];
        if (!userId) {
            return res.status(400).json({
                error: 'Missing user ID',
                message: 'x-user-id header is required'
            });
        }
        const workflowState = await stateManager.getWorkflowState(workflowId);
        if (!workflowState) {
            return res.status(404).json({
                error: 'Workflow not found',
                message: `Workflow ${workflowId} not found`
            });
        }
        // Check if workflow belongs to the user
        if (workflowState.user_id !== userId) {
            return res.status(403).json({
                error: 'Access denied',
                message: 'You do not have access to this workflow'
            });
        }
        const cancelled = await stateManager.cancelWorkflow(workflowId);
        if (cancelled) {
            res.status(200).json({
                success: true,
                message: 'Workflow cancelled successfully'
            });
        }
        else {
            res.status(400).json({
                error: 'Cannot cancel workflow',
                message: 'Workflow is not in a cancellable state'
            });
        }
    }
    catch (error) {
        console.error('Cancel workflow error:', error);
        res.status(500).json({
            error: 'Failed to cancel workflow',
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
// Get active workflows for user
router.get('/workflows/active', async (req, res) => {
    try {
        const userId = req.headers['x-user-id'];
        if (!userId) {
            return res.status(400).json({
                error: 'Missing user ID',
                message: 'x-user-id header is required'
            });
        }
        const activeWorkflows = stateManager.getActiveWorkflows()
            .filter(({ state }) => state.user_id === userId)
            .map(({ workflowId, state }) => ({
            workflow_id: workflowId,
            status: state.status,
            progress: state.progress,
            current_step: state.current_step,
            started_at: state.started_at,
            research_method: state.research_method,
            domain: state.domain,
            topic_input: state.topic_input
        }));
        res.status(200).json({
            success: true,
            data: activeWorkflows
        });
    }
    catch (error) {
        console.error('Get active workflows error:', error);
        res.status(500).json({
            error: 'Failed to get active workflows',
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
// Get existing keywords for a website
router.get('/keywords/:websiteId', async (req, res) => {
    console.log('🔍 GET /keywords/:websiteId route hit!', {
        websiteId: req.params.websiteId,
        userId: req.headers['x-user-id'],
        url: req.url,
        method: req.method
    });
    try {
        const { websiteId } = req.params;
        const userId = req.headers['x-user-id'];
        if (!userId) {
            return res.status(400).json({
                error: 'Missing user ID',
                message: 'x-user-id header is required'
            });
        }
        if (!websiteId) {
            return res.status(400).json({
                error: 'Missing website ID',
                message: 'Website ID is required'
            });
        }
        // Query existing keywords from pseo_keywords table using existing client
        const { supabase } = await Promise.resolve().then(() => __importStar(require('../../../base/common/apps/supabase')));
        const { data: keywords, error } = await supabase
            .from('pseo_keywords')
            .select('*')
            .eq('website_id', websiteId)
            .order('created_at', { ascending: false })
            .limit(100); // Limit to recent 100 keywords
        if (error) {
            console.error('Database query error:', error);
            return res.status(500).json({
                error: 'Database query failed',
                message: error.message
            });
        }
        // Group keywords by data source
        const keywordsBySource = {};
        let clusters = [];
        keywords?.forEach((keyword) => {
            const source = keyword.data_source || 'unknown';
            if (!keywordsBySource[source]) {
                keywordsBySource[source] = [];
            }
            keywordsBySource[source].push({
                keyword: keyword.keyword,
                search_volume: keyword.search_volume,
                keyword_difficulty: keyword.keyword_difficulty,
                cpc: keyword.cpc,
                competition: keyword.competition,
                intent: keyword.intent,
                data_source: keyword.data_source,
                created_at: keyword.created_at
            });
            // Extract clusters from the first keyword that has them
            if (keyword.clusters && Array.isArray(keyword.clusters) && keyword.clusters.length > 0 && clusters.length === 0) {
                clusters = keyword.clusters;
            }
        });
        res.status(200).json({
            success: true,
            data: {
                website_id: websiteId,
                total_keywords: keywords?.length || 0,
                keywords_by_source: keywordsBySource,
                keyword_clusters: clusters,
                total_clusters: clusters.length,
                keywords: keywords || []
            }
        });
    }
    catch (error) {
        console.error('Get existing keywords error:', error);
        res.status(500).json({
            error: 'Failed to get existing keywords',
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
// Server-Sent Events endpoint for real-time workflow updates
router.get('/workflow/:workflowId/stream', async (req, res) => {
    try {
        const { workflowId } = req.params;
        const userId = req.query.userId || req.headers['x-user-id'];
        if (!userId) {
            return res.status(400).json({
                error: 'Missing user ID',
                message: 'userId query parameter or x-user-id header is required'
            });
        }
        // Set up SSE headers
        res.writeHead(200, {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Cache-Control'
        });
        // Send initial connection confirmation
        res.write(`data: ${JSON.stringify({ type: 'connected', workflowId })}\n\n`);
        // Set up workflow status listener
        const statusInterval = setInterval(async () => {
            try {
                console.log(`🔍 SSE: Looking for workflow state: ${workflowId}`);
                const workflowState = await stateManager.getWorkflowState(workflowId);
                if (!workflowState) {
                    console.log(`❌ SSE: Workflow state not found for: ${workflowId}`);
                    console.log(`📊 SSE: Active workflows:`, stateManager.getActiveWorkflows().map(w => w.workflowId));
                    console.log(`📊 SSE: All workflows:`, stateManager.getAllWorkflows().map(w => `${w.workflowId}:${w.state.status}`));
                    res.write(`data: ${JSON.stringify({
                        type: 'error',
                        message: 'Workflow not found'
                    })}\n\n`);
                    clearInterval(statusInterval);
                    res.end();
                    return;
                }
                console.log(`✅ SSE: Found workflow state: ${workflowId}, status: ${workflowState.status}`);
                // Check if workflow belongs to the user
                if (workflowState.user_id !== userId) {
                    res.write(`data: ${JSON.stringify({
                        type: 'error',
                        message: 'Access denied'
                    })}\n\n`);
                    clearInterval(statusInterval);
                    res.end();
                    return;
                }
                // Send status update
                const statusUpdate = {
                    type: 'status',
                    data: {
                        workflow_id: workflowState.workflow_id,
                        status: workflowState.status,
                        progress: workflowState.progress,
                        current_step: workflowState.current_step,
                        keywords_found: workflowState.keywords?.length || 0,
                        processing_time: workflowState.processing_time,
                        last_updated: workflowState.last_updated
                    }
                };
                res.write(`data: ${JSON.stringify(statusUpdate)}\n\n`);
                // If workflow is completed or failed, send final results and close
                if (workflowState.status === 'completed' || workflowState.status === 'failed') {
                    const finalUpdate = {
                        type: 'completed',
                        data: {
                            workflow_id: workflowState.workflow_id,
                            status: workflowState.status,
                            keywords: workflowState.keywords,
                            keyword_clusters: workflowState.keyword_clusters,
                            processing_time: workflowState.processing_time,
                            api_calls_made: workflowState.api_calls_made,
                            total_cost: workflowState.total_cost,
                            data_sources_used: workflowState.data_sources_used,
                            errors: workflowState.errors
                        }
                    };
                    res.write(`data: ${JSON.stringify(finalUpdate)}\n\n`);
                    clearInterval(statusInterval);
                    res.end();
                }
            }
            catch (error) {
                console.error('SSE status check error:', error);
                res.write(`data: ${JSON.stringify({
                    type: 'error',
                    message: 'Status check failed'
                })}\n\n`);
            }
        }, 2000); // Check every 2 seconds
        // Clean up on client disconnect
        req.on('close', () => {
            clearInterval(statusInterval);
            res.end();
        });
    }
    catch (error) {
        console.error('SSE setup error:', error);
        res.status(500).json({
            error: 'Failed to set up workflow stream',
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
// Get workflow execution history
router.get('/workflow/:workflowId/history', async (req, res) => {
    try {
        const { workflowId } = req.params;
        const userId = req.headers['x-user-id'];
        if (!userId) {
            return res.status(400).json({
                error: 'Missing user ID',
                message: 'x-user-id header is required'
            });
        }
        const workflowState = await stateManager.getWorkflowState(workflowId);
        if (!workflowState || workflowState.user_id !== userId) {
            return res.status(404).json({
                error: 'Workflow not found',
                message: `Workflow ${workflowId} not found or access denied`
            });
        }
        const executionHistory = stateManager.getExecutionHistory(workflowId);
        const stateSnapshots = stateManager.getStateSnapshots(workflowId);
        res.status(200).json({
            success: true,
            data: {
                workflow_id: workflowId,
                execution_history: executionHistory,
                state_snapshots: stateSnapshots.map(snapshot => ({
                    timestamp: snapshot.last_updated,
                    step: snapshot.current_step,
                    progress: snapshot.progress,
                    status: snapshot.status
                }))
            }
        });
    }
    catch (error) {
        console.error('Get workflow history error:', error);
        res.status(500).json({
            error: 'Failed to get workflow history',
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.default = router;
//# sourceMappingURL=langgraph.js.map