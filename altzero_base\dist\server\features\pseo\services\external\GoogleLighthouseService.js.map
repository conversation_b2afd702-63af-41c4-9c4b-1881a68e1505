{"version": 3, "file": "GoogleLighthouseService.js", "sourceRoot": "", "sources": ["../../../../../../server/features/pseo/services/external/GoogleLighthouseService.ts"], "names": [], "mappings": ";;;AAAA,+DAAgI;AAEhI,MAAa,uBAAwB,SAAQ,yCAAmB;IAG9D,YAAY,MAA6B;QACvC,KAAK,CAAC,mBAAmB,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QAH5B,YAAO,GAAG,4DAA4D,CAAC;IAIxF,CAAC;IAED,YAAY;QACV,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,qBAAqB,CAAC;YACtC,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC;gBACjC,GAAG,EAAE,OAAO;gBACZ,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,MAAO;gBACxB,QAAQ,EAAE,KAAK;aAChB,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,IAAI,MAAM,EAAE,CAAC,CAAC;YAC1D,OAAO,QAAQ,CAAC,EAAE,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,GAAW;QACvB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC,iBAAiB,CAAC,wDAAwD,CAAC,CAAC;QAC1F,CAAC;QAED,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;YAE7B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;gBAC7C,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC;oBACjC,GAAG,EAAE,GAAG;oBACR,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,MAAO;oBACxB,QAAQ,EAAE,SAAS;iBACpB,CAAC,CAAC;gBAEH,iBAAiB;gBACjB,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;gBACzC,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,eAAe,CAAC,CAAC;gBAC3C,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;gBAC5C,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;gBACjC,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;gBAEjC,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,IAAI,MAAM,EAAE,EAAE;oBACxD,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,KAAK,CAAC;iBAC1D,CAAC,CAAC;gBAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;oBACjB,MAAM,IAAI,KAAK,CAAC,0BAA0B,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;gBACnE,CAAC;gBAED,OAAO,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC/B,CAAC,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAEpC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,iBAAiB,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;QAC1F,CAAC;IACH,CAAC;IAES,gBAAgB;QACxB,OAAO,KAAK,CAAC,CAAC,0BAA0B;IAC1C,CAAC;IAES,cAAc;QACtB,OAAO,CAAC,CAAC,CAAC,eAAe;IAC3B,CAAC;IAEO,aAAa,CAAC,IAAS;QAC7B,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,EAAE,UAAU,IAAI,EAAE,CAAC;QAC3D,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,EAAE,MAAM,IAAI,EAAE,CAAC;QAEnD,MAAM,OAAO,GAAe;YAC1B,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;YACvD,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;YACvE,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;YACvD,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;YACnE,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,aAAa,EAAE,KAAK,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;YACvE,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;YACnD,OAAO,EAAE,IAAI,CAAC,gBAAgB;SAC/B,CAAC;QAEF,MAAM,MAAM,GAAe,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAEtD,OAAO;YACL,QAAQ,EAAE,IAAI,CAAC,IAAI;YACnB,OAAO;YACP,MAAM;YACN,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,OAAO,EAAE,IAAI;SACd,CAAC;IACJ,CAAC;IAEO,aAAa,CAAC,MAAW;QAC/B,MAAM,MAAM,GAAe,EAAE,CAAC;QAE9B,MAAM,SAAS,GAAG;YAChB,EAAE,EAAE,EAAE,gBAAgB,EAAE,QAAQ,EAAE,WAAW,EAAE;YAC/C,EAAE,EAAE,EAAE,kBAAkB,EAAE,QAAQ,EAAE,WAAW,EAAE;YACjD,EAAE,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE;YACtC,EAAE,EAAE,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE;YACvC,EAAE,EAAE,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE;YACtC,EAAE,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,sBAAsB,EAAE;YACpD,EAAE,EAAE,EAAE,WAAW,EAAE,QAAQ,EAAE,eAAe,EAAE;YAC9C,EAAE,EAAE,EAAE,YAAY,EAAE,QAAQ,EAAE,eAAe,EAAE;YAC/C,EAAE,EAAE,EAAE,iBAAiB,EAAE,QAAQ,EAAE,eAAe,EAAE;SACrD,CAAC;QAEF,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE;YACrC,MAAM,KAAK,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;YACzB,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,KAAK,IAAI,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;gBACrD,MAAM,CAAC,IAAI,CAAC;oBACV,QAAQ;oBACR,QAAQ,EAAE,KAAK,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS;oBACpD,KAAK,EAAE,KAAK,CAAC,KAAK,IAAI,EAAE;oBACxB,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,0BAA0B;oBAC5D,cAAc,EAAE,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,KAAK,CAAC;oBACjD,MAAM,EAAE,KAAK,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ;iBAC9C,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,iBAAiB,CAAC,OAAe,EAAE,KAAU;QACnD,MAAM,eAAe,GAA8B;YACjD,gBAAgB,EAAE,gDAAgD;YAClE,kBAAkB,EAAE,6CAA6C;YACjE,UAAU,EAAE,0EAA0E;YACtF,WAAW,EAAE,wCAAwC;YACrD,WAAW,EAAE,kEAAkE;YAC/E,UAAU,EAAE,6CAA6C;YACzD,WAAW,EAAE,iDAAiD;YAC9D,YAAY,EAAE,+BAA+B;YAC7C,iBAAiB,EAAE,sDAAsD;SAC1E,CAAC;QAEF,OAAO,eAAe,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,WAAW,IAAI,oBAAoB,CAAC;IAC/E,CAAC;CACF;AApJD,0DAoJC"}