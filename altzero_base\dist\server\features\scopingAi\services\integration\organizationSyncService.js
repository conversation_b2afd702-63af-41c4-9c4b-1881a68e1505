"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.organizationSyncService = exports.OrganizationSyncService = void 0;
const supabase_1 = require("../../../../base/common/apps/supabase");
class OrganizationSyncService {
    /**
     * Get user's primary organization
     */
    async getUserOrganization(userId) {
        try {
            console.log(`🔍 Getting organization for user: ${userId}`);
            const { data: membership, error } = await supabase_1.supabase
                .from("organisation_members")
                .select(`
          organisation_id,
          organisations (
            id,
            name
          )
        `)
                .eq("user_id", userId)
                .limit(1)
                .single();
            if (error || !membership) {
                console.log(`❌ User ${userId} is not a member of any organization`);
                return null;
            }
            console.log(`✅ User ${userId} belongs to organization: ${membership.organisation_id}`);
            return membership.organisation_id;
        }
        catch (error) {
            console.error("Error getting user organization:", error);
            return null;
        }
    }
    /**
     * Get detailed user organization information
     */
    async getUserOrganizationInfo(userId) {
        try {
            const { data: membership, error } = await supabase_1.supabase
                .from("organisation_members")
                .select(`
          organisation_id,
          role,
          created_at,
          organisations (
            id,
            name
          )
        `)
                .eq("user_id", userId)
                .limit(1)
                .single();
            if (error || !membership) {
                return null;
            }
            return {
                userId,
                organizationId: membership.organisation_id,
                organizationName: membership.organisations?.name || "Unknown",
                role: membership.role || "member",
                joinedAt: membership.created_at,
            };
        }
        catch (error) {
            console.error("Error getting user organization info:", error);
            return null;
        }
    }
    /**
     * Get organization statistics
     */
    async getOrganizationStats(organizationId) {
        try {
            console.log(`📊 Getting stats for organization: ${organizationId}`);
            // Get organization basic info
            const { data: org, error: orgError } = await supabase_1.supabase
                .from("organisations")
                .select("id, name")
                .eq("id", organizationId)
                .single();
            if (orgError || !org) {
                throw new Error("Organization not found");
            }
            // Get member count
            const { count: memberCount, error: memberError } = await supabase_1.supabase
                .from("organisation_members")
                .select("*", { count: "exact", head: true })
                .eq("organisation_id", organizationId);
            if (memberError) {
                console.error("Error counting members:", memberError);
            }
            // Get CRM contact count
            const { count: crmContactCount, error: crmError } = await supabase_1.supabase
                .from("crm_contacts")
                .select("*", { count: "exact", head: true })
                .eq("organisation_id", organizationId);
            if (crmError) {
                console.error("Error counting CRM contacts:", crmError);
            }
            // Get ScopingAI client count for organization members
            const { data: orgMembers, error: membersError } = await supabase_1.supabase
                .from("organisation_members")
                .select("user_id")
                .eq("organisation_id", organizationId);
            let scopingaiClientCount = 0;
            if (!membersError && orgMembers) {
                const memberUserIds = orgMembers.map((m) => m.user_id);
                const { count: clientCount, error: clientError } = await supabase_1.supabase
                    .from("scopingai_clients")
                    .select("*", { count: "exact", head: true })
                    .in("user_id", memberUserIds);
                if (!clientError) {
                    scopingaiClientCount = clientCount || 0;
                }
            }
            const stats = {
                id: org.id,
                name: org.name,
                memberCount: memberCount || 0,
                crmContactCount: crmContactCount || 0,
                scopingaiClientCount,
            };
            console.log(`✅ Organization stats:`, stats);
            return stats;
        }
        catch (error) {
            console.error("Error getting organization stats:", error);
            return null;
        }
    }
    /**
     * Auto-link all CRM contacts in organization for ScopingAI usage
     */
    async autoLinkCrmContacts(organizationId, userId) {
        try {
            console.log(`🔗 Auto-linking CRM contacts for organization: ${organizationId}`);
            // Verify user is member of organization
            const { data: membership, error: membershipError } = await supabase_1.supabase
                .from("organisation_members")
                .select("id")
                .eq("organisation_id", organizationId)
                .eq("user_id", userId)
                .single();
            if (membershipError || !membership) {
                throw new Error("User is not a member of this organization");
            }
            // Get all CRM contacts for the organization
            const { data: crmContacts, error: contactsError } = await supabase_1.supabase
                .from("crm_contacts")
                .select("id")
                .eq("organisation_id", organizationId);
            if (contactsError) {
                throw contactsError;
            }
            if (!crmContacts || crmContacts.length === 0) {
                console.log("No CRM contacts found to link");
                return 0;
            }
            // CRM contacts are now directly available without linking
            console.log(`✅ All ${crmContacts.length} CRM contacts are automatically available for ScopingAI`);
            return crmContacts.length;
        }
        catch (error) {
            console.error("Error in autoLinkCrmContacts:", error);
            throw error;
        }
    }
    /**
     * Sync organization data - ensure all contacts are properly linked
     */
    async syncOrganizationData(organizationId, userId) {
        try {
            console.log(`🔄 Syncing organization data for: ${organizationId}`);
            // Auto-link CRM contacts
            const linkedCount = await this.autoLinkCrmContacts(organizationId, userId);
            // Get updated stats
            const statsAfter = await this.getOrganizationStats(organizationId);
            const result = {
                crmContactsLinked: linkedCount,
                totalCrmContacts: statsAfter?.crmContactCount || 0,
                totalScopingaiClients: statsAfter?.scopingaiClientCount || 0,
            };
            console.log(`✅ Organization sync completed:`, result);
            return result;
        }
        catch (error) {
            console.error("Error syncing organization data:", error);
            throw error;
        }
    }
    /**
     * Get all organizations with their stats
     */
    async getAllOrganizationsStats() {
        try {
            const { data: organizations, error } = await supabase_1.supabase
                .from("organisations")
                .select("id, name")
                .order("name");
            if (error) {
                throw error;
            }
            const stats = [];
            for (const org of organizations || []) {
                const orgStats = await this.getOrganizationStats(org.id);
                if (orgStats) {
                    stats.push(orgStats);
                }
            }
            return stats;
        }
        catch (error) {
            console.error("Error getting all organizations stats:", error);
            return [];
        }
    }
    /**
     * Cleanup inactive links
     */
    async cleanupInactiveLinks(organizationId) {
        try {
            console.log(`✅ No cleanup needed - CRM contacts are directly available for organization: ${organizationId}`);
            return 0;
        }
        catch (error) {
            console.error("Error in cleanupInactiveLinks:", error);
            throw error;
        }
    }
}
exports.OrganizationSyncService = OrganizationSyncService;
exports.organizationSyncService = new OrganizationSyncService();
//# sourceMappingURL=organizationSyncService.js.map