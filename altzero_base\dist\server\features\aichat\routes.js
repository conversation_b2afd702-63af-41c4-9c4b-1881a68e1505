"use strict";
// Main routes export for AI Chat plugin
// This file is required by the plugin loader for backward compatibility
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const aichat_1 = __importDefault(require("./routes/aichat"));
exports.default = aichat_1.default;
//# sourceMappingURL=routes.js.map